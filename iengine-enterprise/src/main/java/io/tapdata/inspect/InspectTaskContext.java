package io.tapdata.inspect;

import com.tapdata.entity.Connections;
import com.tapdata.entity.inspect.InspectTask;
import com.tapdata.mongo.ClientMongoOperator;
import io.tapdata.pdk.core.api.ConnectorNode;

/**
 * <AUTHOR>
 * @Description
 * @create 2022-06-06 11:49
 **/
public class InspectTaskContext extends InspectContext {

	public InspectTaskContext(
			String name,
			String flowId,
			InspectTask task,
			Connections source,
			Connections target,
			String inspectResultParentId,
			String inspectDifferenceMode,
            Boolean enableRecovery,
			ProgressUpdate progressUpdateCallback,
			ConnectorNode sourceConnectorNode,
			ConnectorNode targetConnectorNode,
			ClientMongoOperator clientMongoOperator,
			InspectService inspectService
	) {
		super(name, flowId, task, source, target, inspectResultParentId, inspectDifferenceMode, enableRecovery, progressUpdateCallback, sourceConnectorNode, targetConnectorNode, clientMongoOperator,inspectService);
	}
}
