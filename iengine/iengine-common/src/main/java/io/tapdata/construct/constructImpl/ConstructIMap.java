package io.tapdata.construct.constructImpl;

import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.map.IMap;
import com.hazelcast.persistence.ConstructType;
import com.hazelcast.persistence.PersistenceStorage;
import com.tapdata.tm.commons.externalStorage.ExternalStorageDto;
import io.tapdata.entity.utils.InstanceFactory;
import io.tapdata.entity.utils.ObjectSerializable;
import io.tapdata.flow.engine.V2.util.ExternalStorageUtil;
import org.bson.Document;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @Description
 * @create 2021-11-23 17:09
 **/
public class ConstructIMap<T> extends BaseConstruct<T> {

	protected IMap<String, Object> iMap;

	public ConstructIMap(HazelcastInstance hazelcastInstance, String name) {
		super(name);
		this.iMap = hazelcastInstance.getMap(name);
	}

	public ConstructIMap(HazelcastInstance hazelcastInstance, String referenceId, String name) {
		super(referenceId, name);
		this.iMap = hazelcastInstance.getMap(name);
	}

	public ConstructIMap(HazelcastInstance hazelcastInstance, String referenceId, String name, ExternalStorageDto externalStorageDto) {
		super(referenceId, name, externalStorageDto);
		ExternalStorageUtil.initHZMapStorage(externalStorageDto, referenceId, name, hazelcastInstance.getConfig());
		this.iMap = hazelcastInstance.getMap(name);
		Integer ttlDay = externalStorageDto.getTtlDay();
		if (ttlDay != null && ttlDay > 0) {
			convertTtlDay2Second(ttlDay);
			PersistenceStorage.getInstance().setImapTTL(this.iMap, this.ttlSecond);
		}
	}

	@Override
	public int insert(String key, T data) throws Exception {
		iMap.put(key, data);
		return 1;
	}

	@Override
	public long insertMany(Map<String, T> data) throws Exception {
		iMap.putAll(data);
		return data.size();
	}

	@Override
	public int update(String key, T data) throws Exception {
		return insert(key, data);
	}

	@Override
	public int upsert(String key, T data) throws Exception {
		return insert(key, data);
	}

	@Override
	public int delete(String key) throws Exception {
		int delete = 0;

		iMap.remove(key);
		delete++;

		return delete;
	}

	@Override
	public T find(String key) throws Exception {
		Object obj = iMap.get(key);
		if(obj == null)
			return null;
		obj = handleObjectWhenDiffClassLoader(obj);
		return (T) obj;
	}

	@Override
	public Map<String, Object> findAll(Set<String> keys) {
		Map<String, Object> getMap = iMap.getAll(keys);
		Map<String, Object> result = new HashMap<>();
		Iterator<Map.Entry<String, Object>> iterator = getMap.entrySet().iterator();
		while (iterator.hasNext()) {
			Map.Entry<String, Object> entry = iterator.next();
			Object obj = entry.getValue();
			if (obj == null)
				continue;
			obj = handleObjectWhenDiffClassLoader(obj);
			result.put(entry.getKey(), obj);
		}
		return result;
	}

	private Object handleObjectWhenDiffClassLoader(Object obj) {
		if (!obj.getClass().getClassLoader().equals(this.getClass().getClassLoader())) {
			if (obj.getClass().getName().equals(Document.class.getName())) {
				ObjectSerializable serializable = InstanceFactory.instance(ObjectSerializable.class);
				byte[] bytes = serializable.fromObject(obj);
				obj = serializable.toObject(bytes);
			}
		}
		return obj;
	}

	@Override
	public boolean exists(String key) throws Exception {
		return iMap.containsKey(key);
	}

	@Override
	public void clear() throws Exception {
		PersistenceStorage.getInstance().clear(ConstructType.IMAP, name);
	}

	@Override
	public boolean isEmpty() {
		return PersistenceStorage.getInstance().isEmpty(ConstructType.IMAP, name);
	}

	@Override
	public String getName() {
		return iMap.getName();
	}

	@Override
	public String getType() {
		return "IMap";
	}

	public IMap<String, Object> getiMap() {
		return iMap;
	}

	@Override
	public void destroy() throws Exception {
		if (PersistenceStorage.getInstance().destroy(referenceId, ConstructType.IMAP, name) && null != iMap) {
			iMap.destroy();
		}
	}
}
