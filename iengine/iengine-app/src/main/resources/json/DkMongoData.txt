{"schema": [{"colName": "collection", "count": 2653, "size": 2575941, "storageSize": 782336, "totalIndexSize": 32768, "indexSizes": {"_id_": 32768}, "avgObjSize": 970, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "67b3a5f5-efe5-4fad-bb99-228f9c4b65f2"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "BSON", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "String", "k48": "String", "k49": "String", "k50": "String"}}, {"colName": "collection", "count": 224, "size": 83094, "storageSize": 53248, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 370, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "8e1e9aaa-4458-4514-922a-b555ceafb765"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "String", "k2": "String", "k3": "BSON", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "Number", "k16": "String", "k17": "String"}}, {"colName": "collection", "count": 1500000, "size": 591790592, "storageSize": 148934656, "totalIndexSize": 15052800, "indexSizes": {"_id_": 15052800}, "avgObjSize": 394, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "c79241f7-12ed-45be-83a4-f7d3aa2ed55c"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "String", "k2": "Number", "k3": "String", "k4": "String", "k5": "Number", "k6": "String", "k7": "Number", "k8": "Number", "k9": "Number", "k10": "Number", "k11": "String", "k12": "Number", "k13": "Number", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "Number", "k20": "NumberLong", "k21": "Number", "k22": "String", "k23": "BSON"}}, {"colName": "collection", "count": 0, "size": 0, "storageSize": 831488, "totalIndexSize": 143360, "indexSizes": {"_id_": 143360}, "avgObjSize": 0, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "9f44bd1b-c214-4af2-8c53-66cb14d812e7"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {}}, {"colName": "collection", "count": 7974, "size": 1065764, "storageSize": 331776, "totalIndexSize": 131072, "indexSizes": {"_id_": 131072}, "avgObjSize": 133, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "de0e657f-b66a-46a8-96b3-fff8b61bd8f9"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "NumberLong", "k2": "Number", "k3": "Number", "k4": "Number", "k5": "BSON", "k6": "Number", "k7": "Number"}}, {"colName": "collection", "count": 4807051, "size": 1988620660, "storageSize": 763805696, "totalIndexSize": 51068928, "indexSizes": {"_id_": 51068928}, "avgObjSize": 413, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "2e78a791-ed8b-4ead-82db-717671d09e38"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "String", "k2": "String", "k3": "String", "k4": "Number", "k5": "String", "k6": "Number", "k7": "Number", "k8": "Date", "k9": "BSON", "k10": "BSON", "k11": "Array", "k12": "BSON"}}, {"colName": "collection", "count": 2, "size": 1024, "storageSize": 16384, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 512, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "cbd85f8a-6088-4ba6-89af-0c66209875c0"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "Number", "k2": "BSON", "k3": "String", "k4": "Number", "k5": "String", "k6": "String", "k7": "String", "k8": "NumberLong", "k9": "NumberLong", "k10": "NumberLong", "k11": "String", "k12": "Number", "k13": "Number", "k14": "Number", "k15": "Number", "k16": "Number", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "Boolean", "k24": "String", "k25": "String", "k26": "String"}}, {"colName": "collection", "count": 7, "size": 2891, "storageSize": 16384, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 413, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "05990b5e-6d86-463f-826f-789b149db0ea"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "Number", "k2": "BSON", "k3": "String", "k4": "Number", "k5": "Number", "k6": "String", "k7": "String", "k8": "String", "k9": "NumberLong", "k10": "String", "k11": "NumberLong", "k12": "NumberLong", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String"}}, {"colName": "collection", "count": 86, "size": 50998, "storageSize": 20480, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 593, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "867e6c21-ec35-4d43-b92f-026c416a9cac"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "String", "k2": "BSON", "k3": "String", "k4": "String", "k5": "String", "k6": "Number", "k7": "String", "k8": "Number", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "Number", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "Number", "k23": "String", "k24": "Number", "k25": "Number", "k26": "Number", "k27": "String", "k28": "String"}}, {"colName": "collection", "count": 6405, "size": 7594206, "storageSize": 4435968, "totalIndexSize": 81920, "indexSizes": {"_id_": 81920}, "avgObjSize": 1185, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "dd98ce7c-8b8c-4b74-83bf-1c58493bcd60"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "Number", "k2": "String", "k3": "Number", "k4": "Number", "k5": "String", "k6": "String", "k7": "String", "k8": "BSON", "k9": "String", "k10": "String", "k11": "Number", "k12": "String", "k13": "String", "k14": "String", "k15": "Number", "k16": "NumberLong", "k17": "String", "k18": "String", "k19": "Number", "k20": "Number", "k21": "Number", "k22": "Number", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "Number", "k32": "Number", "k33": "Number", "k34": "String", "k35": "Number", "k36": "Number", "k37": "Number", "k38": "String", "k39": "Number", "k40": "String", "k41": "Number", "k42": "Boolean", "k43": "String", "k44": "Number", "k45": "Number", "k46": "String", "k47": "String", "k48": "String", "k49": "String", "k50": "Number", "k51": "String", "k52": "String", "k53": "String", "k54": "String"}}, {"colName": "collection", "count": 18137, "size": 16287026, "storageSize": 4853760, "totalIndexSize": 176128, "indexSizes": {"_id_": 176128}, "avgObjSize": 898, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "e27dac8b-a5fe-4cc9-885c-c7debfe749ed"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "Number", "k2": "String", "k3": "BSON", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "Number", "k10": "Number", "k11": "Number", "k12": "Number", "k13": "String", "k14": "NumberLong", "k15": "String", "k16": "NumberLong", "k17": "String", "k18": "Number", "k19": "Number", "k20": "Number", "k21": "String", "k22": "Number", "k23": "Number", "k24": "Number", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "Number", "k30": "String", "k31": "String", "k32": "String", "k33": "Number", "k34": "Number", "k35": "String", "k36": "Number", "k37": "NumberLong", "k38": "Number", "k39": "String", "k40": "String"}}, {"colName": "collection", "count": 14372, "size": 8867524, "storageSize": 1331200, "totalIndexSize": 143360, "indexSizes": {"_id_": 143360}, "avgObjSize": 617, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "d648bbed-b990-4075-81d2-daffffe3cbd9"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "String", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "Number", "k10": "String", "k11": "String", "k12": "Number", "k13": "Number", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "Number", "k22": "Number", "k23": "Number", "k24": "Number", "k25": "BSON"}}, {"colName": "collection", "count": 11447, "size": 13990460, "storageSize": 5881856, "totalIndexSize": 143360, "indexSizes": {"_id_": 143360}, "avgObjSize": 1222, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "c2edded4-eaac-4d11-a32b-8d0bf9b27c7d"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "Number", "k2": "Number", "k3": "String", "k4": "String", "k5": "String", "k6": "Number", "k7": "Number", "k8": "Number", "k9": "String", "k10": "BSON", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "Number", "k16": "String", "k17": "Number", "k18": "Number", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "Number", "k24": "Number", "k25": "Number", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "Number", "k33": "Number", "k34": "Number", "k35": "String", "k36": "Number", "k37": "String", "k38": "Number", "k39": "Number", "k40": "Number", "k41": "Boolean", "k42": "String", "k43": "Number", "k44": "Number", "k45": "String", "k46": "String", "k47": "String", "k48": "String", "k49": "Number", "k50": "String", "k51": "String", "k52": "NumberLong", "k53": "Number", "k54": "String", "k55": "Number", "k56": "Number", "k57": "String", "k58": "String"}}, {"colName": "collection", "count": 2, "size": 1144, "storageSize": 16384, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 572, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "19b9362d-d983-42e1-be30-05ef82b9e092"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "String", "k2": "String", "k3": "BSON", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "NumberLong", "k10": "Number", "k11": "Number", "k12": "String", "k13": "NumberLong", "k14": "Number", "k15": "String", "k16": "Number", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "Boolean", "k22": "Number", "k23": "Number", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "Number", "k31": "Number", "k32": "Number", "k33": "String", "k34": "String"}}, {"colName": "collection", "count": 27915, "size": 15185775, "storageSize": 2252800, "totalIndexSize": 262144, "indexSizes": {"_id_": 262144}, "avgObjSize": 544, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "0f920ce4-8444-4dcf-bf6a-b7e7ec6300ea"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "String", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "NumberLong", "k9": "Number", "k10": "Number", "k11": "String", "k12": "NumberLong", "k13": "Number", "k14": "String", "k15": "Number", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "Boolean", "k21": "Number", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "Number", "k29": "Number", "k30": "Number", "k31": "String", "k32": "BSON"}}, {"colName": "collection", "count": 3268, "size": 5142734, "storageSize": 757760, "totalIndexSize": 40960, "indexSizes": {"_id_": 40960}, "avgObjSize": 1573, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "831df609-d4e6-4736-8445-34d324268009"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "String", "k48": "String", "k49": "String", "k50": "String", "k51": "String", "k52": "String", "k53": "String", "k54": "String", "k55": "String", "k56": "String", "k57": "String", "k58": "String", "k59": "String", "k60": "String", "k61": "String", "k62": "String", "k63": "String", "k64": "String", "k65": "String", "k66": "String", "k67": "String", "k68": "String", "k69": "String", "k70": "String", "k71": "String", "k72": "String", "k73": "String", "k74": "String", "k75": "String", "k76": "String", "k77": "String", "k78": "String", "k79": "String", "k80": "String", "k81": "String", "k82": "String", "k83": "String", "k84": "String", "k85": "String", "k86": "String", "k87": "String"}}, {"colName": "collection", "count": 2653, "size": 2575941, "storageSize": 782336, "totalIndexSize": 32768, "indexSizes": {"_id_": 32768}, "avgObjSize": 970, "colInfo": [{"name": "collection", "type": "collection", "options": {"validationLevel": "off", "validationAction": "error"}, "info": {"readOnly": false, "uuid": "909530aa-2439-4031-b64a-8370b6168695"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "BSON", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "String", "k48": "String", "k49": "String", "k50": "String"}}, {"colName": "collection", "count": 0, "size": 0, "storageSize": 4096, "totalIndexSize": 4096, "indexSizes": {"_id_": 4096}, "avgObjSize": 0, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "a73902ba-91ec-4073-92f5-679751613e5b"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {}}, {"colName": "collection", "count": 1, "size": 1687, "storageSize": 16384, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 1687, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "1fa29c60-a658-428f-bf3b-2b4bbf4bd4b1"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "Number", "k2": "String", "k3": "Number", "k4": "String", "k5": "String", "k6": "Number", "k7": "String", "k8": "String", "k9": "String", "k10": "Number", "k11": "Boolean", "k12": "Number", "k13": "Number", "k14": "Boolean", "k15": "String", "k16": "BSON", "k17": "Number", "k18": "Number", "k19": "String", "k20": "Boolean", "k21": "Number", "k22": "String", "k23": "Number", "k24": "Number", "k25": "Number", "k26": "Number", "k27": "Number", "k28": "Number", "k29": "Number", "k30": "Date", "k31": "Number", "k32": "Date", "k33": "String", "k34": "Number", "k35": "Date", "k36": "String", "k37": "String", "k38": "String", "k39": "Date", "k40": "String", "k41": "Number", "k42": "Number", "k43": "Number", "k44": "Number", "k45": "Number", "k46": "Number", "k47": "Boolean", "k48": "Number", "k49": "String", "k50": "Number", "k51": "Number", "k52": "String", "k53": "Number", "k54": "Number", "k55": "Boolean", "k56": "Number", "k57": "Date", "k58": "Number", "k59": "Number", "k60": "String", "k61": "Number", "k62": "Number", "k63": "Number", "k64": "Number", "k65": "String", "k66": "Number", "k67": "Date", "k68": "String", "k69": "String", "k70": "String", "k71": "Date", "k72": "String", "k73": "Number", "k74": "String", "k75": "Number", "k76": "Number", "k77": "String", "k78": "String", "k79": "String", "k80": "Number", "k81": "Number", "k82": "Number", "k83": "Number", "k84": "Number", "k85": "Number", "k86": "Number", "k87": "Number", "k88": "Number", "k89": "Number", "k90": "Number", "k91": "Number", "k92": "Number", "k93": "Number", "k94": "Number", "k95": "String", "k96": "String", "k97": "Number", "k98": "Number", "k99": "Boolean", "k100": "Number", "k101": "Number", "k102": "String", "k103": "String", "k104": "String", "k105": "Number", "k106": "Boolean", "k107": "Number", "k108": "Number"}}, {"colName": "collection", "count": 1181, "size": 3189552, "storageSize": 995328, "totalIndexSize": 24576, "indexSizes": {"_id_": 24576}, "avgObjSize": 2700, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "21c7c7d5-9a21-4040-9681-26f4fdeb11e8"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "String", "k48": "String", "k49": "String", "k50": "String", "k51": "String", "k52": "String", "k53": "String", "k54": "String", "k55": "String", "k56": "String", "k57": "String", "k58": "String", "k59": "String", "k60": "String", "k61": "String", "k62": "String", "k63": "String", "k64": "String", "k65": "String", "k66": "String", "k67": "String", "k68": "String", "k69": "String", "k70": "String", "k71": "String", "k72": "String", "k73": "String", "k74": "String", "k75": "String", "k76": "String", "k77": "String", "k78": "String", "k79": "String", "k80": "String", "k81": "String", "k82": "String", "k83": "String", "k84": "String", "k85": "String", "k86": "String", "k87": "String", "k88": "String", "k89": "String", "k90": "String", "k91": "String", "k92": "String", "k93": "String", "k94": "String", "k95": "String", "k96": "String", "k97": "String", "k98": "String", "k99": "String", "k100": "String", "k101": "String", "k102": "String", "k103": "String", "k104": "String", "k105": "String", "k106": "String", "k107": "String", "k108": "String", "k109": "String", "k110": "String", "k111": "String", "k112": "String", "k113": "String", "k114": "String", "k115": "String", "k116": "String", "k117": "String", "k118": "String", "k119": "String", "k120": "String", "k121": "String", "k122": "String", "k123": "String", "k124": "String", "k125": "String", "k126": "String", "k127": "String", "k128": "String"}}, {"colName": "collection", "count": 2593, "size": 4033204, "storageSize": 1191936, "totalIndexSize": 32768, "indexSizes": {"_id_": 32768}, "avgObjSize": 1555, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "683e0805-b79d-4f1b-82ff-07b9f1e4c35b"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "String", "k48": "String", "k49": "String", "k50": "String", "k51": "String", "k52": "String", "k53": "String", "k54": "String", "k55": "String", "k56": "String", "k57": "String", "k58": "String", "k59": "String", "k60": "String", "k61": "String", "k62": "String", "k63": "String", "k64": "String", "k65": "String", "k66": "String", "k67": "String", "k68": "String", "k69": "String"}}, {"colName": "collection", "count": 1, "size": 2635, "storageSize": 16384, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 2635, "colInfo": [{"name": "collection", "type": "collection", "options": {"validationLevel": "strict", "validationAction": "error"}, "info": {"readOnly": false, "uuid": "1f9fffdf-dfbf-4fee-a0e9-a38d6eedf6f3"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "String", "k2": "Number", "k3": "String", "k4": "Number", "k5": "Number", "k6": "Number", "k7": "String", "k8": "String", "k9": "Number", "k10": "String", "k11": "String", "k12": "String", "k13": "Number", "k14": "String", "k15": "String", "k16": "BSON", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "Number", "k24": "String", "k25": "String", "k26": "Number", "k27": "Number", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "Number", "k38": "Number", "k39": "Number", "k40": "String", "k41": "String", "k42": "String", "k43": "Number", "k44": "String", "k45": "Number", "k46": "String", "k47": "String", "k48": "String", "k49": "String", "k50": "Number", "k51": "Number", "k52": "String", "k53": "String", "k54": "String", "k55": "Date", "k56": "Date", "k57": "String", "k58": "Number", "k59": "Date", "k60": "String", "k61": "String", "k62": "String", "k63": "Date", "k64": "String", "k65": "Boolean", "k66": "Date", "k67": "String", "k68": "String", "k69": "String", "k70": "String", "k71": "String", "k72": "Number", "k73": "Number", "k74": "Number", "k75": "Number", "k76": "String", "k77": "String", "k78": "String", "k79": "String", "k80": "String", "k81": "String", "k82": "String", "k83": "String", "k84": "String", "k85": "String", "k86": "String", "k87": "String", "k88": "String", "k89": "String", "k90": "String", "k91": "String", "k92": "String", "k93": "String", "k94": "String", "k95": "String", "k96": "String", "k97": "String", "k98": "String", "k99": "String", "k100": "String", "k101": "String", "k102": "String", "k103": "String", "k104": "Number", "k105": "Number", "k106": "String", "k107": "Number", "k108": "Date", "k109": "String", "k110": "String", "k111": "String", "k112": "Date", "k113": "String", "k114": "String", "k115": "Date", "k116": "String", "k117": "String", "k118": "String", "k119": "String", "k120": "Number", "k121": "String", "k122": "String", "k123": "String", "k124": "Number", "k125": "String", "k126": "String", "k127": "String", "k128": "Number", "k129": "String", "k130": "String", "k131": "String", "k132": "String", "k133": "String", "k134": "String", "k135": "String", "k136": "String", "k137": "String", "k138": "String", "k139": "String", "k140": "String", "k141": "String", "k142": "String", "k143": "String", "k144": "String", "k145": "String", "k146": "String", "k147": "String", "k148": "String", "k149": "String", "k150": "String", "k151": "String", "k152": "String", "k153": "String"}}, {"colName": "collection", "count": 5822, "size": 14634469, "storageSize": 4730880, "totalIndexSize": 61440, "indexSizes": {"_id_": 61440}, "avgObjSize": 2513, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "06677f9a-f823-4ddf-b10c-1d5d3b0d999d"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "String", "k48": "String", "k49": "String", "k50": "String", "k51": "String", "k52": "String", "k53": "ObjectId", "k54": "String", "k55": "String", "k56": "String", "k57": "String", "k58": "String", "k59": "String", "k60": "String", "k61": "String", "k62": "String", "k63": "String", "k64": "String", "k65": "String", "k66": "String", "k67": "String", "k68": "String", "k69": "String", "k70": "String", "k71": "String", "k72": "String", "k73": "String", "k74": "String", "k75": "String", "k76": "String", "k77": "String", "k78": "String", "k79": "String", "k80": "String", "k81": "String", "k82": "String", "k83": "String", "k84": "String", "k85": "String", "k86": "String", "k87": "String", "k88": "String", "k89": "String", "k90": "String", "k91": "String", "k92": "String", "k93": "String", "k94": "String", "k95": "String", "k96": "String", "k97": "String", "k98": "String", "k99": "String", "k100": "String", "k101": "String", "k102": "String", "k103": "String", "k104": "String", "k105": "String", "k106": "String", "k107": "String", "k108": "String", "k109": "String", "k110": "String", "k111": "String", "k112": "String", "k113": "String", "k114": "String", "k115": "String", "k116": "String", "k117": "String"}}, {"colName": "collection", "count": 1357, "size": 3878500, "storageSize": 1204224, "totalIndexSize": 45056, "indexSizes": {"_id_": 45056}, "avgObjSize": 2858, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "0d8e342f-8be2-45e8-9b94-5ad16a43b53a"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "String", "k48": "String", "k49": "String", "k50": "String", "k51": "String", "k52": "String", "k53": "String", "k54": "String", "k55": "String", "k56": "String", "k57": "String", "k58": "String", "k59": "String", "k60": "String", "k61": "String", "k62": "String", "k63": "String", "k64": "String", "k65": "String", "k66": "String", "k67": "String", "k68": "String", "k69": "String", "k70": "String", "k71": "String", "k72": "String", "k73": "String", "k74": "String", "k75": "String", "k76": "String", "k77": "String", "k78": "String", "k79": "String", "k80": "String", "k81": "String", "k82": "String", "k83": "String", "k84": "String", "k85": "String", "k86": "String", "k87": "String", "k88": "String", "k89": "String", "k90": "String", "k91": "String", "k92": "String", "k93": "String", "k94": "String", "k95": "String", "k96": "String", "k97": "String", "k98": "String", "k99": "String", "k100": "String", "k101": "String", "k102": "String", "k103": "String", "k104": "String", "k105": "String", "k106": "String", "k107": "String", "k108": "String", "k109": "String", "k110": "String", "k111": "String", "k112": "String", "k113": "String", "k114": "String", "k115": "String", "k116": "String", "k117": "String", "k118": "String", "k119": "String", "k120": "String", "k121": "String", "k122": "String", "k123": "String", "k124": "String"}}, {"colName": "collection", "count": 3834, "size": 9250155, "storageSize": 5689344, "totalIndexSize": 45056, "indexSizes": {"_id_": 45056}, "avgObjSize": 2412, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "9e36357a-3cb6-403b-b18d-434f15c952e5"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "String", "k48": "String", "k49": "String", "k50": "String", "k51": "String", "k52": "String", "k53": "String", "k54": "String", "k55": "String", "k56": "String", "k57": "String", "k58": "String", "k59": "String", "k60": "String", "k61": "String", "k62": "String", "k63": "String", "k64": "String", "k65": "String", "k66": "String", "k67": "String", "k68": "String", "k69": "String", "k70": "String", "k71": "String", "k72": "String", "k73": "String", "k74": "String", "k75": "String", "k76": "String", "k77": "String", "k78": "String", "k79": "String", "k80": "String", "k81": "String", "k82": "String", "k83": "String", "k84": "String", "k85": "String", "k86": "String", "k87": "String", "k88": "String", "k89": "String", "k90": "String", "k91": "String", "k92": "String", "k93": "String", "k94": "String", "k95": "String", "k96": "String", "k97": "String", "k98": "String", "k99": "String", "k100": "String", "k101": "String", "k102": "String", "k103": "String"}}, {"colName": "collection", "count": 5744, "size": 18339154, "storageSize": 5943296, "totalIndexSize": 61440, "indexSizes": {"_id_": 61440}, "avgObjSize": 3192, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "6fcdd29c-f65b-4f21-bfce-1ae1feb4e5fe"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "String", "k48": "String", "k49": "String", "k50": "String", "k51": "String", "k52": "String", "k53": "String", "k54": "String", "k55": "String", "k56": "String", "k57": "String", "k58": "String", "k59": "String", "k60": "String", "k61": "String", "k62": "String", "k63": "String", "k64": "String", "k65": "String", "k66": "String", "k67": "String", "k68": "String", "k69": "String", "k70": "String", "k71": "String", "k72": "String", "k73": "String", "k74": "String", "k75": "String", "k76": "String", "k77": "String", "k78": "String", "k79": "String", "k80": "String", "k81": "String", "k82": "String", "k83": "String", "k84": "String", "k85": "String", "k86": "String", "k87": "String", "k88": "String", "k89": "String", "k90": "String", "k91": "String", "k92": "String", "k93": "String", "k94": "String", "k95": "String", "k96": "String", "k97": "String", "k98": "String", "k99": "String", "k100": "String", "k101": "String", "k102": "String", "k103": "String", "k104": "String", "k105": "String", "k106": "String", "k107": "String", "k108": "String", "k109": "String", "k110": "String", "k111": "String", "k112": "String", "k113": "String", "k114": "String", "k115": "String", "k116": "String", "k117": "String", "k118": "String", "k119": "String", "k120": "String", "k121": "String", "k122": "String", "k123": "String", "k124": "String", "k125": "String", "k126": "String", "k127": "String", "k128": "String", "k129": "String", "k130": "String", "k131": "String", "k132": "String", "k133": "String", "k134": "String", "k135": "String", "k136": "String", "k137": "String", "k138": "String", "k139": "String", "k140": "String", "k141": "String", "k142": "String", "k143": "String", "k144": "String", "k145": "String", "k146": "String", "k147": "String", "k148": "String", "k149": "String", "k150": "String", "k151": "String", "k152": "String", "k153": "String", "k154": "String", "k155": "String"}}, {"colName": "collection", "count": 1835, "size": 6167721, "storageSize": 1921024, "totalIndexSize": 49152, "indexSizes": {"_id_": 49152}, "avgObjSize": 3361, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "d1a9e86c-4f90-45a3-b957-5bb1242d8d60"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "String", "k48": "String", "k49": "String", "k50": "String", "k51": "String", "k52": "String", "k53": "String", "k54": "String", "k55": "String", "k56": "String", "k57": "String", "k58": "String", "k59": "String", "k60": "String", "k61": "String", "k62": "String", "k63": "String", "k64": "String", "k65": "String", "k66": "String", "k67": "String", "k68": "String", "k69": "String", "k70": "String", "k71": "String", "k72": "String", "k73": "String", "k74": "String", "k75": "String", "k76": "String", "k77": "String", "k78": "String", "k79": "String", "k80": "String", "k81": "String", "k82": "String", "k83": "String", "k84": "String", "k85": "String", "k86": "String", "k87": "String", "k88": "String", "k89": "String", "k90": "String", "k91": "String", "k92": "String", "k93": "String", "k94": "String", "k95": "String", "k96": "String", "k97": "String", "k98": "String", "k99": "String", "k100": "String", "k101": "String", "k102": "String", "k103": "String", "k104": "String", "k105": "String", "k106": "String", "k107": "String", "k108": "String", "k109": "String", "k110": "String", "k111": "String", "k112": "String", "k113": "String", "k114": "String", "k115": "String", "k116": "String", "k117": "String", "k118": "String", "k119": "String", "k120": "String", "k121": "String", "k122": "String", "k123": "String", "k124": "String", "k125": "String", "k126": "String", "k127": "String", "k128": "String", "k129": "String", "k130": "String", "k131": "String", "k132": "String", "k133": "String", "k134": "String", "k135": "String", "k136": "String", "k137": "String", "k138": "String", "k139": "String", "k140": "String", "k141": "String", "k142": "String", "k143": "String", "k144": "String", "k145": "String", "k146": "String", "k147": "String", "k148": "String", "k149": "String", "k150": "String", "k151": "String", "k152": "String", "k153": "String", "k154": "String"}}, {"colName": "collection", "count": 1, "size": 1314, "storageSize": 32768, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 1314, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "2b84cb3e-1deb-49ba-9f5e-94490685b1d8"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "String", "k2": "Number", "k3": "String", "k4": "String", "k5": "Number", "k6": "String", "k7": "String", "k8": "String", "k9": "Number", "k10": "String", "k11": "BSON", "k12": "String", "k13": "String", "k14": "Number", "k15": "Number", "k16": "Number", "k17": "Number", "k18": "Number", "k19": "Number", "k20": "Number", "k21": "Number", "k22": "Number", "k23": "Number", "k24": "Number", "k25": "Number", "k26": "Date", "k27": "Date", "k28": "String", "k29": "Number", "k30": "Date", "k31": "String", "k32": "String", "k33": "String", "k34": "Date", "k35": "String", "k36": "Number", "k37": "Number", "k38": "Number", "k39": "Boolean", "k40": "Number", "k41": "String", "k42": "Boolean", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "Number", "k48": "Number", "k49": "String", "k50": "Number", "k51": "Date", "k52": "String", "k53": "String", "k54": "String", "k55": "Date", "k56": "String", "k57": "String", "k58": "String", "k59": "String", "k60": "String", "k61": "Number", "k62": "String", "k63": "String", "k64": "String", "k65": "Number", "k66": "Number", "k67": "Number", "k68": "Number", "k69": "Number", "k70": "Number", "k71": "Number", "k72": "String", "k73": "String", "k74": "String", "k75": "String"}}, {"colName": "collection", "count": 1, "size": 1086, "storageSize": 16384, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 1086, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "b15a0de6-4e75-417e-a04c-72f60f793bb9"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "String", "k2": "Number", "k3": "String", "k4": "String", "k5": "Number", "k6": "String", "k7": "String", "k8": "String", "k9": "Number", "k10": "String", "k11": "BSON", "k12": "String", "k13": "String", "k14": "Number", "k15": "Number", "k16": "Number", "k17": "Number", "k18": "Number", "k19": "Number", "k20": "Number", "k21": "Number", "k22": "Date", "k23": "Date", "k24": "String", "k25": "Number", "k26": "Date", "k27": "String", "k28": "String", "k29": "String", "k30": "Date", "k31": "String", "k32": "Number", "k33": "Number", "k34": "Number", "k35": "String", "k36": "Number", "k37": "Number", "k38": "String", "k39": "Number", "k40": "String", "k41": "Number", "k42": "Number", "k43": "String", "k44": "String", "k45": "String", "k46": "Number", "k47": "Date", "k48": "String", "k49": "String", "k50": "String", "k51": "Date", "k52": "String", "k53": "String", "k54": "String", "k55": "String", "k56": "String", "k57": "String", "k58": "Number", "k59": "Number", "k60": "Number", "k61": "String", "k62": "String", "k63": "String", "k64": "String", "k65": "String", "k66": "String"}}, {"colName": "collection", "count": 5821, "size": 23133819, "storageSize": 6909952, "totalIndexSize": 61440, "indexSizes": {"_id_": 61440}, "avgObjSize": 3974, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "dbcecb73-512f-4aa5-a139-789054c3a12a"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "String", "k48": "String", "k49": "String", "k50": "String", "k51": "String", "k52": "String", "k53": "String", "k54": "String", "k55": "String", "k56": "String", "k57": "String", "k58": "String", "k59": "String", "k60": "String", "k61": "String", "k62": "String", "k63": "String", "k64": "String", "k65": "String", "k66": "String", "k67": "String", "k68": "String", "k69": "String", "k70": "String", "k71": "String", "k72": "String", "k73": "String", "k74": "String", "k75": "String", "k76": "String", "k77": "String", "k78": "String", "k79": "String", "k80": "String", "k81": "String", "k82": "String", "k83": "String", "k84": "String", "k85": "String", "k86": "String", "k87": "String", "k88": "String", "k89": "String", "k90": "String", "k91": "String", "k92": "String", "k93": "String", "k94": "String", "k95": "String", "k96": "String", "k97": "String", "k98": "String", "k99": "String", "k100": "String", "k101": "String", "k102": "String", "k103": "String", "k104": "String", "k105": "String", "k106": "String", "k107": "String", "k108": "String", "k109": "String", "k110": "String", "k111": "String", "k112": "String", "k113": "String", "k114": "String", "k115": "String", "k116": "String", "k117": "String", "k118": "String", "k119": "String", "k120": "String", "k121": "String", "k122": "String", "k123": "String", "k124": "String", "k125": "String", "k126": "String", "k127": "String", "k128": "String", "k129": "String", "k130": "String", "k131": "String", "k132": "String", "k133": "String", "k134": "String", "k135": "String", "k136": "String", "k137": "String", "k138": "String", "k139": "String", "k140": "String", "k141": "String", "k142": "String", "k143": "String", "k144": "String", "k145": "String", "k146": "String", "k147": "String", "k148": "String", "k149": "String", "k150": "String", "k151": "String", "k152": "String", "k153": "String", "k154": "String", "k155": "String", "k156": "String", "k157": "String", "k158": "String", "k159": "String", "k160": "String", "k161": "String", "k162": "String", "k163": "String", "k164": "String", "k165": "String", "k166": "String", "k167": "String", "k168": "String", "k169": "String", "k170": "String", "k171": "String", "k172": "String", "k173": "String", "k174": "String", "k175": "String", "k176": "String", "k177": "String", "k178": "String", "k179": "String", "k180": "String", "k181": "String", "k182": "String", "k183": "String", "k184": "String", "k185": "String", "k186": "String", "k187": "String", "k188": "String", "k189": "String"}}, {"colName": "collection", "count": 991, "size": 2952189, "storageSize": 929792, "totalIndexSize": 20480, "indexSizes": {"_id_": 20480}, "avgObjSize": 2979, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "e96da7bd-61fc-427b-9283-a839ca37ba32"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "String", "k48": "String", "k49": "String", "k50": "String", "k51": "String", "k52": "String", "k53": "String", "k54": "String", "k55": "String", "k56": "String", "k57": "String", "k58": "String", "k59": "String", "k60": "String", "k61": "String", "k62": "String", "k63": "String", "k64": "String", "k65": "String", "k66": "String", "k67": "String", "k68": "String", "k69": "String", "k70": "String", "k71": "String", "k72": "String", "k73": "String", "k74": "String", "k75": "String", "k76": "String", "k77": "String", "k78": "String", "k79": "String", "k80": "String", "k81": "String", "k82": "String", "k83": "String", "k84": "String", "k85": "String", "k86": "String", "k87": "String", "k88": "String", "k89": "String", "k90": "String", "k91": "String", "k92": "String", "k93": "String", "k94": "String", "k95": "String", "k96": "String", "k97": "String", "k98": "String", "k99": "String", "k100": "String", "k101": "String", "k102": "String", "k103": "String", "k104": "String", "k105": "String", "k106": "String", "k107": "String", "k108": "String", "k109": "String", "k110": "String", "k111": "String", "k112": "String", "k113": "String", "k114": "String", "k115": "String", "k116": "String", "k117": "String", "k118": "String", "k119": "String", "k120": "String", "k121": "String", "k122": "String", "k123": "String", "k124": "String", "k125": "String", "k126": "String", "k127": "String", "k128": "String", "k129": "String", "k130": "String", "k131": "String", "k132": "String", "k133": "String", "k134": "String", "k135": "String", "k136": "String", "k137": "String", "k138": "String", "k139": "String", "k140": "String", "k141": "String", "k142": "String", "k143": "String", "k144": "String"}}, {"colName": "collection", "count": 1847, "size": 4365057, "storageSize": 643072, "totalIndexSize": 28672, "indexSizes": {"_id_": 28672}, "avgObjSize": 2363, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "382a8135-49ef-4b9c-b3cc-5a8ee466a3cc"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "String", "k48": "String", "k49": "String", "k50": "String", "k51": "String", "k52": "String", "k53": "String", "k54": "String", "k55": "String", "k56": "String", "k57": "String", "k58": "String", "k59": "String", "k60": "String", "k61": "String", "k62": "String", "k63": "String", "k64": "String", "k65": "String", "k66": "String", "k67": "String", "k68": "String", "k69": "String", "k70": "String", "k71": "String", "k72": "String", "k73": "String", "k74": "String", "k75": "String", "k76": "String", "k77": "String", "k78": "String", "k79": "String", "k80": "String", "k81": "String", "k82": "String", "k83": "String", "k84": "String", "k85": "String", "k86": "String", "k87": "String", "k88": "String", "k89": "String", "k90": "String", "k91": "String", "k92": "String", "k93": "String", "k94": "String", "k95": "String", "k96": "String", "k97": "String", "k98": "String", "k99": "String", "k100": "String", "k101": "String", "k102": "String", "k103": "String", "k104": "String", "k105": "String", "k106": "String", "k107": "String", "k108": "String", "k109": "String", "k110": "String", "k111": "String", "k112": "String", "k113": "String", "k114": "String", "k115": "String", "k116": "String", "k117": "String", "k118": "String", "k119": "String", "k120": "String", "k121": "String", "k122": "String", "k123": "String"}}, {"colName": "collection", "count": 2235, "size": 8656879, "storageSize": 2674688, "totalIndexSize": 32768, "indexSizes": {"_id_": 32768}, "avgObjSize": 3873, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "cf1d7563-248b-48dd-96b7-aace84e2ce88"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "String", "k48": "String", "k49": "String", "k50": "String", "k51": "String", "k52": "String", "k53": "String", "k54": "String", "k55": "String", "k56": "String", "k57": "String", "k58": "String", "k59": "String", "k60": "String", "k61": "String", "k62": "String", "k63": "String", "k64": "String", "k65": "String", "k66": "String", "k67": "String", "k68": "String", "k69": "String", "k70": "String", "k71": "String", "k72": "String", "k73": "String", "k74": "String", "k75": "String", "k76": "String", "k77": "String", "k78": "String", "k79": "String", "k80": "String", "k81": "String", "k82": "String", "k83": "String", "k84": "String", "k85": "String", "k86": "String", "k87": "String", "k88": "String", "k89": "String", "k90": "String", "k91": "String", "k92": "String", "k93": "String", "k94": "String", "k95": "String", "k96": "String", "k97": "String", "k98": "String", "k99": "String", "k100": "String", "k101": "String", "k102": "String", "k103": "String", "k104": "String", "k105": "String", "k106": "String", "k107": "String", "k108": "String", "k109": "String", "k110": "String", "k111": "String", "k112": "String", "k113": "String", "k114": "String", "k115": "String", "k116": "String", "k117": "String", "k118": "String", "k119": "String", "k120": "String", "k121": "String", "k122": "String", "k123": "String", "k124": "String", "k125": "String", "k126": "String", "k127": "String", "k128": "String", "k129": "String", "k130": "String", "k131": "String", "k132": "String", "k133": "String", "k134": "String", "k135": "String", "k136": "String", "k137": "String", "k138": "String", "k139": "String", "k140": "String", "k141": "String", "k142": "String", "k143": "String", "k144": "String", "k145": "String", "k146": "String", "k147": "String", "k148": "String", "k149": "String", "k150": "String", "k151": "String", "k152": "String", "k153": "String", "k154": "String", "k155": "String", "k156": "String", "k157": "String", "k158": "String", "k159": "String", "k160": "String", "k161": "String", "k162": "String", "k163": "String", "k164": "String", "k165": "String", "k166": "String", "k167": "String", "k168": "String", "k169": "String", "k170": "String", "k171": "String", "k172": "String", "k173": "String", "k174": "String", "k175": "String", "k176": "String", "k177": "String", "k178": "String", "k179": "String", "k180": "String", "k181": "String", "k182": "String", "k183": "String", "k184": "String", "k185": "String", "k186": "String", "k187": "String", "k188": "String", "k189": "String", "k190": "String"}}, {"colName": "collection", "count": 1, "size": 2453, "storageSize": 16384, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 2453, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "c6c79f55-640c-48a3-bec4-547d77f441f5"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "String", "k2": "String", "k3": "String", "k4": "Number", "k5": "String", "k6": "String", "k7": "Number", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "Number", "k13": "String", "k14": "String", "k15": "BSON", "k16": "String", "k17": "String", "k18": "Number", "k19": "String", "k20": "String", "k21": "String", "k22": "Number", "k23": "Number", "k24": "String", "k25": "String", "k26": "Number", "k27": "String", "k28": "String", "k29": "Number", "k30": "Number", "k31": "String", "k32": "String", "k33": "Date", "k34": "Number", "k35": "String", "k36": "String", "k37": "Date", "k38": "Number", "k39": "Date", "k40": "String", "k41": "Number", "k42": "Number", "k43": "Number", "k44": "String", "k45": "Number", "k46": "String", "k47": "Date", "k48": "Date", "k49": "String", "k50": "Number", "k51": "Date", "k52": "String", "k53": "String", "k54": "String", "k55": "Date", "k56": "String", "k57": "Number", "k58": "Number", "k59": "Number", "k60": "Number", "k61": "String", "k62": "String", "k63": "String", "k64": "Number", "k65": "String", "k66": "String", "k67": "String", "k68": "String", "k69": "Number", "k70": "Number", "k71": "String", "k72": "Number", "k73": "Number", "k74": "String", "k75": "String", "k76": "String", "k77": "String", "k78": "String", "k79": "String", "k80": "Number", "k81": "String", "k82": "String", "k83": "String", "k84": "String", "k85": "String", "k86": "String", "k87": "String", "k88": "String", "k89": "Number", "k90": "String", "k91": "Number", "k92": "Date", "k93": "String", "k94": "String", "k95": "String", "k96": "Date", "k97": "String", "k98": "String", "k99": "String", "k100": "String", "k101": "String", "k102": "Boolean", "k103": "String", "k104": "String", "k105": "String", "k106": "Number", "k107": "String", "k108": "String", "k109": "String", "k110": "String", "k111": "String", "k112": "String", "k113": "String", "k114": "String", "k115": "String", "k116": "String", "k117": "String", "k118": "String", "k119": "String", "k120": "String", "k121": "String", "k122": "String", "k123": "String", "k124": "String", "k125": "Number", "k126": "String", "k127": "String", "k128": "Date", "k129": "Number", "k130": "String", "k131": "String", "k132": "String", "k133": "String", "k134": "String", "k135": "Number", "k136": "String", "k137": "String", "k138": "String"}}, {"colName": "collection", "count": 3493, "size": 7836322, "storageSize": 2453504, "totalIndexSize": 45056, "indexSizes": {"_id_": 45056}, "avgObjSize": 2243, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "e4f29d54-7530-477e-896c-217f5011726c"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "String", "k48": "String", "k49": "String", "k50": "String", "k51": "String", "k52": "String", "k53": "String", "k54": "String", "k55": "String", "k56": "String", "k57": "String", "k58": "String", "k59": "String", "k60": "String", "k61": "String", "k62": "String", "k63": "String", "k64": "String", "k65": "String", "k66": "String", "k67": "String", "k68": "String", "k69": "String", "k70": "String", "k71": "String", "k72": "String", "k73": "String", "k74": "String", "k75": "String", "k76": "String", "k77": "String", "k78": "String", "k79": "String", "k80": "String", "k81": "String", "k82": "String", "k83": "String", "k84": "String", "k85": "String", "k86": "String", "k87": "String", "k88": "String", "k89": "String", "k90": "String", "k91": "String", "k92": "String", "k93": "String", "k94": "String", "k95": "String", "k96": "String", "k97": "String", "k98": "String", "k99": "String", "k100": "String", "k101": "String", "k102": "String", "k103": "String", "k104": "String", "k105": "String", "k106": "String", "k107": "String", "k108": "String", "k109": "String", "k110": "String", "k111": "String", "k112": "String", "k113": "String", "k114": "String", "k115": "String", "k116": "String", "k117": "String", "k118": "String", "k119": "String", "k120": "String"}}, {"colName": "collection", "count": 2329, "size": 9699460, "storageSize": 3248128, "totalIndexSize": 32768, "indexSizes": {"_id_": 32768}, "avgObjSize": 4164, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "c7267ae9-fc87-427f-9908-1a2eb627e922"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "String", "k48": "String", "k49": "String", "k50": "String", "k51": "String", "k52": "String", "k53": "String", "k54": "String", "k55": "String", "k56": "String", "k57": "String", "k58": "String", "k59": "String", "k60": "String", "k61": "String", "k62": "String", "k63": "String", "k64": "String", "k65": "String", "k66": "String", "k67": "String", "k68": "String", "k69": "String", "k70": "String", "k71": "String", "k72": "String", "k73": "String", "k74": "String", "k75": "String", "k76": "String", "k77": "String", "k78": "String", "k79": "String", "k80": "String", "k81": "String", "k82": "String", "k83": "String", "k84": "String", "k85": "String", "k86": "String", "k87": "String", "k88": "String", "k89": "String", "k90": "String", "k91": "String", "k92": "String", "k93": "String", "k94": "String", "k95": "String", "k96": "String", "k97": "String", "k98": "String", "k99": "String", "k100": "String", "k101": "String", "k102": "String", "k103": "String", "k104": "String", "k105": "String", "k106": "String", "k107": "String", "k108": "String", "k109": "String", "k110": "String", "k111": "String", "k112": "String", "k113": "String", "k114": "String", "k115": "String", "k116": "String", "k117": "String", "k118": "String", "k119": "String", "k120": "String", "k121": "String", "k122": "String", "k123": "String", "k124": "String", "k125": "String", "k126": "String", "k127": "String", "k128": "String", "k129": "String", "k130": "String", "k131": "String", "k132": "String", "k133": "String", "k134": "String", "k135": "String", "k136": "String", "k137": "String", "k138": "String", "k139": "String", "k140": "String", "k141": "String", "k142": "String", "k143": "String", "k144": "String", "k145": "String", "k146": "String", "k147": "String", "k148": "String", "k149": "String", "k150": "String", "k151": "String", "k152": "String", "k153": "String", "k154": "String", "k155": "String", "k156": "String", "k157": "String", "k158": "String", "k159": "String", "k160": "String", "k161": "String", "k162": "String", "k163": "String", "k164": "String", "k165": "String", "k166": "String", "k167": "String", "k168": "String", "k169": "String", "k170": "String", "k171": "String", "k172": "String", "k173": "String", "k174": "String", "k175": "String", "k176": "String", "k177": "String", "k178": "String", "k179": "String", "k180": "String", "k181": "String", "k182": "String", "k183": "String", "k184": "String", "k185": "String", "k186": "String", "k187": "String", "k188": "String", "k189": "String", "k190": "String", "k191": "String", "k192": "String", "k193": "String", "k194": "String", "k195": "String", "k196": "String", "k197": "String", "k198": "String", "k199": "String", "k200": "String", "k201": "String", "k202": "String", "k203": "String", "k204": "String", "k205": "String", "k206": "String", "k207": "String", "k208": "String", "k209": "String", "k210": "String", "k211": "String", "k212": "String"}}, {"colName": "collection", "count": 1165, "size": 5334535, "storageSize": 806912, "totalIndexSize": 24576, "indexSizes": {"_id_": 24576}, "avgObjSize": 4579, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "73ae8bf3-ed7b-47ee-8bd4-344a3a11a6a9"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "String", "k48": "String", "k49": "String", "k50": "String", "k51": "String", "k52": "String", "k53": "String", "k54": "String", "k55": "String", "k56": "String", "k57": "String", "k58": "String", "k59": "String", "k60": "String", "k61": "String", "k62": "String", "k63": "String", "k64": "String", "k65": "String", "k66": "String", "k67": "String", "k68": "String", "k69": "String", "k70": "String", "k71": "String", "k72": "String", "k73": "String", "k74": "String", "k75": "String", "k76": "String", "k77": "String", "k78": "String", "k79": "String", "k80": "String", "k81": "String", "k82": "String", "k83": "String", "k84": "String", "k85": "String", "k86": "String", "k87": "String", "k88": "String", "k89": "String", "k90": "String", "k91": "String", "k92": "String", "k93": "String", "k94": "String", "k95": "String", "k96": "String", "k97": "String", "k98": "String", "k99": "String", "k100": "String", "k101": "String", "k102": "String", "k103": "String", "k104": "String", "k105": "String", "k106": "String", "k107": "String", "k108": "String", "k109": "String", "k110": "String", "k111": "String", "k112": "String", "k113": "String", "k114": "String", "k115": "String", "k116": "String", "k117": "String", "k118": "String", "k119": "String", "k120": "String", "k121": "String", "k122": "String", "k123": "String", "k124": "String", "k125": "String", "k126": "String", "k127": "String", "k128": "String", "k129": "String", "k130": "String", "k131": "String", "k132": "String", "k133": "String", "k134": "String", "k135": "String", "k136": "String", "k137": "String", "k138": "String", "k139": "String", "k140": "String", "k141": "String", "k142": "String", "k143": "String", "k144": "String", "k145": "String", "k146": "String", "k147": "String", "k148": "String", "k149": "String", "k150": "String", "k151": "String", "k152": "String", "k153": "String", "k154": "String", "k155": "String", "k156": "String", "k157": "String", "k158": "String", "k159": "String", "k160": "String", "k161": "String", "k162": "String", "k163": "String", "k164": "String", "k165": "String", "k166": "String", "k167": "String", "k168": "String", "k169": "String", "k170": "String", "k171": "String", "k172": "String", "k173": "String", "k174": "String", "k175": "String", "k176": "String", "k177": "String", "k178": "String", "k179": "String", "k180": "String", "k181": "String", "k182": "String", "k183": "String", "k184": "String", "k185": "String", "k186": "String", "k187": "String", "k188": "String", "k189": "String", "k190": "String", "k191": "String", "k192": "String", "k193": "String", "k194": "String", "k195": "String", "k196": "String", "k197": "String", "k198": "String", "k199": "String", "k200": "String", "k201": "String", "k202": "String", "k203": "String", "k204": "String"}}, {"colName": "collection", "count": 1, "size": 1164, "storageSize": 16384, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 1164, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "41760a92-b604-4d6a-8546-69c8bdfdadcb"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "Number", "k2": "String", "k3": "Number", "k4": "String", "k5": "String", "k6": "String", "k7": "Number", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "Number", "k13": "Number", "k14": "String", "k15": "BSON", "k16": "String", "k17": "String", "k18": "String", "k19": "Number", "k20": "Number", "k21": "Number", "k22": "Number", "k23": "Number", "k24": "String", "k25": "Date", "k26": "Date", "k27": "Date", "k28": "Number", "k29": "String", "k30": "Number", "k31": "Date", "k32": "String", "k33": "String", "k34": "String", "k35": "Date", "k36": "String", "k37": "Date", "k38": "Number", "k39": "Number", "k40": "Number", "k41": "Number", "k42": "Number", "k43": "Number", "k44": "Number", "k45": "Number", "k46": "Number", "k47": "Number", "k48": "Number", "k49": "Date", "k50": "Number", "k51": "String", "k52": "Number", "k53": "Date", "k54": "String", "k55": "String", "k56": "String", "k57": "Date", "k58": "String", "k59": "String", "k60": "Number", "k61": "Number", "k62": "String", "k63": "Number", "k64": "String", "k65": "String", "k66": "String", "k67": "Number", "k68": "Number", "k69": "Number", "k70": "String", "k71": "String", "k72": "String"}}, {"colName": "collection", "count": 917, "size": 3242149, "storageSize": 970752, "totalIndexSize": 20480, "indexSizes": {"_id_": 20480}, "avgObjSize": 3535, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "dac4f054-615c-4cfc-babb-d6021c4651b6"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "String", "k48": "String", "k49": "String", "k50": "String", "k51": "String", "k52": "String", "k53": "String", "k54": "String", "k55": "String", "k56": "String", "k57": "String", "k58": "String", "k59": "String", "k60": "String", "k61": "String", "k62": "String", "k63": "String", "k64": "String", "k65": "String", "k66": "String", "k67": "String", "k68": "String", "k69": "String", "k70": "String", "k71": "String", "k72": "String", "k73": "String", "k74": "String", "k75": "String", "k76": "String", "k77": "String", "k78": "String", "k79": "String", "k80": "String", "k81": "String", "k82": "String", "k83": "String", "k84": "String", "k85": "String", "k86": "String", "k87": "String", "k88": "String", "k89": "String", "k90": "String", "k91": "String", "k92": "String", "k93": "String", "k94": "String", "k95": "String", "k96": "String", "k97": "String", "k98": "String", "k99": "String", "k100": "String", "k101": "String", "k102": "String", "k103": "String", "k104": "String", "k105": "String", "k106": "String", "k107": "String", "k108": "String", "k109": "String", "k110": "String", "k111": "String", "k112": "String", "k113": "String", "k114": "String", "k115": "String", "k116": "String", "k117": "String", "k118": "String", "k119": "String", "k120": "String", "k121": "String", "k122": "String", "k123": "String", "k124": "String", "k125": "String", "k126": "String", "k127": "String", "k128": "String", "k129": "String", "k130": "String", "k131": "String", "k132": "String", "k133": "String", "k134": "String", "k135": "String", "k136": "String", "k137": "String", "k138": "String", "k139": "String", "k140": "String", "k141": "String", "k142": "String", "k143": "String", "k144": "String", "k145": "String", "k146": "String", "k147": "String", "k148": "String", "k149": "String", "k150": "String", "k151": "String", "k152": "String", "k153": "String", "k154": "String", "k155": "String", "k156": "String", "k157": "String", "k158": "String", "k159": "String", "k160": "String", "k161": "String", "k162": "String", "k163": "String", "k164": "String", "k165": "String", "k166": "String"}}, {"colName": "collection", "count": 1, "size": 1466, "storageSize": 36864, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 1466, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "4c62494e-a230-43a1-a767-c6647b04d7a0"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "String", "k2": "Number", "k3": "String", "k4": "String", "k5": "Number", "k6": "String", "k7": "String", "k8": "String", "k9": "Number", "k10": "String", "k11": "BSON", "k12": "String", "k13": "String", "k14": "String", "k15": "Number", "k16": "Number", "k17": "Number", "k18": "Number", "k19": "Number", "k20": "Number", "k21": "Number", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "Number", "k27": "String", "k28": "Date", "k29": "Date", "k30": "String", "k31": "String", "k32": "Number", "k33": "Number", "k34": "String", "k35": "Number", "k36": "Date", "k37": "String", "k38": "String", "k39": "String", "k40": "Date", "k41": "String", "k42": "Number", "k43": "Number", "k44": "Number", "k45": "Number", "k46": "Number", "k47": "Number", "k48": "String", "k49": "Number", "k50": "String", "k51": "Number", "k52": "String", "k53": "String", "k54": "String", "k55": "Number", "k56": "Number", "k57": "String", "k58": "Number", "k59": "Date", "k60": "String", "k61": "String", "k62": "String", "k63": "Date", "k64": "String", "k65": "String", "k66": "String", "k67": "String", "k68": "String", "k69": "String", "k70": "String", "k71": "String", "k72": "String", "k73": "String", "k74": "Number", "k75": "String", "k76": "Number", "k77": "Number", "k78": "Number", "k79": "Number", "k80": "Number", "k81": "String", "k82": "String", "k83": "String", "k84": "String", "k85": "String", "k86": "String"}}, {"colName": "collection", "count": 9036, "size": 1057212, "storageSize": 327680, "totalIndexSize": 94208, "indexSizes": {"_id_": 94208}, "avgObjSize": 117, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "921d8ebf-fe23-4310-815b-e371ba319552"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "Number", "k3": "Number", "k4": "Number", "k5": "NumberLong", "k6": "Number"}}, {"colName": "collection", "count": 307, "size": 114409, "storageSize": 28672, "totalIndexSize": 20480, "indexSizes": {"_id_": 20480}, "avgObjSize": 372, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "d330e773-3420-447d-9bfd-05691706b34a"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "String", "k2": "String", "k3": "BSON", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "Number", "k16": "String", "k17": "String"}}, {"colName": "collection", "count": 1499999, "size": 1065994861, "storageSize": 341614592, "totalIndexSize": 44593152, "indexSizes": {"_id_": 20500480, "carrCenFre_1": 10629120, "datIntTim_1": 6803456, "生命周期管理": 6660096}, "avgObjSize": 710, "colInfo": [{"name": "collection", "type": "collection", "options": {"validator": {"unitID": {"$regex": "{Han}a-zA-Z0-9]{1,2}"}, "siteID": {"$regex": "{Han}a-zA-Z0-9]{1,2}"}, "siteNam": {"$regex": "{Han}a-zA-Z0-9]{1,16}"}, "senTypID": {"$regex": "{Han}a-zA-Z0-9]{1,2}"}, "senPlaTypID": {"$regex": "{Han}a-zA-Z0-9]{1,2}"}, "antNam": {"$regex": "{Han}a-zA-Z0-9]{1,20}"}}, "validationLevel": "off", "validationAction": "error"}, "info": {"readOnly": false, "uuid": "41f1b144-7b7a-4394-bdaf-4f75cf1e5bb3"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "String", "k2": "String", "k3": "String", "k4": "Number", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "Number", "k10": "String", "k11": "Date", "k12": "String", "k13": "String", "k14": "Number", "k15": "<PERSON><PERSON>", "k16": "Number", "k17": "Number", "k18": "Number", "k19": "Number", "k20": "String", "k21": "String", "k22": "String", "k23": "Number", "k24": "Number", "k25": "String", "k26": "<PERSON><PERSON>", "k27": "<PERSON><PERSON>", "k28": "<PERSON><PERSON>", "k29": "<PERSON><PERSON>", "k30": "<PERSON><PERSON>", "k31": "<PERSON><PERSON>", "k32": "<PERSON><PERSON>", "k33": "<PERSON><PERSON>", "k34": "<PERSON><PERSON>", "k35": "<PERSON><PERSON>", "k36": "<PERSON><PERSON>", "k37": "<PERSON><PERSON>", "k38": "<PERSON><PERSON>", "k39": "<PERSON><PERSON>", "k40": "<PERSON><PERSON>", "k41": "<PERSON><PERSON>", "k42": "<PERSON><PERSON>", "k43": "<PERSON><PERSON>", "k44": "<PERSON><PERSON>", "k45": "<PERSON><PERSON>", "k46": "<PERSON><PERSON>", "k47": "BSON"}}, {"colName": "collection", "count": 1500000, "size": 734999996, "storageSize": 239095808, "totalIndexSize": 33394688, "indexSizes": {"_id_": 15417344, "datIntTim_1": 6766592, "hopNum_1": 11210752}, "avgObjSize": 489, "colInfo": [{"name": "collection", "type": "collection", "options": {"validator": {"unitID": {"$regex": "{Han}a-zA-Z0-9]{1,2}"}, "siteID": {"$regex": "{Han}a-zA-Z0-9]{1,2}"}, "siteNam": {"$regex": "{Han}a-zA-Z0-9]{1,16}"}, "senTypID": {"$regex": "{Han}a-zA-Z0-9]{1,2}"}, "senPlaTypID": {"$regex": "{Han}a-zA-Z0-9]{1,2}"}, "antNam": {"$regex": "{Han}a-zA-Z0-9]{1,20}"}}, "validationLevel": "off", "validationAction": "error"}, "info": {"readOnly": false, "uuid": "dd8e7356-10e5-4a11-9ef3-25d7b855e313"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "String", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "NumberLong", "k10": "String", "k11": "Number", "k12": "Number", "k13": "String", "k14": "Number", "k15": "Number", "k16": "Number", "k17": "Number", "k18": "Number", "k19": "Date", "k20": "String", "k21": "<PERSON><PERSON>", "k22": "<PERSON><PERSON>", "k23": "<PERSON><PERSON>", "k24": "<PERSON><PERSON>", "k25": "<PERSON><PERSON>", "k26": "<PERSON><PERSON>", "k27": "<PERSON><PERSON>", "k28": "BSON"}}, {"colName": "collection", "count": 5, "size": 2435, "storageSize": 16384, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 487, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "0b4e8672-b13c-4ee8-a937-965a73de6a67"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "String", "k2": "BSON", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String"}}, {"colName": "collection", "count": 7, "size": 7511, "storageSize": 16384, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 1073, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "150d837a-7f95-4dea-9311-f9d79a5e0a57"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "String", "k2": "String", "k3": "String", "k4": "BSON", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "String", "k48": "String", "k49": "String", "k50": "String", "k51": "String"}}, {"colName": "collection", "count": 2188, "size": 2594119, "storageSize": 782336, "totalIndexSize": 32768, "indexSizes": {"_id_": 32768}, "avgObjSize": 1185, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "4adff9a6-7b12-4cc7-bb88-4e557ec69caa"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "Number", "k2": "Number", "k3": "String", "k4": "Number", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "BSON", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "Number", "k30": "String", "k31": "String", "k32": "Number", "k33": "String", "k34": "String", "k35": "Number", "k36": "String", "k37": "Number", "k38": "String", "k39": "Number", "k40": "Number", "k41": "String", "k42": "String", "k43": "Number", "k44": "Number", "k45": "Number", "k46": "String", "k47": "String", "k48": "String", "k49": "String", "k50": "Number", "k51": "String", "k52": "String", "k53": "Number", "k54": "Number", "k55": "String", "k56": "Number", "k57": "String", "k58": "String", "k59": "String", "k60": "String", "k61": "String", "k62": "String"}}, {"colName": "collection", "count": 6, "size": 1827, "storageSize": 16384, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 304, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "b453c4c4-43ac-4088-8382-4df49bc8e40b"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String"}}, {"colName": "collection", "count": 1, "size": 878, "storageSize": 36864, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 878, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "27ae00f0-f5f0-4800-be23-c38e39331545"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "Number", "k2": "Number", "k3": "Number", "k4": "BSON", "k5": "String", "k6": "Number", "k7": "String", "k8": "Number", "k9": "Number", "k10": "String", "k11": "Number", "k12": "String", "k13": "String", "k14": "Number", "k15": "String", "k16": "String", "k17": "Number", "k18": "Date", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "Number", "k24": "Number", "k25": "Date", "k26": "String", "k27": "String", "k28": "String", "k29": "Number", "k30": "String", "k31": "Number", "k32": "String", "k33": "String", "k34": "String", "k35": "Number", "k36": "String", "k37": "String", "k38": "Number", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "Number", "k44": "Date", "k45": "Date", "k46": "String", "k47": "Number", "k48": "String", "k49": "String", "k50": "String", "k51": "Number"}}, {"colName": "collection", "count": 1, "size": 1237, "storageSize": 32768, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 1237, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "50b59c98-f3b4-43b5-ba43-4032e84c6c30"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "Number", "k2": "Number", "k3": "BSON", "k4": "Number", "k5": "String", "k6": "Number", "k7": "Number", "k8": "Number", "k9": "Number", "k10": "String", "k11": "String", "k12": "Number", "k13": "String", "k14": "String", "k15": "String", "k16": "Number", "k17": "Number", "k18": "Number", "k19": "Number", "k20": "Number", "k21": "Number", "k22": "Date", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "Number", "k29": "String", "k30": "Number", "k31": "String", "k32": "String", "k33": "String", "k34": "Number", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "Number", "k45": "Date", "k46": "Date", "k47": "Number", "k48": "String", "k49": "Number", "k50": "Number", "k51": "Number", "k52": "Number", "k53": "Number", "k54": "Number", "k55": "Number", "k56": "Number", "k57": "Number", "k58": "Number", "k59": "Number", "k60": "Number", "k61": "String", "k62": "String", "k63": "Number", "k64": "Number", "k65": "Number", "k66": "Number", "k67": "String", "k68": "String", "k69": "String", "k70": "Number", "k71": "Number"}}, {"colName": "collection", "count": 72, "size": 59975, "storageSize": 20480, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 832, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "8ece024c-088a-4c33-bb53-d5617ed21a1f"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "<PERSON><PERSON>", "k6": "Number", "k7": "String", "k8": "<PERSON><PERSON>", "k9": "String", "k10": "<PERSON><PERSON>", "k11": "NumberLong", "k12": "String", "k13": "Number", "k14": "String", "k15": "String", "k16": "<PERSON><PERSON>", "k17": "<PERSON><PERSON>", "k18": "<PERSON><PERSON>", "k19": "Number", "k20": "<PERSON><PERSON>", "k21": "<PERSON><PERSON>", "k22": "String", "k23": "String", "k24": "String", "k25": "<PERSON><PERSON>", "k26": "String", "k27": "String", "k28": "Number", "k29": "String", "k30": "String", "k31": "NumberLong", "k32": "String", "k33": "<PERSON><PERSON>", "k34": "String", "k35": "<PERSON><PERSON>", "k36": "<PERSON><PERSON>", "k37": "String", "k38": "String"}}, {"colName": "collection", "count": 1, "size": 1141, "storageSize": 32768, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 1141, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "0e2949f9-f96c-44c9-acc6-a016d0e67bfe"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "String", "k2": "String", "k3": "String", "k4": "String", "k5": "BSON", "k6": "String", "k7": "String", "k8": "Date", "k9": "Number", "k10": "Number", "k11": "Number", "k12": "Number", "k13": "String", "k14": "String", "k15": "String", "k16": "Number", "k17": "String", "k18": "String", "k19": "Number", "k20": "String", "k21": "String", "k22": "Number", "k23": "Date", "k24": "String", "k25": "String", "k26": "Number", "k27": "Date", "k28": "String", "k29": "String", "k30": "String", "k31": "Number", "k32": "Date", "k33": "String", "k34": "Number", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "String", "k48": "String", "k49": "Number", "k50": "String", "k51": "String", "k52": "Number", "k53": "String", "k54": "Date", "k55": "String", "k56": "Date", "k57": "Date", "k58": "Number", "k59": "String", "k60": "String", "k61": "String", "k62": "String", "k63": "String", "k64": "String"}}, {"colName": "collection", "count": 1, "size": 762, "storageSize": 16384, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 762, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "cace5b6b-**************-dae45053857f"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "Number", "k2": "String", "k3": "Number", "k4": "BSON", "k5": "String", "k6": "Number", "k7": "Number", "k8": "Number", "k9": "String", "k10": "String", "k11": "Number", "k12": "String", "k13": "String", "k14": "Number", "k15": "Date", "k16": "Number", "k17": "Date", "k18": "Number", "k19": "String", "k20": "String", "k21": "Number", "k22": "String", "k23": "String", "k24": "String", "k25": "Number", "k26": "String", "k27": "Number", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "Number", "k38": "Date", "k39": "Date", "k40": "Number", "k41": "String", "k42": "String"}}, {"colName": "collection", "count": 1, "size": 790, "storageSize": 32768, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 790, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "10fc831f-9d36-4b80-842b-ff558472e888"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "String", "k2": "String", "k3": "BSON", "k4": "String", "k5": "Number", "k6": "Number", "k7": "String", "k8": "Number", "k9": "String", "k10": "String", "k11": "String", "k12": "Number", "k13": "String", "k14": "Date", "k15": "String", "k16": "Number", "k17": "Date", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "Number", "k29": "Number", "k30": "String", "k31": "Number", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "Number", "k37": "Date", "k38": "Date", "k39": "Number", "k40": "String", "k41": "String", "k42": "String", "k43": "String"}}, {"colName": "collection", "count": 2000, "size": 1500888, "storageSize": 229376, "totalIndexSize": 28672, "indexSizes": {"_id_": 28672}, "avgObjSize": 750, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "a8341371-57be-48b2-8221-dd414294707c"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "Number", "k6": "<PERSON><PERSON>", "k7": "NumberLong", "k8": "String", "k9": "String", "k10": "String", "k11": "NumberLong", "k12": "String", "k13": "Number", "k14": "<PERSON><PERSON>", "k15": "String", "k16": "<PERSON><PERSON>", "k17": "String", "k18": "<PERSON><PERSON>", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "NumberLong", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "<PERSON><PERSON>", "k30": "String", "k31": "<PERSON><PERSON>", "k32": "<PERSON><PERSON>", "k33": "String", "k34": "String"}}, {"colName": "collection", "count": 1, "size": 768, "storageSize": 16384, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 768, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "f0296f57-85b4-4844-80d5-085a3510fe4e"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "String", "k4": "String", "k5": "Number", "k6": "Number", "k7": "Number", "k8": "Number", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "Number", "k14": "String", "k15": "String", "k16": "Number", "k17": "Date", "k18": "String", "k19": "String", "k20": "String", "k21": "Number", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "Date", "k34": "String", "k35": "String", "k36": "Number", "k37": "Number", "k38": "Number", "k39": "Date", "k40": "Number", "k41": "String", "k42": "String"}}, {"colName": "collection", "count": 1, "size": 657, "storageSize": 16384, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 657, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "0cf65959-dd6f-486b-9003-adf0916bd9b6"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "Number", "k4": "Number", "k5": "String", "k6": "Number", "k7": "String", "k8": "String", "k9": "Number", "k10": "String", "k11": "String", "k12": "Number", "k13": "Date", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "Number", "k30": "Date", "k31": "Date", "k32": "Number", "k33": "String", "k34": "String", "k35": "String"}}, {"colName": "collection", "count": 1, "size": 1387, "storageSize": 16384, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 1387, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "af513f50-60db-49ad-b0f4-98bfdbd327b7"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "String", "k2": "String", "k3": "BSON", "k4": "String", "k5": "String", "k6": "Date", "k7": "String", "k8": "String", "k9": "Date", "k10": "Date", "k11": "String", "k12": "String", "k13": "String", "k14": "Date", "k15": "Number", "k16": "Number", "k17": "Number", "k18": "Number", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "Number", "k24": "String", "k25": "String", "k26": "Number", "k27": "String", "k28": "String", "k29": "String", "k30": "Number", "k31": "Date", "k32": "String", "k33": "String", "k34": "Number", "k35": "Date", "k36": "Number", "k37": "String", "k38": "String", "k39": "Number", "k40": "String", "k41": "String", "k42": "Number", "k43": "Date", "k44": "String", "k45": "Number", "k46": "String", "k47": "String", "k48": "String", "k49": "String", "k50": "String", "k51": "String", "k52": "String", "k53": "String", "k54": "String", "k55": "String", "k56": "String", "k57": "String", "k58": "String", "k59": "String", "k60": "Date", "k61": "String", "k62": "String", "k63": "Number", "k64": "String", "k65": "String", "k66": "Number", "k67": "String", "k68": "Date", "k69": "String", "k70": "Date", "k71": "Number", "k72": "String", "k73": "String", "k74": "String", "k75": "String", "k76": "String"}}, {"colName": "collection", "count": 359, "size": 355483, "storageSize": 65536, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 990, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "f94f4173-847d-40ea-a754-d629428abeee"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "<PERSON><PERSON>", "k3": "String", "k4": "String", "k5": "<PERSON><PERSON>", "k6": "<PERSON><PERSON>", "k7": "String", "k8": "<PERSON><PERSON>", "k9": "<PERSON><PERSON>", "k10": "<PERSON><PERSON>", "k11": "<PERSON><PERSON>", "k12": "String", "k13": "String", "k14": "<PERSON><PERSON>", "k15": "String", "k16": "<PERSON><PERSON>", "k17": "Number", "k18": "String", "k19": "<PERSON><PERSON>", "k20": "<PERSON><PERSON>", "k21": "NumberLong", "k22": "<PERSON><PERSON>", "k23": "Number", "k24": "String", "k25": "String", "k26": "<PERSON><PERSON>", "k27": "Number", "k28": "String", "k29": "<PERSON><PERSON>", "k30": "<PERSON><PERSON>", "k31": "Number", "k32": "<PERSON><PERSON>", "k33": "<PERSON><PERSON>", "k34": "Number", "k35": "<PERSON><PERSON>", "k36": "Number", "k37": "Number", "k38": "<PERSON><PERSON>", "k39": "String", "k40": "<PERSON><PERSON>", "k41": "<PERSON><PERSON>", "k42": "String", "k43": "String", "k44": "String", "k45": "<PERSON><PERSON>", "k46": "<PERSON><PERSON>", "k47": "<PERSON><PERSON>", "k48": "<PERSON><PERSON>", "k49": "String", "k50": "<PERSON><PERSON>", "k51": "Number", "k52": "String", "k53": "<PERSON><PERSON>", "k54": "<PERSON><PERSON>", "k55": "<PERSON><PERSON>", "k56": "<PERSON><PERSON>", "k57": "<PERSON><PERSON>"}}, {"colName": "collection", "count": 1381, "size": 710527, "storageSize": 118784, "totalIndexSize": 24576, "indexSizes": {"_id_": 24576}, "avgObjSize": 514, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "42defeea-8d73-460f-a697-3f42356b6138"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "<PERSON><PERSON>", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "String", "k6": "String", "k7": "<PERSON><PERSON>", "k8": "<PERSON><PERSON>", "k9": "<PERSON><PERSON>", "k10": "<PERSON><PERSON>", "k11": "<PERSON><PERSON>", "k12": "<PERSON><PERSON>", "k13": "String", "k14": "NumberLong", "k15": "<PERSON><PERSON>", "k16": "<PERSON><PERSON>", "k17": "String", "k18": "<PERSON><PERSON>", "k19": "<PERSON><PERSON>", "k20": "<PERSON><PERSON>", "k21": "<PERSON><PERSON>", "k22": "<PERSON><PERSON>", "k23": "<PERSON><PERSON>", "k24": "<PERSON><PERSON>", "k25": "<PERSON><PERSON>", "k26": "<PERSON><PERSON>", "k27": "<PERSON><PERSON>", "k28": "<PERSON><PERSON>", "k29": "<PERSON><PERSON>", "k30": "<PERSON><PERSON>", "k31": "<PERSON><PERSON>", "k32": "String", "k33": "<PERSON><PERSON>", "k34": "<PERSON><PERSON>", "k35": "<PERSON><PERSON>", "k36": "<PERSON><PERSON>"}}, {"colName": "collection", "count": 257, "size": 251398, "storageSize": 49152, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 978, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "6493702a-d870-49df-b29a-26976f7eb531"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "<PERSON><PERSON>", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "String", "k6": "<PERSON><PERSON>", "k7": "String", "k8": "<PERSON><PERSON>", "k9": "<PERSON><PERSON>", "k10": "<PERSON><PERSON>", "k11": "<PERSON><PERSON>", "k12": "<PERSON><PERSON>", "k13": "NumberLong", "k14": "<PERSON><PERSON>", "k15": "<PERSON><PERSON>", "k16": "<PERSON><PERSON>", "k17": "<PERSON><PERSON>", "k18": "<PERSON><PERSON>", "k19": "<PERSON><PERSON>", "k20": "<PERSON><PERSON>", "k21": "<PERSON><PERSON>", "k22": "<PERSON><PERSON>", "k23": "<PERSON><PERSON>", "k24": "<PERSON><PERSON>", "k25": "<PERSON><PERSON>", "k26": "<PERSON><PERSON>", "k27": "<PERSON><PERSON>", "k28": "Number", "k29": "String", "k30": "<PERSON><PERSON>", "k31": "<PERSON><PERSON>", "k32": "<PERSON><PERSON>", "k33": "<PERSON><PERSON>", "k34": "<PERSON><PERSON>", "k35": "<PERSON><PERSON>", "k36": "String", "k37": "String", "k38": "<PERSON><PERSON>", "k39": "<PERSON><PERSON>", "k40": "<PERSON><PERSON>", "k41": "<PERSON><PERSON>", "k42": "<PERSON><PERSON>", "k43": "<PERSON><PERSON>", "k44": "<PERSON><PERSON>", "k45": "<PERSON><PERSON>", "k46": "<PERSON><PERSON>", "k47": "<PERSON><PERSON>", "k48": "<PERSON><PERSON>", "k49": "<PERSON><PERSON>", "k50": "<PERSON><PERSON>", "k51": "<PERSON><PERSON>", "k52": "String", "k53": "<PERSON><PERSON>", "k54": "<PERSON><PERSON>", "k55": "String", "k56": "<PERSON><PERSON>", "k57": "<PERSON><PERSON>", "k58": "<PERSON><PERSON>", "k59": "<PERSON><PERSON>", "k60": "<PERSON><PERSON>", "k61": "Number", "k62": "<PERSON><PERSON>", "k63": "<PERSON><PERSON>", "k64": "<PERSON><PERSON>", "k65": "<PERSON><PERSON>", "k66": "String", "k67": "<PERSON><PERSON>", "k68": "<PERSON><PERSON>", "k69": "<PERSON><PERSON>"}}, {"colName": "collection", "count": 836, "size": 448037, "storageSize": 77824, "totalIndexSize": 20480, "indexSizes": {"_id_": 20480}, "avgObjSize": 535, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "066358d1-4ae8-403b-ba92-f3650b1c6515"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "<PERSON><PERSON>", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "<PERSON><PERSON>", "k6": "<PERSON><PERSON>", "k7": "<PERSON><PERSON>", "k8": "<PERSON><PERSON>", "k9": "<PERSON><PERSON>", "k10": "NumberLong", "k11": "<PERSON><PERSON>", "k12": "<PERSON><PERSON>", "k13": "<PERSON><PERSON>", "k14": "String", "k15": "<PERSON><PERSON>", "k16": "<PERSON><PERSON>", "k17": "String", "k18": "<PERSON><PERSON>", "k19": "<PERSON><PERSON>", "k20": "<PERSON><PERSON>", "k21": "<PERSON><PERSON>", "k22": "<PERSON><PERSON>", "k23": "<PERSON><PERSON>", "k24": "<PERSON><PERSON>", "k25": "<PERSON><PERSON>", "k26": "<PERSON><PERSON>", "k27": "<PERSON><PERSON>", "k28": "<PERSON><PERSON>", "k29": "<PERSON><PERSON>", "k30": "String", "k31": "<PERSON><PERSON>", "k32": "<PERSON><PERSON>", "k33": "<PERSON><PERSON>", "k34": "<PERSON><PERSON>"}}, {"colName": "collection", "count": 64, "size": 38398, "storageSize": 20480, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 599, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "e7c0f871-4b34-42a2-81c3-b9da9a124f0b"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "<PERSON><PERSON>", "k3": "String", "k4": "String", "k5": "<PERSON><PERSON>", "k6": "<PERSON><PERSON>", "k7": "<PERSON><PERSON>", "k8": "String", "k9": "<PERSON><PERSON>", "k10": "Number", "k11": "<PERSON><PERSON>", "k12": "<PERSON><PERSON>", "k13": "NumberLong", "k14": "<PERSON><PERSON>", "k15": "<PERSON><PERSON>", "k16": "String", "k17": "<PERSON><PERSON>", "k18": "<PERSON><PERSON>", "k19": "Number", "k20": "String", "k21": "<PERSON><PERSON>", "k22": "<PERSON><PERSON>", "k23": "<PERSON><PERSON>", "k24": "<PERSON><PERSON>", "k25": "<PERSON><PERSON>", "k26": "<PERSON><PERSON>", "k27": "<PERSON><PERSON>", "k28": "<PERSON><PERSON>", "k29": "<PERSON><PERSON>", "k30": "String", "k31": "<PERSON><PERSON>", "k32": "Number", "k33": "String", "k34": "<PERSON><PERSON>", "k35": "<PERSON><PERSON>", "k36": "<PERSON><PERSON>", "k37": "<PERSON><PERSON>"}}, {"colName": "collection", "count": 1, "size": 754, "storageSize": 16384, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 754, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "b4255b77-86f9-4565-8380-c4fc0a5fea1f"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "String", "k4": "String", "k5": "Number", "k6": "Number", "k7": "Number", "k8": "String", "k9": "String", "k10": "Number", "k11": "String", "k12": "String", "k13": "Number", "k14": "Date", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "Date", "k32": "String", "k33": "String", "k34": "Number", "k35": "String", "k36": "Date", "k37": "String", "k38": "Number", "k39": "String", "k40": "String", "k41": "String"}}, {"colName": "collection", "count": 1, "size": 816, "storageSize": 16384, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 816, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "3dde334a-5bda-4a41-9c81-8380cd997d34"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "Number", "k4": "Number", "k5": "Number", "k6": "String", "k7": "String", "k8": "Number", "k9": "String", "k10": "Date", "k11": "Date", "k12": "String", "k13": "Number", "k14": "Date", "k15": "String", "k16": "String", "k17": "String", "k18": "Number", "k19": "Date", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "Date", "k37": "String", "k38": "String", "k39": "Number", "k40": "Date", "k41": "Number", "k42": "String", "k43": "String", "k44": "String"}}, {"colName": "collection", "count": 1, "size": 1894, "storageSize": 32768, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 1894, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "8764093f-beb6-475a-affa-3b9a32891acc"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "String", "k2": "BSON", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "Number", "k8": "Number", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "Number", "k16": "String", "k17": "String", "k18": "String", "k19": "Number", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "Number", "k27": "String", "k28": "String", "k29": "String", "k30": "Number", "k31": "Number", "k32": "Date", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "Date", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "String", "k48": "String", "k49": "String", "k50": "String", "k51": "String", "k52": "String", "k53": "String", "k54": "String", "k55": "String", "k56": "String", "k57": "String", "k58": "String", "k59": "String", "k60": "String", "k61": "String", "k62": "String", "k63": "String", "k64": "String", "k65": "String", "k66": "String", "k67": "String", "k68": "String", "k69": "String", "k70": "String", "k71": "Date", "k72": "String", "k73": "String", "k74": "Number", "k75": "String", "k76": "Date", "k77": "String", "k78": "String", "k79": "Number", "k80": "String", "k81": "String", "k82": "String", "k83": "String", "k84": "String", "k85": "String", "k86": "String", "k87": "Number", "k88": "String", "k89": "String", "k90": "String", "k91": "String", "k92": "String", "k93": "String", "k94": "String", "k95": "String", "k96": "String", "k97": "String", "k98": "String", "k99": "String", "k100": "String", "k101": "String"}}, {"colName": "collection", "count": 8098, "size": 4948094, "storageSize": 733184, "totalIndexSize": 81920, "indexSizes": {"_id_": 81920}, "avgObjSize": 611, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "ca5e8621-ca84-463e-b743-226c77a4b391"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "<PERSON><PERSON>", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "<PERSON><PERSON>", "k6": "<PERSON><PERSON>", "k7": "<PERSON><PERSON>", "k8": "String", "k9": "<PERSON><PERSON>", "k10": "Number", "k11": "String", "k12": "<PERSON><PERSON>", "k13": "NumberLong", "k14": "Number", "k15": "String", "k16": "Number", "k17": "String", "k18": "<PERSON><PERSON>", "k19": "<PERSON><PERSON>", "k20": "Number", "k21": "String", "k22": "String", "k23": "<PERSON><PERSON>", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "<PERSON><PERSON>", "k29": "<PERSON><PERSON>", "k30": "<PERSON><PERSON>", "k31": "<PERSON><PERSON>", "k32": "String", "k33": "<PERSON><PERSON>", "k34": "Number", "k35": "String", "k36": "<PERSON><PERSON>", "k37": "<PERSON><PERSON>", "k38": "Number", "k39": "<PERSON><PERSON>", "k40": "<PERSON><PERSON>", "k41": "<PERSON><PERSON>", "k42": "Number", "k43": "Number"}}, {"colName": "collection", "count": 28, "size": 27198, "storageSize": 32768, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 971, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "c00fbb67-a472-4e51-92d0-01cdab19e4a2"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "<PERSON><PERSON>", "k6": "String", "k7": "Number", "k8": "Number", "k9": "Number", "k10": "String", "k11": "<PERSON><PERSON>", "k12": "Number", "k13": "String", "k14": "String", "k15": "NumberLong", "k16": "String", "k17": "Number", "k18": "<PERSON><PERSON>", "k19": "Number", "k20": "String", "k21": "<PERSON><PERSON>", "k22": "String", "k23": "Number", "k24": "String", "k25": "String", "k26": "<PERSON><PERSON>", "k27": "String", "k28": "Number", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "Number", "k35": "<PERSON><PERSON>", "k36": "String", "k37": "String", "k38": "String", "k39": "<PERSON><PERSON>", "k40": "Number", "k41": "String", "k42": "<PERSON><PERSON>", "k43": "<PERSON><PERSON>", "k44": "String", "k45": "String"}}, {"colName": "collection", "count": 2313, "size": 2532302, "storageSize": 1150976, "totalIndexSize": 32768, "indexSizes": {"_id_": 32768}, "avgObjSize": 1094, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "dd5e5f83-8799-4d1a-9e88-3c2cb36d2ad2"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "<PERSON><PERSON>", "k3": "<PERSON><PERSON>", "k4": "Number", "k5": "<PERSON><PERSON>", "k6": "<PERSON><PERSON>", "k7": "<PERSON><PERSON>", "k8": "String", "k9": "Number", "k10": "<PERSON><PERSON>", "k11": "<PERSON><PERSON>", "k12": "<PERSON><PERSON>", "k13": "<PERSON><PERSON>", "k14": "Number", "k15": "String", "k16": "<PERSON><PERSON>", "k17": "Number", "k18": "String", "k19": "<PERSON><PERSON>", "k20": "NumberLong", "k21": "<PERSON><PERSON>", "k22": "<PERSON><PERSON>", "k23": "<PERSON><PERSON>", "k24": "<PERSON><PERSON>", "k25": "Number", "k26": "String", "k27": "String", "k28": "<PERSON><PERSON>", "k29": "<PERSON><PERSON>", "k30": "<PERSON><PERSON>", "k31": "Number", "k32": "<PERSON><PERSON>", "k33": "Number", "k34": "String", "k35": "String", "k36": "String", "k37": "<PERSON><PERSON>", "k38": "<PERSON><PERSON>", "k39": "String", "k40": "String", "k41": "<PERSON><PERSON>", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "<PERSON><PERSON>", "k48": "Number", "k49": "Number", "k50": "String", "k51": "<PERSON><PERSON>", "k52": "<PERSON><PERSON>", "k53": "Number", "k54": "<PERSON><PERSON>", "k55": "String", "k56": "String"}}, {"colName": "collection", "count": 76, "size": 95849, "storageSize": 28672, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 1261, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "83e58a2b-45c8-4812-98e2-26487c898842"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "<PERSON><PERSON>", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "<PERSON><PERSON>", "k6": "<PERSON><PERSON>", "k7": "<PERSON><PERSON>", "k8": "<PERSON><PERSON>", "k9": "<PERSON><PERSON>", "k10": "<PERSON><PERSON>", "k11": "<PERSON><PERSON>", "k12": "<PERSON><PERSON>", "k13": "<PERSON><PERSON>", "k14": "<PERSON><PERSON>", "k15": "<PERSON><PERSON>", "k16": "<PERSON><PERSON>", "k17": "<PERSON><PERSON>", "k18": "String", "k19": "<PERSON><PERSON>", "k20": "<PERSON><PERSON>", "k21": "<PERSON><PERSON>", "k22": "<PERSON><PERSON>", "k23": "<PERSON><PERSON>", "k24": "<PERSON><PERSON>", "k25": "<PERSON><PERSON>", "k26": "<PERSON><PERSON>", "k27": "String", "k28": "<PERSON><PERSON>", "k29": "Number", "k30": "<PERSON><PERSON>", "k31": "<PERSON><PERSON>", "k32": "<PERSON><PERSON>", "k33": "<PERSON><PERSON>", "k34": "<PERSON><PERSON>", "k35": "<PERSON><PERSON>", "k36": "<PERSON><PERSON>", "k37": "<PERSON><PERSON>", "k38": "<PERSON><PERSON>", "k39": "Number", "k40": "<PERSON><PERSON>", "k41": "<PERSON><PERSON>", "k42": "NumberLong", "k43": "String", "k44": "<PERSON><PERSON>", "k45": "<PERSON><PERSON>", "k46": "String", "k47": "<PERSON><PERSON>", "k48": "<PERSON><PERSON>", "k49": "<PERSON><PERSON>", "k50": "<PERSON><PERSON>", "k51": "<PERSON><PERSON>", "k52": "<PERSON><PERSON>", "k53": "<PERSON><PERSON>", "k54": "<PERSON><PERSON>", "k55": "<PERSON><PERSON>", "k56": "<PERSON><PERSON>", "k57": "<PERSON><PERSON>", "k58": "<PERSON><PERSON>", "k59": "<PERSON><PERSON>", "k60": "<PERSON><PERSON>", "k61": "<PERSON><PERSON>", "k62": "<PERSON><PERSON>", "k63": "<PERSON><PERSON>", "k64": "String", "k65": "<PERSON><PERSON>", "k66": "Number", "k67": "<PERSON><PERSON>", "k68": "<PERSON><PERSON>", "k69": "<PERSON><PERSON>", "k70": "<PERSON><PERSON>", "k71": "<PERSON><PERSON>", "k72": "<PERSON><PERSON>", "k73": "<PERSON><PERSON>", "k74": "<PERSON><PERSON>", "k75": "<PERSON><PERSON>", "k76": "<PERSON><PERSON>", "k77": "String", "k78": "<PERSON><PERSON>", "k79": "<PERSON><PERSON>", "k80": "<PERSON><PERSON>", "k81": "<PERSON><PERSON>", "k82": "<PERSON><PERSON>"}}, {"colName": "collection", "count": 0, "size": 0, "storageSize": 4096, "totalIndexSize": 4096, "indexSizes": {"_id_": 4096}, "avgObjSize": 0, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "7d92b670-3441-40cf-8579-4b1856a67c32"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {}}, {"colName": "collection", "count": 1, "size": 5419752, "storageSize": 942080, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 5419752, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "50bc4eb9-523f-448b-b527-9631f47304dd"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "<PERSON><PERSON>", "k2": "<PERSON><PERSON>", "k3": "<PERSON><PERSON>", "k4": "Array", "k5": "<PERSON><PERSON>", "k6": "BSON", "k7": "<PERSON><PERSON>", "k8": "<PERSON><PERSON>", "k9": "<PERSON><PERSON>", "k10": "<PERSON><PERSON>", "k11": "<PERSON><PERSON>", "k12": "<PERSON><PERSON>", "k13": "<PERSON><PERSON>", "k14": "<PERSON><PERSON>", "k15": "<PERSON><PERSON>", "k16": "<PERSON><PERSON>", "k17": "<PERSON><PERSON>", "k18": "<PERSON><PERSON>", "k19": "<PERSON><PERSON>", "k20": "<PERSON><PERSON>", "k21": "<PERSON><PERSON>", "k22": "<PERSON><PERSON>", "k23": "<PERSON><PERSON>", "k24": "<PERSON><PERSON>", "k25": "<PERSON><PERSON>", "k26": "<PERSON><PERSON>", "k27": "<PERSON><PERSON>", "k28": "<PERSON><PERSON>", "k29": "<PERSON><PERSON>", "k30": "<PERSON><PERSON>", "k31": "<PERSON><PERSON>", "k32": "<PERSON><PERSON>", "k33": "<PERSON><PERSON>", "k34": "<PERSON><PERSON>", "k35": "<PERSON><PERSON>", "k36": "<PERSON><PERSON>", "k37": "<PERSON><PERSON>", "k38": "<PERSON><PERSON>", "k39": "<PERSON><PERSON>", "k40": "<PERSON><PERSON>", "k41": "<PERSON><PERSON>", "k42": "<PERSON><PERSON>", "k43": "<PERSON><PERSON>", "k44": "<PERSON><PERSON>", "k45": "<PERSON><PERSON>", "k46": "<PERSON><PERSON>", "k47": "<PERSON><PERSON>", "k48": "<PERSON><PERSON>", "k49": "<PERSON><PERSON>", "k50": "<PERSON><PERSON>", "k51": "<PERSON><PERSON>", "k52": "<PERSON><PERSON>", "k53": "<PERSON><PERSON>", "k54": "<PERSON><PERSON>", "k55": "<PERSON><PERSON>", "k56": "<PERSON><PERSON>", "k57": "<PERSON><PERSON>", "k58": "<PERSON><PERSON>", "k59": "<PERSON><PERSON>"}}, {"colName": "collection", "count": 1, "size": 976, "storageSize": 16384, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 976, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "41b65ed3-5850-45f0-ab3c-cc642edd18bb"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "String", "k2": "BSON", "k3": "String", "k4": "String", "k5": "String", "k6": "Number", "k7": "Number", "k8": "Date", "k9": "Number", "k10": "String", "k11": "String", "k12": "Number", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "Number", "k18": "Date", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "Number", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "Date", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "Number", "k45": "Date", "k46": "String", "k47": "Number", "k48": "String", "k49": "String", "k50": "String"}}, {"colName": "collection", "count": 1590, "size": 1273881, "storageSize": 405504, "totalIndexSize": 24576, "indexSizes": {"_id_": 24576}, "avgObjSize": 801, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "bc0a80eb-d6ff-4bad-9bcc-6468a3f5f65f"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "Number", "k6": "String", "k7": "<PERSON><PERSON>", "k8": "String", "k9": "String", "k10": "NumberLong", "k11": "Number", "k12": "String", "k13": "String", "k14": "<PERSON><PERSON>", "k15": "String", "k16": "Number", "k17": "<PERSON><PERSON>", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "Number", "k25": "String", "k26": "String", "k27": "Number", "k28": "String", "k29": "<PERSON><PERSON>", "k30": "String", "k31": "<PERSON><PERSON>", "k32": "<PERSON><PERSON>", "k33": "String", "k34": "String"}}, {"colName": "collection", "count": 1, "size": 1029, "storageSize": 32768, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 1029, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "faee7f12-3d3a-415a-ad5f-0218ccfc8c7e"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "String", "k2": "String", "k3": "String", "k4": "String", "k5": "BSON", "k6": "String", "k7": "String", "k8": "Date", "k9": "String", "k10": "String", "k11": "Number", "k12": "Number", "k13": "String", "k14": "String", "k15": "String", "k16": "Number", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "Number", "k23": "String", "k24": "String", "k25": "Number", "k26": "Date", "k27": "Number", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "Number", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "Date", "k45": "String", "k46": "String", "k47": "Number", "k48": "Date", "k49": "String", "k50": "String", "k51": "Number", "k52": "String", "k53": "String", "k54": "String", "k55": "String", "k56": "String", "k57": "String", "k58": "String", "k59": "Number"}}, {"colName": "collection", "count": 1424, "size": 925363, "storageSize": 294912, "totalIndexSize": 24576, "indexSizes": {"_id_": 24576}, "avgObjSize": 649, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "f99f8ec4-d9d6-46b4-a481-199ffcead808"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "Number", "k3": "String", "k4": "<PERSON><PERSON>", "k5": "<PERSON><PERSON>", "k6": "Number", "k7": "Number", "k8": "String", "k9": "<PERSON><PERSON>", "k10": "String", "k11": "<PERSON><PERSON>", "k12": "NumberLong", "k13": "Number", "k14": "String", "k15": "<PERSON><PERSON>", "k16": "String", "k17": "<PERSON><PERSON>", "k18": "<PERSON><PERSON>", "k19": "<PERSON><PERSON>", "k20": "String", "k21": "String", "k22": "<PERSON><PERSON>", "k23": "<PERSON><PERSON>", "k24": "<PERSON><PERSON>", "k25": "String", "k26": "String", "k27": "Number", "k28": "String", "k29": "String", "k30": "String", "k31": "<PERSON><PERSON>", "k32": "String", "k33": "<PERSON><PERSON>", "k34": "<PERSON><PERSON>", "k35": "String", "k36": "String", "k37": "Number"}}, {"colName": "collection", "count": 1641, "size": 1853936, "storageSize": 290816, "totalIndexSize": 24576, "indexSizes": {"_id_": 24576}, "avgObjSize": 1129, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "0769b9bb-b688-44b0-8ac2-2707f585d623"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "Number", "k4": "<PERSON><PERSON>", "k5": "<PERSON><PERSON>", "k6": "<PERSON><PERSON>", "k7": "String", "k8": "String", "k9": "Number", "k10": "String", "k11": "<PERSON><PERSON>", "k12": "Number", "k13": "NumberLong", "k14": "NumberLong", "k15": "String", "k16": "String", "k17": "String", "k18": "NumberLong", "k19": "String", "k20": "String", "k21": "<PERSON><PERSON>", "k22": "String", "k23": "String", "k24": "String", "k25": "Number", "k26": "Number", "k27": "String", "k28": "String", "k29": "Number", "k30": "Number", "k31": "<PERSON><PERSON>", "k32": "Number", "k33": "String", "k34": "String", "k35": "String", "k36": "Number", "k37": "<PERSON><PERSON>", "k38": "String", "k39": "String", "k40": "Number", "k41": "String", "k42": "String", "k43": "<PERSON><PERSON>", "k44": "String", "k45": "String", "k46": "Number", "k47": "String", "k48": "String", "k49": "String", "k50": "String", "k51": "Number", "k52": "String", "k53": "String", "k54": "String", "k55": "<PERSON><PERSON>", "k56": "Number", "k57": "NumberLong", "k58": "Number", "k59": "Number", "k60": "Number", "k61": "String", "k62": "String"}}, {"colName": "collection", "count": 50, "size": 127701, "storageSize": 32768, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 2554, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "8067b2f5-7447-4d5a-8d82-4fdee0fccb54"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "String", "k4": "<PERSON><PERSON>", "k5": "<PERSON><PERSON>", "k6": "Number", "k7": "String", "k8": "String", "k9": "Number", "k10": "String", "k11": "<PERSON><PERSON>", "k12": "NumberLong", "k13": "String", "k14": "String", "k15": "Number", "k16": "String", "k17": "String", "k18": "String", "k19": "<PERSON><PERSON>", "k20": "String", "k21": "String", "k22": "Number", "k23": "String", "k24": "<PERSON><PERSON>", "k25": "String", "k26": "String", "k27": "String", "k28": "Number", "k29": "String", "k30": "Number", "k31": "Number", "k32": "String", "k33": "Number", "k34": "Number", "k35": "String", "k36": "Number", "k37": "Number", "k38": "Number", "k39": "Number", "k40": "Number", "k41": "Number", "k42": "String", "k43": "Number", "k44": "Number", "k45": "String", "k46": "Number", "k47": "Number", "k48": "String", "k49": "Number", "k50": "Number", "k51": "String", "k52": "Number", "k53": "Number", "k54": "Number", "k55": "Number", "k56": "String", "k57": "Number", "k58": "Number", "k59": "String", "k60": "Number", "k61": "Number", "k62": "String", "k63": "Number", "k64": "String", "k65": "Number", "k66": "Number", "k67": "Number", "k68": "String", "k69": "Number", "k70": "Number", "k71": "String", "k72": "Number", "k73": "String", "k74": "String", "k75": "<PERSON><PERSON>", "k76": "String", "k77": "String", "k78": "Number", "k79": "Number", "k80": "String", "k81": "String", "k82": "String", "k83": "String", "k84": "Number", "k85": "String", "k86": "String", "k87": "String", "k88": "<PERSON><PERSON>", "k89": "String", "k90": "String", "k91": "Number", "k92": "Number", "k93": "Number", "k94": "Number", "k95": "Number", "k96": "String", "k97": "String", "k98": "String", "k99": "String", "k100": "String", "k101": "String", "k102": "Number", "k103": "String", "k104": "String", "k105": "Number", "k106": "String", "k107": "String", "k108": "Number", "k109": "String", "k110": "String", "k111": "Number", "k112": "String", "k113": "String"}}, {"colName": "collection", "count": 2000, "size": 1664212, "storageSize": 253952, "totalIndexSize": 28672, "indexSizes": {"_id_": 28672}, "avgObjSize": 832, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "265dcc99-aa8f-4167-a83c-15781aa511ab"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "Number", "k6": "String", "k7": "<PERSON><PERSON>", "k8": "Number", "k9": "String", "k10": "String", "k11": "NumberLong", "k12": "Number", "k13": "String", "k14": "String", "k15": "<PERSON><PERSON>", "k16": "String", "k17": "<PERSON><PERSON>", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "Number", "k25": "String", "k26": "String", "k27": "String", "k28": "<PERSON><PERSON>", "k29": "Number", "k30": "String", "k31": "<PERSON><PERSON>", "k32": "Number", "k33": "<PERSON><PERSON>", "k34": "String", "k35": "String", "k36": "Number"}}, {"colName": "collection", "count": 1, "size": 1167, "storageSize": 32768, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 1167, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "d3720d8a-61ec-4c2e-8ccf-7b8d89b9be7f"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "String", "k2": "String", "k3": "BSON", "k4": "String", "k5": "String", "k6": "Date", "k7": "Number", "k8": "Number", "k9": "Number", "k10": "Number", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "Number", "k16": "String", "k17": "String", "k18": "Number", "k19": "String", "k20": "String", "k21": "Number", "k22": "Date", "k23": "String", "k24": "String", "k25": "Number", "k26": "Date", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "Number", "k32": "Date", "k33": "String", "k34": "Number", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "String", "k48": "String", "k49": "Date", "k50": "String", "k51": "String", "k52": "Number", "k53": "String", "k54": "String", "k55": "Number", "k56": "String", "k57": "Date", "k58": "String", "k59": "Date", "k60": "Number", "k61": "String", "k62": "String", "k63": "String", "k64": "String", "k65": "String"}}, {"colName": "collection", "count": 2308, "size": 1265200, "storageSize": 196608, "totalIndexSize": 32768, "indexSizes": {"_id_": 32768}, "avgObjSize": 548, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "44c35308-14e8-43d9-99ac-e391f85914c2"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "<PERSON><PERSON>", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "Number", "k6": "String", "k7": "<PERSON><PERSON>", "k8": "<PERSON><PERSON>", "k9": "<PERSON><PERSON>", "k10": "<PERSON><PERSON>", "k11": "<PERSON><PERSON>", "k12": "<PERSON><PERSON>", "k13": "NumberLong", "k14": "<PERSON><PERSON>", "k15": "<PERSON><PERSON>", "k16": "String", "k17": "<PERSON><PERSON>", "k18": "<PERSON><PERSON>", "k19": "Number", "k20": "<PERSON><PERSON>", "k21": "<PERSON><PERSON>", "k22": "<PERSON><PERSON>", "k23": "<PERSON><PERSON>", "k24": "<PERSON><PERSON>", "k25": "<PERSON><PERSON>", "k26": "<PERSON><PERSON>", "k27": "Number", "k28": "<PERSON><PERSON>", "k29": "<PERSON><PERSON>", "k30": "<PERSON><PERSON>", "k31": "<PERSON><PERSON>", "k32": "<PERSON><PERSON>", "k33": "String", "k34": "<PERSON><PERSON>", "k35": "<PERSON><PERSON>", "k36": "<PERSON><PERSON>", "k37": "<PERSON><PERSON>"}}, {"colName": "collection", "count": 1, "size": 911, "storageSize": 16384, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 911, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "ec0b32e2-cd7d-4613-9f37-791c640c1ab5"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "String", "k2": "String", "k3": "BSON", "k4": "String", "k5": "String", "k6": "String", "k7": "Number", "k8": "Number", "k9": "String", "k10": "String", "k11": "String", "k12": "Number", "k13": "String", "k14": "String", "k15": "String", "k16": "Number", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "Number", "k23": "Date", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "Date", "k38": "String", "k39": "String", "k40": "Date", "k41": "String", "k42": "String", "k43": "Number", "k44": "String", "k45": "Date", "k46": "String", "k47": "Number", "k48": "String", "k49": "String", "k50": "String"}}, {"colName": "collection", "count": 1, "size": 724, "storageSize": 16384, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 724, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "69e59a5d-7b18-4a91-ab1d-7b19eaf5d9a9"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "String", "k4": "Number", "k5": "Number", "k6": "String", "k7": "Number", "k8": "String", "k9": "String", "k10": "Number", "k11": "String", "k12": "String", "k13": "Number", "k14": "Date", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "Date", "k32": "String", "k33": "String", "k34": "Number", "k35": "String", "k36": "Date", "k37": "Number", "k38": "String", "k39": "String"}}, {"colName": "collection", "count": 238, "size": 191174, "storageSize": 40960, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 803, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "3b0891ac-5db0-4890-933a-c6089528a752"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "<PERSON><PERSON>", "k6": "Number", "k7": "String", "k8": "<PERSON><PERSON>", "k9": "String", "k10": "<PERSON><PERSON>", "k11": "NumberLong", "k12": "Number", "k13": "Number", "k14": "String", "k15": "String", "k16": "<PERSON><PERSON>", "k17": "<PERSON><PERSON>", "k18": "NumberLong", "k19": "<PERSON><PERSON>", "k20": "<PERSON><PERSON>", "k21": "<PERSON><PERSON>", "k22": "String", "k23": "<PERSON><PERSON>", "k24": "String", "k25": "String", "k26": "<PERSON><PERSON>", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "<PERSON><PERSON>", "k33": "String", "k34": "<PERSON><PERSON>", "k35": "<PERSON><PERSON>", "k36": "String", "k37": "String"}}, {"colName": "collection", "count": 14238, "size": 20406560, "storageSize": 5988352, "totalIndexSize": 139264, "indexSizes": {"_id_": 139264}, "avgObjSize": 1433, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "2c972568-f04d-4775-a397-b66027f9c604"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "Number", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "<PERSON><PERSON>", "k15": "Number", "k16": "String", "k17": "String", "k18": "NumberLong", "k19": "Number", "k20": "Number", "k21": "String", "k22": "String", "k23": "<PERSON><PERSON>", "k24": "Number", "k25": "String", "k26": "Number", "k27": "String", "k28": "<PERSON><PERSON>", "k29": "<PERSON><PERSON>", "k30": "<PERSON><PERSON>", "k31": "<PERSON><PERSON>", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "<PERSON><PERSON>", "k42": "Number", "k43": "String", "k44": "<PERSON><PERSON>", "k45": "Number", "k46": "<PERSON><PERSON>", "k47": "<PERSON><PERSON>", "k48": "String", "k49": "String", "k50": "String", "k51": "String", "k52": "String", "k53": "String", "k54": "String", "k55": "String", "k56": "String", "k57": "String", "k58": "<PERSON><PERSON>"}}, {"colName": "collection", "count": 1, "size": 1154, "storageSize": 36864, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 1154, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "a0c1f5d3-9ae1-451d-a1e9-3aa1cd95f442"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "String", "k2": "String", "k3": "BSON", "k4": "String", "k5": "String", "k6": "Date", "k7": "Number", "k8": "Number", "k9": "Number", "k10": "Number", "k11": "String", "k12": "String", "k13": "Number", "k14": "String", "k15": "Number", "k16": "String", "k17": "String", "k18": "Number", "k19": "String", "k20": "String", "k21": "Number", "k22": "Date", "k23": "String", "k24": "String", "k25": "Number", "k26": "Number", "k27": "Date", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "Number", "k33": "Date", "k34": "String", "k35": "Number", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "String", "k48": "Date", "k49": "String", "k50": "String", "k51": "Number", "k52": "String", "k53": "String", "k54": "Number", "k55": "String", "k56": "Date", "k57": "String", "k58": "Date", "k59": "Number", "k60": "String", "k61": "String", "k62": "String", "k63": "String", "k64": "String"}}, {"colName": "collection", "count": 205, "size": 245933, "storageSize": 49152, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 1199, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "4a9333dc-e42c-49cf-b05b-b187a57eecdb"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "<PERSON><PERSON>", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "<PERSON><PERSON>", "k6": "<PERSON><PERSON>", "k7": "<PERSON><PERSON>", "k8": "<PERSON><PERSON>", "k9": "<PERSON><PERSON>", "k10": "<PERSON><PERSON>", "k11": "<PERSON><PERSON>", "k12": "Number", "k13": "String", "k14": "<PERSON><PERSON>", "k15": "Number", "k16": "<PERSON><PERSON>", "k17": "<PERSON><PERSON>", "k18": "<PERSON><PERSON>", "k19": "Number", "k20": "<PERSON><PERSON>", "k21": "NumberLong", "k22": "<PERSON><PERSON>", "k23": "<PERSON><PERSON>", "k24": "<PERSON><PERSON>", "k25": "Number", "k26": "<PERSON><PERSON>", "k27": "String", "k28": "String", "k29": "<PERSON><PERSON>", "k30": "<PERSON><PERSON>", "k31": "<PERSON><PERSON>", "k32": "String", "k33": "<PERSON><PERSON>", "k34": "<PERSON><PERSON>", "k35": "<PERSON><PERSON>", "k36": "Number", "k37": "<PERSON><PERSON>", "k38": "<PERSON><PERSON>", "k39": "<PERSON><PERSON>", "k40": "<PERSON><PERSON>", "k41": "NumberLong", "k42": "Number", "k43": "String", "k44": "<PERSON><PERSON>", "k45": "<PERSON><PERSON>", "k46": "Number", "k47": "<PERSON><PERSON>", "k48": "<PERSON><PERSON>", "k49": "Number", "k50": "Number", "k51": "<PERSON><PERSON>", "k52": "<PERSON><PERSON>", "k53": "<PERSON><PERSON>", "k54": "<PERSON><PERSON>", "k55": "Number", "k56": "<PERSON><PERSON>", "k57": "<PERSON><PERSON>", "k58": "<PERSON><PERSON>", "k59": "<PERSON><PERSON>", "k60": "<PERSON><PERSON>", "k61": "<PERSON><PERSON>", "k62": "<PERSON><PERSON>", "k63": "<PERSON><PERSON>", "k64": "<PERSON><PERSON>", "k65": "Number", "k66": "<PERSON><PERSON>", "k67": "<PERSON><PERSON>", "k68": "<PERSON><PERSON>", "k69": "<PERSON><PERSON>", "k70": "<PERSON><PERSON>", "k71": "<PERSON><PERSON>", "k72": "<PERSON><PERSON>", "k73": "Number", "k74": "String", "k75": "<PERSON><PERSON>", "k76": "Number", "k77": "String", "k78": "<PERSON><PERSON>", "k79": "<PERSON><PERSON>", "k80": "Number", "k81": "Number", "k82": "<PERSON><PERSON>", "k83": "<PERSON><PERSON>", "k84": "<PERSON><PERSON>"}}, {"colName": "collection", "count": 183, "size": 135825, "storageSize": 36864, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 742, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "64d5eec7-9e14-4da1-86c5-b4431ccdead4"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "<PERSON><PERSON>", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "Number", "k6": "<PERSON><PERSON>", "k7": "<PERSON><PERSON>", "k8": "<PERSON><PERSON>", "k9": "<PERSON><PERSON>", "k10": "<PERSON><PERSON>", "k11": "<PERSON><PERSON>", "k12": "NumberLong", "k13": "Number", "k14": "String", "k15": "<PERSON><PERSON>", "k16": "<PERSON><PERSON>", "k17": "Number", "k18": "String", "k19": "<PERSON><PERSON>", "k20": "String", "k21": "<PERSON><PERSON>", "k22": "String", "k23": "<PERSON><PERSON>", "k24": "<PERSON><PERSON>", "k25": "<PERSON><PERSON>", "k26": "<PERSON><PERSON>", "k27": "<PERSON><PERSON>", "k28": "<PERSON><PERSON>", "k29": "<PERSON><PERSON>", "k30": "<PERSON><PERSON>", "k31": "<PERSON><PERSON>", "k32": "<PERSON><PERSON>", "k33": "<PERSON><PERSON>", "k34": "String", "k35": "<PERSON><PERSON>", "k36": "<PERSON><PERSON>", "k37": "<PERSON><PERSON>", "k38": "<PERSON><PERSON>", "k39": "<PERSON><PERSON>", "k40": "<PERSON><PERSON>", "k41": "Number"}}, {"colName": "collection", "count": 245, "size": 213183, "storageSize": 45056, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 870, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "e1049ef5-73ed-4f3f-971f-602f43557a7e"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "<PERSON><PERSON>", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "<PERSON><PERSON>", "k6": "String", "k7": "<PERSON><PERSON>", "k8": "String", "k9": "<PERSON><PERSON>", "k10": "Number", "k11": "<PERSON><PERSON>", "k12": "<PERSON><PERSON>", "k13": "String", "k14": "<PERSON><PERSON>", "k15": "<PERSON><PERSON>", "k16": "<PERSON><PERSON>", "k17": "<PERSON><PERSON>", "k18": "<PERSON><PERSON>", "k19": "<PERSON><PERSON>", "k20": "<PERSON><PERSON>", "k21": "<PERSON><PERSON>", "k22": "<PERSON><PERSON>", "k23": "<PERSON><PERSON>", "k24": "<PERSON><PERSON>", "k25": "<PERSON><PERSON>", "k26": "NumberLong", "k27": "String", "k28": "String", "k29": "<PERSON><PERSON>", "k30": "<PERSON><PERSON>", "k31": "String", "k32": "String", "k33": "<PERSON><PERSON>", "k34": "<PERSON><PERSON>", "k35": "<PERSON><PERSON>", "k36": "String", "k37": "<PERSON><PERSON>", "k38": "<PERSON><PERSON>", "k39": "<PERSON><PERSON>", "k40": "String", "k41": "<PERSON><PERSON>", "k42": "String", "k43": "<PERSON><PERSON>", "k44": "<PERSON><PERSON>", "k45": "<PERSON><PERSON>", "k46": "<PERSON><PERSON>", "k47": "<PERSON><PERSON>", "k48": "<PERSON><PERSON>", "k49": "<PERSON><PERSON>", "k50": "<PERSON><PERSON>", "k51": "String", "k52": "<PERSON><PERSON>", "k53": "Number", "k54": "String", "k55": "String", "k56": "<PERSON><PERSON>", "k57": "<PERSON><PERSON>", "k58": "<PERSON><PERSON>", "k59": "<PERSON><PERSON>", "k60": "<PERSON><PERSON>", "k61": "<PERSON><PERSON>", "k62": "String", "k63": "<PERSON><PERSON>", "k64": "<PERSON><PERSON>", "k65": "<PERSON><PERSON>"}}, {"colName": "collection", "count": 1, "size": 1173, "storageSize": 16384, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 1173, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "ae394902-36eb-40c9-8993-c125a17fbec4"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "String", "k2": "String", "k3": "String", "k4": "BSON", "k5": "String", "k6": "String", "k7": "Date", "k8": "Number", "k9": "Number", "k10": "Number", "k11": "Number", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "Number", "k17": "String", "k18": "String", "k19": "Number", "k20": "String", "k21": "String", "k22": "Number", "k23": "Date", "k24": "String", "k25": "String", "k26": "Number", "k27": "Date", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "Number", "k33": "Date", "k34": "String", "k35": "Number", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "String", "k48": "String", "k49": "String", "k50": "Number", "k51": "String", "k52": "String", "k53": "Number", "k54": "String", "k55": "Date", "k56": "String", "k57": "Date", "k58": "Date", "k59": "Number", "k60": "String", "k61": "String", "k62": "String", "k63": "String", "k64": "String", "k65": "String"}}, {"colName": "collection", "count": 6802, "size": 5182243, "storageSize": 765952, "totalIndexSize": 73728, "indexSizes": {"_id_": 73728}, "avgObjSize": 761, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "*************-4cd0-81e4-a87d3dc3bae6"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "String", "k6": "String", "k7": "String", "k8": "<PERSON><PERSON>", "k9": "String", "k10": "String", "k11": "Number", "k12": "String", "k13": "<PERSON><PERSON>", "k14": "NumberLong", "k15": "String", "k16": "<PERSON><PERSON>", "k17": "String", "k18": "String", "k19": "String", "k20": "<PERSON><PERSON>", "k21": "String", "k22": "<PERSON><PERSON>", "k23": "String", "k24": "String", "k25": "String", "k26": "<PERSON><PERSON>", "k27": "String", "k28": "String", "k29": "Number", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "Number", "k35": "String", "k36": "<PERSON><PERSON>", "k37": "Number", "k38": "Number", "k39": "String", "k40": "String", "k41": "String"}}, {"colName": "collection", "count": 10114, "size": 4321574, "storageSize": 659456, "totalIndexSize": 102400, "indexSizes": {"_id_": 102400}, "avgObjSize": 427, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "525aad91-b676-48f2-9338-0ba0127d6846"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "<PERSON><PERSON>", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "String", "k6": "String", "k7": "<PERSON><PERSON>", "k8": "<PERSON><PERSON>", "k9": "<PERSON><PERSON>", "k10": "<PERSON><PERSON>", "k11": "NumberLong", "k12": "<PERSON><PERSON>", "k13": "<PERSON><PERSON>", "k14": "String", "k15": "<PERSON><PERSON>", "k16": "<PERSON><PERSON>", "k17": "<PERSON><PERSON>", "k18": "<PERSON><PERSON>", "k19": "<PERSON><PERSON>", "k20": "<PERSON><PERSON>", "k21": "<PERSON><PERSON>", "k22": "<PERSON><PERSON>", "k23": "<PERSON><PERSON>", "k24": "<PERSON><PERSON>", "k25": "<PERSON><PERSON>", "k26": "<PERSON><PERSON>", "k27": "String", "k28": "<PERSON><PERSON>", "k29": "<PERSON><PERSON>", "k30": "<PERSON><PERSON>", "k31": "<PERSON><PERSON>"}}, {"colName": "collection", "count": 411, "size": 243605, "storageSize": 53248, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 592, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "0931f3a5-2ae6-407e-a65a-6305327c4d45"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "<PERSON><PERSON>", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "<PERSON><PERSON>", "k6": "<PERSON><PERSON>", "k7": "<PERSON><PERSON>", "k8": "<PERSON><PERSON>", "k9": "String", "k10": "<PERSON><PERSON>", "k11": "Number", "k12": "<PERSON><PERSON>", "k13": "<PERSON><PERSON>", "k14": "NumberLong", "k15": "<PERSON><PERSON>", "k16": "<PERSON><PERSON>", "k17": "<PERSON><PERSON>", "k18": "String", "k19": "<PERSON><PERSON>", "k20": "<PERSON><PERSON>", "k21": "<PERSON><PERSON>", "k22": "Number", "k23": "String", "k24": "<PERSON><PERSON>", "k25": "<PERSON><PERSON>", "k26": "<PERSON><PERSON>", "k27": "<PERSON><PERSON>", "k28": "<PERSON><PERSON>", "k29": "<PERSON><PERSON>", "k30": "<PERSON><PERSON>", "k31": "<PERSON><PERSON>", "k32": "<PERSON><PERSON>", "k33": "<PERSON><PERSON>", "k34": "String", "k35": "<PERSON><PERSON>", "k36": "Number", "k37": "String", "k38": "<PERSON><PERSON>", "k39": "<PERSON><PERSON>", "k40": "<PERSON><PERSON>", "k41": "<PERSON><PERSON>"}}, {"colName": "collection", "count": 3000, "size": 2648583, "storageSize": 397312, "totalIndexSize": 40960, "indexSizes": {"_id_": 40960}, "avgObjSize": 882, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "c5c6f06b-105a-4703-b26a-11f3beccc235"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "Number", "k6": "String", "k7": "<PERSON><PERSON>", "k8": "Number", "k9": "String", "k10": "String", "k11": "NumberLong", "k12": "Number", "k13": "String", "k14": "Number", "k15": "Number", "k16": "String", "k17": "<PERSON><PERSON>", "k18": "Number", "k19": "String", "k20": "<PERSON><PERSON>", "k21": "Number", "k22": "Number", "k23": "<PERSON><PERSON>", "k24": "<PERSON><PERSON>", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "<PERSON><PERSON>", "k35": "Number", "k36": "String", "k37": "<PERSON><PERSON>", "k38": "<PERSON><PERSON>", "k39": "String", "k40": "String"}}, {"colName": "collection", "count": 1, "size": 873, "storageSize": 32768, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 873, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "3e668a3a-e119-42d7-b69c-26bc85b273d4"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "String", "k2": "BSON", "k3": "String", "k4": "Number", "k5": "Number", "k6": "Number", "k7": "String", "k8": "String", "k9": "Number", "k10": "String", "k11": "String", "k12": "Number", "k13": "Date", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "Number", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "Date", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "Number", "k39": "Date", "k40": "String", "k41": "Number", "k42": "String", "k43": "String", "k44": "String"}}, {"colName": "collection", "count": 1, "size": 980, "storageSize": 16384, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 980, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "b81db47d-c59f-47b4-bd78-a5d9dec9a137"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "String", "k4": "Number", "k5": "Number", "k6": "String", "k7": "String", "k8": "Number", "k9": "Number", "k10": "String", "k11": "Number", "k12": "String", "k13": "String", "k14": "Number", "k15": "String", "k16": "String", "k17": "Number", "k18": "String", "k19": "String", "k20": "Date", "k21": "String", "k22": "String", "k23": "String", "k24": "Number", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "Date", "k47": "String", "k48": "String", "k49": "Number", "k50": "String", "k51": "Date", "k52": "String", "k53": "Number", "k54": "String", "k55": "String", "k56": "String"}}, {"colName": "collection", "count": 1, "size": 755, "storageSize": 16384, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 755, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "*************-4f35-a51d-abbc71ec5913"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "Number", "k4": "Number", "k5": "Date", "k6": "Number", "k7": "String", "k8": "Date", "k9": "Number", "k10": "Date", "k11": "String", "k12": "String", "k13": "Number", "k14": "String", "k15": "String", "k16": "Number", "k17": "Date", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "Number", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "Date", "k33": "String", "k34": "String", "k35": "Number", "k36": "Date", "k37": "Date", "k38": "Number", "k39": "String", "k40": "String"}}, {"colName": "collection", "count": 1, "size": 863, "storageSize": 16384, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 863, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "7236ce22-43cd-4b84-97e8-0059bda070df"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "String", "k4": "Number", "k5": "Number", "k6": "Number", "k7": "Number", "k8": "Number", "k9": "Date", "k10": "Number", "k11": "String", "k12": "String", "k13": "Number", "k14": "Number", "k15": "String", "k16": "String", "k17": "Number", "k18": "Date", "k19": "String", "k20": "String", "k21": "Number", "k22": "String", "k23": "String", "k24": "Number", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "Date", "k40": "String", "k41": "String", "k42": "Number", "k43": "Date", "k44": "String", "k45": "Number", "k46": "String", "k47": "String", "k48": "String"}}, {"colName": "collection", "count": 35, "size": 17797, "storageSize": 16384, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 508, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "f7cbe690-de32-4fae-b7cd-cd94d0504543"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "<PERSON><PERSON>", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "<PERSON><PERSON>", "k6": "<PERSON><PERSON>", "k7": "<PERSON><PERSON>", "k8": "<PERSON><PERSON>", "k9": "NumberLong", "k10": "<PERSON><PERSON>", "k11": "<PERSON><PERSON>", "k12": "String", "k13": "<PERSON><PERSON>", "k14": "<PERSON><PERSON>", "k15": "String", "k16": "String", "k17": "String", "k18": "Number", "k19": "<PERSON><PERSON>", "k20": "<PERSON><PERSON>", "k21": "<PERSON><PERSON>", "k22": "<PERSON><PERSON>", "k23": "<PERSON><PERSON>", "k24": "<PERSON><PERSON>", "k25": "<PERSON><PERSON>", "k26": "<PERSON><PERSON>", "k27": "<PERSON><PERSON>", "k28": "<PERSON><PERSON>", "k29": "String", "k30": "<PERSON><PERSON>", "k31": "<PERSON><PERSON>", "k32": "<PERSON><PERSON>", "k33": "<PERSON><PERSON>"}}, {"colName": "collection", "count": 1, "size": 1155, "storageSize": 32768, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 1155, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "82dd7598-7d65-4588-9db7-b5ba006c8a44"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "String", "k2": "String", "k3": "String", "k4": "BSON", "k5": "String", "k6": "String", "k7": "Date", "k8": "Number", "k9": "Number", "k10": "Number", "k11": "Number", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "Number", "k17": "String", "k18": "String", "k19": "Number", "k20": "String", "k21": "String", "k22": "Number", "k23": "Date", "k24": "String", "k25": "String", "k26": "Number", "k27": "Date", "k28": "String", "k29": "String", "k30": "String", "k31": "Number", "k32": "Date", "k33": "String", "k34": "Number", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "String", "k48": "String", "k49": "Number", "k50": "String", "k51": "String", "k52": "Number", "k53": "String", "k54": "Date", "k55": "String", "k56": "Date", "k57": "Date", "k58": "Number", "k59": "String", "k60": "String", "k61": "String", "k62": "String", "k63": "String", "k64": "String"}}, {"colName": "collection", "count": 1203, "size": 727601, "storageSize": 118784, "totalIndexSize": 24576, "indexSizes": {"_id_": 24576}, "avgObjSize": 604, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "3a415730-8ddf-4463-b59e-5f64c04f11a4"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "Number", "k6": "String", "k7": "<PERSON><PERSON>", "k8": "<PERSON><PERSON>", "k9": "Number", "k10": "String", "k11": "<PERSON><PERSON>", "k12": "NumberLong", "k13": "Number", "k14": "String", "k15": "String", "k16": "<PERSON><PERSON>", "k17": "<PERSON><PERSON>", "k18": "<PERSON><PERSON>", "k19": "String", "k20": "String", "k21": "String", "k22": "<PERSON><PERSON>", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "<PERSON><PERSON>", "k29": "Number", "k30": "String", "k31": "Number", "k32": "String", "k33": "<PERSON><PERSON>", "k34": "<PERSON><PERSON>", "k35": "String", "k36": "String"}}, {"colName": "collection", "count": 15000, "size": 8543600, "storageSize": 1265664, "totalIndexSize": 143360, "indexSizes": {"_id_": 143360}, "avgObjSize": 569, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "62cd0141-d2da-4ff4-bb59-************"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "Number", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "<PERSON><PERSON>", "k6": "<PERSON><PERSON>", "k7": "String", "k8": "<PERSON><PERSON>", "k9": "Number", "k10": "<PERSON><PERSON>", "k11": "<PERSON><PERSON>", "k12": "NumberLong", "k13": "<PERSON><PERSON>", "k14": "<PERSON><PERSON>", "k15": "String", "k16": "<PERSON><PERSON>", "k17": "<PERSON><PERSON>", "k18": "<PERSON><PERSON>", "k19": "<PERSON><PERSON>", "k20": "<PERSON><PERSON>", "k21": "<PERSON><PERSON>", "k22": "String", "k23": "<PERSON><PERSON>", "k24": "<PERSON><PERSON>", "k25": "<PERSON><PERSON>", "k26": "<PERSON><PERSON>", "k27": "<PERSON><PERSON>", "k28": "<PERSON><PERSON>", "k29": "<PERSON><PERSON>", "k30": "String", "k31": "<PERSON><PERSON>", "k32": "Number", "k33": "String", "k34": "<PERSON><PERSON>", "k35": "<PERSON><PERSON>", "k36": "<PERSON><PERSON>", "k37": "<PERSON><PERSON>"}}, {"colName": "collection", "count": 71, "size": 36337, "storageSize": 20480, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 511, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "64c5c85a-2820-4921-b825-b0c725971635"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "<PERSON><PERSON>", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "<PERSON><PERSON>", "k6": "<PERSON><PERSON>", "k7": "<PERSON><PERSON>", "k8": "String", "k9": "<PERSON><PERSON>", "k10": "Number", "k11": "<PERSON><PERSON>", "k12": "<PERSON><PERSON>", "k13": "NumberLong", "k14": "<PERSON><PERSON>", "k15": "<PERSON><PERSON>", "k16": "String", "k17": "<PERSON><PERSON>", "k18": "<PERSON><PERSON>", "k19": "String", "k20": "<PERSON><PERSON>", "k21": "<PERSON><PERSON>", "k22": "<PERSON><PERSON>", "k23": "<PERSON><PERSON>", "k24": "<PERSON><PERSON>", "k25": "<PERSON><PERSON>", "k26": "<PERSON><PERSON>", "k27": "<PERSON><PERSON>", "k28": "<PERSON><PERSON>", "k29": "String", "k30": "<PERSON><PERSON>", "k31": "Number", "k32": "String", "k33": "<PERSON><PERSON>", "k34": "<PERSON><PERSON>", "k35": "<PERSON><PERSON>", "k36": "<PERSON><PERSON>"}}, {"colName": "collection", "count": 1, "size": 633, "storageSize": 36864, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 633, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "dbf313d0-7075-42ef-aa9a-175bdef8d82e"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "Number", "k4": "Number", "k5": "Number", "k6": "String", "k7": "String", "k8": "Number", "k9": "String", "k10": "String", "k11": "String", "k12": "Number", "k13": "Date", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "Date", "k27": "String", "k28": "String", "k29": "Number", "k30": "Date", "k31": "Number", "k32": "Number", "k33": "String", "k34": "String"}}, {"colName": "collection", "count": 1, "size": 710, "storageSize": 16384, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 710, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "3b697098-5ba6-417a-adf3-94fa99ae6d2f"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "Number", "k4": "Number", "k5": "String", "k6": "Number", "k7": "String", "k8": "String", "k9": "Number", "k10": "String", "k11": "String", "k12": "Number", "k13": "Date", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "Date", "k30": "String", "k31": "String", "k32": "Number", "k33": "Date", "k34": "Number", "k35": "String", "k36": "String", "k37": "String", "k38": "Number", "k39": "String"}}, {"colName": "collection", "count": 1, "size": 638, "storageSize": 36864, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 638, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "700ebf89-c8be-45ff-bf7a-16fc55f922ac"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "Number", "k4": "Number", "k5": "Number", "k6": "String", "k7": "String", "k8": "Number", "k9": "String", "k10": "String", "k11": "String", "k12": "Number", "k13": "Date", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "Date", "k28": "String", "k29": "String", "k30": "Number", "k31": "Date", "k32": "Number", "k33": "String", "k34": "String"}}, {"colName": "collection", "count": 1, "size": 724, "storageSize": 32768, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 724, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "511a98cb-e44b-4810-b061-44b825ca2194"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "String", "k4": "Number", "k5": "Number", "k6": "Number", "k7": "Number", "k8": "String", "k9": "String", "k10": "String", "k11": "Number", "k12": "String", "k13": "String", "k14": "Number", "k15": "Date", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "Date", "k31": "String", "k32": "String", "k33": "Number", "k34": "String", "k35": "Date", "k36": "Number", "k37": "String", "k38": "String", "k39": "String"}}, {"colName": "collection", "count": 1, "size": 689, "storageSize": 32768, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 689, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "a060b0c8-0f81-42b3-8226-5fbcba51e71f"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "String", "k2": "BSON", "k3": "String", "k4": "Number", "k5": "Number", "k6": "Number", "k7": "String", "k8": "String", "k9": "Number", "k10": "String", "k11": "String", "k12": "Number", "k13": "Date", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "Date", "k29": "String", "k30": "String", "k31": "Number", "k32": "Date", "k33": "Number", "k34": "String", "k35": "String", "k36": "String"}}, {"colName": "collection", "count": 80, "size": 65213, "storageSize": 49152, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 815, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "545bbb01-9092-48d9-ad31-a7d34644a3cc"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "Number", "k6": "String", "k7": "<PERSON><PERSON>", "k8": "<PERSON><PERSON>", "k9": "String", "k10": "Number", "k11": "Number", "k12": "String", "k13": "<PERSON><PERSON>", "k14": "NumberLong", "k15": "Number", "k16": "String", "k17": "String", "k18": "<PERSON><PERSON>", "k19": "<PERSON><PERSON>", "k20": "<PERSON><PERSON>", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "<PERSON><PERSON>", "k31": "<PERSON><PERSON>", "k32": "String", "k33": "Number", "k34": "Number", "k35": "String", "k36": "<PERSON><PERSON>", "k37": "<PERSON><PERSON>", "k38": "String", "k39": "String"}}, {"colName": "collection", "count": 1, "size": 965, "storageSize": 32768, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 965, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "a29df2db-5763-4984-906d-0ba8f98f2fc7"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "String", "k2": "String", "k3": "BSON", "k4": "String", "k5": "String", "k6": "Number", "k7": "String", "k8": "Number", "k9": "Number", "k10": "Date", "k11": "Number", "k12": "String", "k13": "String", "k14": "Number", "k15": "String", "k16": "String", "k17": "Number", "k18": "Date", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "Date", "k45": "String", "k46": "String", "k47": "Number", "k48": "Date", "k49": "String", "k50": "Number", "k51": "String", "k52": "String"}}, {"colName": "collection", "count": 1, "size": 1389, "storageSize": 16384, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 1389, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "2faad70e-3c0c-4c5f-bac2-48dfc17565c5"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "String", "k2": "String", "k3": "BSON", "k4": "String", "k5": "String", "k6": "Date", "k7": "Number", "k8": "Number", "k9": "Number", "k10": "Number", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "Number", "k21": "String", "k22": "String", "k23": "String", "k24": "Number", "k25": "String", "k26": "String", "k27": "String", "k28": "Number", "k29": "Date", "k30": "String", "k31": "String", "k32": "Number", "k33": "Date", "k34": "String", "k35": "String", "k36": "Date", "k37": "String", "k38": "String", "k39": "Number", "k40": "Date", "k41": "String", "k42": "Number", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "String", "k48": "String", "k49": "String", "k50": "String", "k51": "String", "k52": "String", "k53": "String", "k54": "String", "k55": "String", "k56": "Date", "k57": "String", "k58": "String", "k59": "Number", "k60": "String", "k61": "String", "k62": "Number", "k63": "String", "k64": "Date", "k65": "String", "k66": "Date", "k67": "String", "k68": "String", "k69": "Number", "k70": "String", "k71": "String", "k72": "String", "k73": "String", "k74": "String", "k75": "String"}}, {"colName": "collection", "count": 1, "size": 1689, "storageSize": 32768, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 1689, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "244dd756-0965-445b-9616-7b5defd4700b"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "Date", "k4": "Number", "k5": "Number", "k6": "Number", "k7": "String", "k8": "Date", "k9": "String", "k10": "String", "k11": "String", "k12": "Number", "k13": "String", "k14": "String", "k15": "Number", "k16": "String", "k17": "String", "k18": "String", "k19": "Number", "k20": "String", "k21": "Number", "k22": "Date", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "String", "k48": "String", "k49": "String", "k50": "String", "k51": "String", "k52": "Date", "k53": "String", "k54": "String", "k55": "String", "k56": "Number", "k57": "String", "k58": "String", "k59": "Date", "k60": "Date", "k61": "String", "k62": "String", "k63": "String", "k64": "String", "k65": "String", "k66": "String", "k67": "String", "k68": "String", "k69": "String", "k70": "String", "k71": "String", "k72": "Date", "k73": "String", "k74": "String", "k75": "Number", "k76": "Date", "k77": "Number", "k78": "Date", "k79": "Date", "k80": "Number", "k81": "String", "k82": "String"}}, {"colName": "collection", "count": 25, "size": 33444, "storageSize": 20480, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 1337, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "e56b81a3-d025-4faa-8a14-3b98e9b51f6a"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "<PERSON><PERSON>", "k3": "<PERSON><PERSON>", "k4": "Number", "k5": "String", "k6": "Number", "k7": "<PERSON><PERSON>", "k8": "<PERSON><PERSON>", "k9": "String", "k10": "Number", "k11": "<PERSON><PERSON>", "k12": "<PERSON><PERSON>", "k13": "<PERSON><PERSON>", "k14": "<PERSON><PERSON>", "k15": "<PERSON><PERSON>", "k16": "<PERSON><PERSON>", "k17": "<PERSON><PERSON>", "k18": "<PERSON><PERSON>", "k19": "Number", "k20": "<PERSON><PERSON>", "k21": "String", "k22": "<PERSON><PERSON>", "k23": "<PERSON><PERSON>", "k24": "<PERSON><PERSON>", "k25": "String", "k26": "<PERSON><PERSON>", "k27": "<PERSON><PERSON>", "k28": "<PERSON><PERSON>", "k29": "<PERSON><PERSON>", "k30": "String", "k31": "<PERSON><PERSON>", "k32": "<PERSON><PERSON>", "k33": "<PERSON><PERSON>", "k34": "<PERSON><PERSON>", "k35": "NumberLong", "k36": "<PERSON><PERSON>", "k37": "<PERSON><PERSON>", "k38": "<PERSON><PERSON>", "k39": "Number", "k40": "<PERSON><PERSON>", "k41": "<PERSON><PERSON>", "k42": "<PERSON><PERSON>", "k43": "String", "k44": "<PERSON><PERSON>", "k45": "<PERSON><PERSON>", "k46": "<PERSON><PERSON>", "k47": "Number", "k48": "String", "k49": "<PERSON><PERSON>", "k50": "Number", "k51": "<PERSON><PERSON>", "k52": "Number", "k53": "Number", "k54": "Number", "k55": "<PERSON><PERSON>", "k56": "NumberLong", "k57": "Number", "k58": "Number", "k59": "String", "k60": "<PERSON><PERSON>", "k61": "Number", "k62": "<PERSON><PERSON>", "k63": "<PERSON><PERSON>", "k64": "<PERSON><PERSON>", "k65": "<PERSON><PERSON>", "k66": "Number", "k67": "<PERSON><PERSON>", "k68": "<PERSON><PERSON>", "k69": "<PERSON><PERSON>", "k70": "<PERSON><PERSON>", "k71": "String", "k72": "Number", "k73": "Number", "k74": "<PERSON><PERSON>", "k75": "<PERSON><PERSON>", "k76": "String", "k77": "String", "k78": "String", "k79": "String", "k80": "String", "k81": "String", "k82": "String", "k83": "String", "k84": "<PERSON><PERSON>", "k85": "<PERSON><PERSON>", "k86": "String", "k87": "<PERSON><PERSON>", "k88": "<PERSON><PERSON>", "k89": "<PERSON><PERSON>", "k90": "<PERSON><PERSON>", "k91": "<PERSON><PERSON>", "k92": "<PERSON><PERSON>", "k93": "Number", "k94": "<PERSON><PERSON>", "k95": "<PERSON><PERSON>", "k96": "<PERSON><PERSON>", "k97": "String", "k98": "String", "k99": "String", "k100": "Number", "k101": "<PERSON><PERSON>"}}, {"colName": "collection", "count": 1, "size": 620, "storageSize": 16384, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 620, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "d90508ac-df30-43ad-82ed-ec5a42bbdb97"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "Number", "k4": "Number", "k5": "Number", "k6": "String", "k7": "String", "k8": "Number", "k9": "String", "k10": "String", "k11": "Number", "k12": "Date", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "Date", "k27": "String", "k28": "String", "k29": "Number", "k30": "Date", "k31": "Number", "k32": "String", "k33": "String"}}, {"colName": "collection", "count": 75, "size": 45818, "storageSize": 20480, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 610, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "a516d753-f0b9-4169-8e6f-19f7ee0459c3"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "String", "k4": "<PERSON><PERSON>", "k5": "<PERSON><PERSON>", "k6": "String", "k7": "<PERSON><PERSON>", "k8": "String", "k9": "String", "k10": "<PERSON><PERSON>", "k11": "NumberLong", "k12": "<PERSON><PERSON>", "k13": "String", "k14": "String", "k15": "String", "k16": "<PERSON><PERSON>", "k17": "<PERSON><PERSON>", "k18": "String", "k19": "String", "k20": "<PERSON><PERSON>", "k21": "<PERSON><PERSON>", "k22": "String", "k23": "String", "k24": "Number", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "<PERSON><PERSON>", "k30": "String", "k31": "Number", "k32": "String", "k33": "String"}}, {"colName": "collection", "count": 128, "size": 92743, "storageSize": 28672, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 724, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "3c204d49-d960-43f3-8bff-8e5fc83c3a97"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "String", "k6": "Number", "k7": "String", "k8": "String", "k9": "<PERSON><PERSON>", "k10": "String", "k11": "String", "k12": "Number", "k13": "String", "k14": "<PERSON><PERSON>", "k15": "NumberLong", "k16": "String", "k17": "<PERSON><PERSON>", "k18": "String", "k19": "String", "k20": "String", "k21": "<PERSON><PERSON>", "k22": "<PERSON><PERSON>", "k23": "String", "k24": "String", "k25": "<PERSON><PERSON>", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "Number", "k33": "String", "k34": "<PERSON><PERSON>", "k35": "Number", "k36": "String", "k37": "String", "k38": "String"}}, {"colName": "collection", "count": 1, "size": 703, "storageSize": 16384, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 703, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "4b4e7237-6f26-4040-99eb-ed3b077f0e2e"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "Number", "k4": "Number", "k5": "String", "k6": "Number", "k7": "String", "k8": "String", "k9": "Number", "k10": "Number", "k11": "Number", "k12": "String", "k13": "String", "k14": "Number", "k15": "Date", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "Date", "k30": "String", "k31": "String", "k32": "Number", "k33": "Number", "k34": "Number", "k35": "Date", "k36": "Number", "k37": "String", "k38": "String"}}, {"colName": "collection", "count": 30, "size": 41286, "storageSize": 24576, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 1376, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "ec8450f9-21ec-4781-9d4e-292a7f856ece"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "<PERSON><PERSON>", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "String", "k6": "String", "k7": "<PERSON><PERSON>", "k8": "<PERSON><PERSON>", "k9": "<PERSON><PERSON>", "k10": "<PERSON><PERSON>", "k11": "<PERSON><PERSON>", "k12": "<PERSON><PERSON>", "k13": "<PERSON><PERSON>", "k14": "<PERSON><PERSON>", "k15": "<PERSON><PERSON>", "k16": "<PERSON><PERSON>", "k17": "<PERSON><PERSON>", "k18": "<PERSON><PERSON>", "k19": "<PERSON><PERSON>", "k20": "<PERSON><PERSON>", "k21": "<PERSON><PERSON>", "k22": "<PERSON><PERSON>", "k23": "<PERSON><PERSON>", "k24": "<PERSON><PERSON>", "k25": "<PERSON><PERSON>", "k26": "String", "k27": "Number", "k28": "String", "k29": "<PERSON><PERSON>", "k30": "Number", "k31": "String", "k32": "String", "k33": "NumberLong", "k34": "NumberLong", "k35": "<PERSON><PERSON>", "k36": "<PERSON><PERSON>", "k37": "String", "k38": "<PERSON><PERSON>", "k39": "NumberLong", "k40": "Number", "k41": "String", "k42": "String", "k43": "<PERSON><PERSON>", "k44": "<PERSON><PERSON>", "k45": "<PERSON><PERSON>", "k46": "<PERSON><PERSON>", "k47": "<PERSON><PERSON>", "k48": "<PERSON><PERSON>", "k49": "<PERSON><PERSON>", "k50": "<PERSON><PERSON>", "k51": "<PERSON><PERSON>", "k52": "String", "k53": "<PERSON><PERSON>", "k54": "<PERSON><PERSON>", "k55": "<PERSON><PERSON>", "k56": "<PERSON><PERSON>", "k57": "<PERSON><PERSON>", "k58": "<PERSON><PERSON>", "k59": "<PERSON><PERSON>", "k60": "<PERSON><PERSON>", "k61": "String", "k62": "String", "k63": "String", "k64": "String", "k65": "String", "k66": "String", "k67": "String", "k68": "String", "k69": "String", "k70": "<PERSON><PERSON>", "k71": "Number", "k72": "String", "k73": "<PERSON><PERSON>", "k74": "String", "k75": "<PERSON><PERSON>", "k76": "<PERSON><PERSON>", "k77": "String", "k78": "String", "k79": "<PERSON><PERSON>", "k80": "<PERSON><PERSON>"}}, {"colName": "collection", "count": 13, "size": 15893, "storageSize": 16384, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 1222, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "1e946b0e-c7c2-4908-ae31-fde2476b18ce"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "<PERSON><PERSON>", "k3": "String", "k4": "<PERSON><PERSON>", "k5": "<PERSON><PERSON>", "k6": "<PERSON><PERSON>", "k7": "<PERSON><PERSON>", "k8": "<PERSON><PERSON>", "k9": "<PERSON><PERSON>", "k10": "Number", "k11": "<PERSON><PERSON>", "k12": "String", "k13": "<PERSON><PERSON>", "k14": "Number", "k15": "Number", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "NumberLong", "k22": "Number", "k23": "String", "k24": "String", "k25": "<PERSON><PERSON>", "k26": "String", "k27": "<PERSON><PERSON>", "k28": "<PERSON><PERSON>", "k29": "<PERSON><PERSON>", "k30": "String", "k31": "<PERSON><PERSON>", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "<PERSON><PERSON>", "k38": "String", "k39": "String", "k40": "String", "k41": "<PERSON><PERSON>", "k42": "Number", "k43": "<PERSON><PERSON>", "k44": "String", "k45": "<PERSON><PERSON>", "k46": "String", "k47": "<PERSON><PERSON>", "k48": "String", "k49": "<PERSON><PERSON>", "k50": "<PERSON><PERSON>", "k51": "String", "k52": "String", "k53": "<PERSON><PERSON>", "k54": "<PERSON><PERSON>", "k55": "<PERSON><PERSON>"}}, {"colName": "collection", "count": 485, "size": 435920, "storageSize": 77824, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 898, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "2a6eb3d0-f70f-43df-92ac-d0f95e60aaf9"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "Number", "k3": "String", "k4": "<PERSON><PERSON>", "k5": "<PERSON><PERSON>", "k6": "<PERSON><PERSON>", "k7": "<PERSON><PERSON>", "k8": "Number", "k9": "Number", "k10": "String", "k11": "<PERSON><PERSON>", "k12": "Number", "k13": "String", "k14": "String", "k15": "NumberLong", "k16": "Number", "k17": "String", "k18": "NumberLong", "k19": "String", "k20": "<PERSON><PERSON>", "k21": "String", "k22": "Number", "k23": "<PERSON><PERSON>", "k24": "<PERSON><PERSON>", "k25": "String", "k26": "<PERSON><PERSON>", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "<PERSON><PERSON>", "k36": "Number", "k37": "String", "k38": "<PERSON><PERSON>", "k39": "<PERSON><PERSON>", "k40": "<PERSON><PERSON>", "k41": "String", "k42": "String", "k43": "Number"}}, {"colName": "collection", "count": 3000, "size": 2347506, "storageSize": 356352, "totalIndexSize": 40960, "indexSizes": {"_id_": 40960}, "avgObjSize": 782, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "ece98360-1c88-4996-b8df-46caac98e7b9"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "Number", "k6": "Number", "k7": "Number", "k8": "Number", "k9": "String", "k10": "<PERSON><PERSON>", "k11": "String", "k12": "String", "k13": "Number", "k14": "String", "k15": "<PERSON><PERSON>", "k16": "NumberLong", "k17": "String", "k18": "Number", "k19": "<PERSON><PERSON>", "k20": "String", "k21": "String", "k22": "String", "k23": "<PERSON><PERSON>", "k24": "Number", "k25": "<PERSON><PERSON>", "k26": "String", "k27": "String", "k28": "String", "k29": "<PERSON><PERSON>", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "Number", "k37": "String", "k38": "<PERSON><PERSON>", "k39": "Number", "k40": "String", "k41": "String", "k42": "Number"}}, {"colName": "collection", "count": 1, "size": 752, "storageSize": 16384, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 752, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "0099ccde-b1c5-4d57-b6f0-812550d5631b"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "String", "k2": "String", "k3": "Number", "k4": "BSON", "k5": "String", "k6": "Number", "k7": "Number", "k8": "String", "k9": "Number", "k10": "String", "k11": "String", "k12": "Number", "k13": "String", "k14": "String", "k15": "String", "k16": "Number", "k17": "Date", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "Number", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "Date", "k35": "String", "k36": "String", "k37": "Number", "k38": "Date", "k39": "Number", "k40": "String", "k41": "String", "k42": "Number", "k43": "Number"}}, {"colName": "collection", "count": 8, "size": 7085, "storageSize": 16384, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 885, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "bb8f8032-7ce9-4070-83a5-cf457efbfb15"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "Number", "k3": "String", "k4": "<PERSON><PERSON>", "k5": "<PERSON><PERSON>", "k6": "Number", "k7": "String", "k8": "<PERSON><PERSON>", "k9": "Number", "k10": "String", "k11": "String", "k12": "NumberLong", "k13": "Number", "k14": "String", "k15": "String", "k16": "<PERSON><PERSON>", "k17": "String", "k18": "<PERSON><PERSON>", "k19": "<PERSON><PERSON>", "k20": "Number", "k21": "Number", "k22": "NumberLong", "k23": "Number", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "<PERSON><PERSON>", "k34": "Number", "k35": "String", "k36": "<PERSON><PERSON>", "k37": "<PERSON><PERSON>", "k38": "String", "k39": "String", "k40": "<PERSON><PERSON>"}}, {"colName": "collection", "count": 8, "size": 7085, "storageSize": 16384, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 885, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "9537cb72-3d24-4848-803f-bbcb4d6b4fb6"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "Number", "k3": "String", "k4": "<PERSON><PERSON>", "k5": "<PERSON><PERSON>", "k6": "Number", "k7": "String", "k8": "<PERSON><PERSON>", "k9": "Number", "k10": "String", "k11": "String", "k12": "NumberLong", "k13": "Number", "k14": "<PERSON><PERSON>", "k15": "String", "k16": "<PERSON><PERSON>", "k17": "String", "k18": "<PERSON><PERSON>", "k19": "String", "k20": "<PERSON><PERSON>", "k21": "<PERSON><PERSON>", "k22": "NumberLong", "k23": "Number", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "<PERSON><PERSON>", "k34": "Number", "k35": "String", "k36": "<PERSON><PERSON>", "k37": "<PERSON><PERSON>", "k38": "String", "k39": "String", "k40": "String"}}, {"colName": "collection", "count": 738, "size": 1058065, "storageSize": 253952, "totalIndexSize": 20480, "indexSizes": {"_id_": 20480}, "avgObjSize": 1433, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "77e8e185-ef4d-44f9-ac44-ad32a6f5824d"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "<PERSON><PERSON>", "k6": "<PERSON><PERSON>", "k7": "<PERSON><PERSON>", "k8": "<PERSON><PERSON>", "k9": "<PERSON><PERSON>", "k10": "<PERSON><PERSON>", "k11": "<PERSON><PERSON>", "k12": "<PERSON><PERSON>", "k13": "Number", "k14": "<PERSON><PERSON>", "k15": "<PERSON><PERSON>", "k16": "String", "k17": "<PERSON><PERSON>", "k18": "Number", "k19": "String", "k20": "<PERSON><PERSON>", "k21": "<PERSON><PERSON>", "k22": "String", "k23": "<PERSON><PERSON>", "k24": "NumberLong", "k25": "Number", "k26": "String", "k27": "String", "k28": "<PERSON><PERSON>", "k29": "String", "k30": "<PERSON><PERSON>", "k31": "<PERSON><PERSON>", "k32": "<PERSON><PERSON>", "k33": "<PERSON><PERSON>", "k34": "<PERSON><PERSON>", "k35": "String", "k36": "<PERSON><PERSON>", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "<PERSON><PERSON>", "k47": "Number", "k48": "String", "k49": "String", "k50": "String", "k51": "String", "k52": "String", "k53": "String", "k54": "String", "k55": "String", "k56": "String", "k57": "<PERSON><PERSON>", "k58": "<PERSON><PERSON>", "k59": "String", "k60": "String", "k61": "<PERSON><PERSON>"}}, {"colName": "collection", "count": 5855, "size": 8978525, "storageSize": 2654208, "totalIndexSize": 65536, "indexSizes": {"_id_": 65536}, "avgObjSize": 1533, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "*************-405b-bbbc-7d072cbeb73b"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "<PERSON><PERSON>", "k3": "<PERSON><PERSON>", "k4": "String", "k5": "<PERSON><PERSON>", "k6": "<PERSON><PERSON>", "k7": "<PERSON><PERSON>", "k8": "<PERSON><PERSON>", "k9": "<PERSON><PERSON>", "k10": "<PERSON><PERSON>", "k11": "<PERSON><PERSON>", "k12": "<PERSON><PERSON>", "k13": "<PERSON><PERSON>", "k14": "<PERSON><PERSON>", "k15": "<PERSON><PERSON>", "k16": "<PERSON><PERSON>", "k17": "<PERSON><PERSON>", "k18": "<PERSON><PERSON>", "k19": "<PERSON><PERSON>", "k20": "<PERSON><PERSON>", "k21": "String", "k22": "Number", "k23": "<PERSON><PERSON>", "k24": "<PERSON><PERSON>", "k25": "String", "k26": "<PERSON><PERSON>", "k27": "Number", "k28": "String", "k29": "<PERSON><PERSON>", "k30": "<PERSON><PERSON>", "k31": "<PERSON><PERSON>", "k32": "String", "k33": "NumberLong", "k34": "String", "k35": "Number", "k36": "String", "k37": "<PERSON><PERSON>", "k38": "<PERSON><PERSON>", "k39": "<PERSON><PERSON>", "k40": "<PERSON><PERSON>", "k41": "String", "k42": "<PERSON><PERSON>", "k43": "String", "k44": "<PERSON><PERSON>", "k45": "<PERSON><PERSON>", "k46": "String", "k47": "<PERSON><PERSON>", "k48": "<PERSON><PERSON>", "k49": "String", "k50": "String", "k51": "String", "k52": "String", "k53": "String", "k54": "<PERSON><PERSON>", "k55": "<PERSON><PERSON>", "k56": "String", "k57": "String", "k58": "String", "k59": "<PERSON><PERSON>", "k60": "Number", "k61": "<PERSON><PERSON>", "k62": "<PERSON><PERSON>", "k63": "<PERSON><PERSON>", "k64": "<PERSON><PERSON>", "k65": "<PERSON><PERSON>", "k66": "String", "k67": "String", "k68": "Number", "k69": "<PERSON><PERSON>", "k70": "<PERSON><PERSON>", "k71": "<PERSON><PERSON>", "k72": "<PERSON><PERSON>", "k73": "<PERSON><PERSON>", "k74": "String", "k75": "<PERSON><PERSON>", "k76": "<PERSON><PERSON>", "k77": "<PERSON><PERSON>", "k78": "<PERSON><PERSON>", "k79": "<PERSON><PERSON>", "k80": "<PERSON><PERSON>", "k81": "String", "k82": "String"}}, {"colName": "collection", "count": 58, "size": 50258, "storageSize": 20480, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 866, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "8e6a23c9-6afa-4a61-bef3-8d0d295677ae"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "Number", "k4": "String", "k5": "<PERSON><PERSON>", "k6": "<PERSON><PERSON>", "k7": "Number", "k8": "<PERSON><PERSON>", "k9": "String", "k10": "String", "k11": "NumberLong", "k12": "Number", "k13": "<PERSON><PERSON>", "k14": "String", "k15": "<PERSON><PERSON>", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "<PERSON><PERSON>", "k21": "String", "k22": "Number", "k23": "String", "k24": "Number", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "<PERSON><PERSON>", "k33": "String", "k34": "<PERSON><PERSON>", "k35": "<PERSON><PERSON>", "k36": "String", "k37": "String"}}, {"colName": "collection", "count": 4000, "size": 3313431, "storageSize": 499712, "totalIndexSize": 49152, "indexSizes": {"_id_": 49152}, "avgObjSize": 828, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "8f09ffd9-6ac0-4e7a-bd9b-57d0b7611e8f"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "<PERSON><PERSON>", "k6": "<PERSON><PERSON>", "k7": "<PERSON><PERSON>", "k8": "<PERSON><PERSON>", "k9": "Number", "k10": "String", "k11": "<PERSON><PERSON>", "k12": "Number", "k13": "String", "k14": "String", "k15": "NumberLong", "k16": "Number", "k17": "<PERSON><PERSON>", "k18": "String", "k19": "<PERSON><PERSON>", "k20": "<PERSON><PERSON>", "k21": "<PERSON><PERSON>", "k22": "String", "k23": "String", "k24": "<PERSON><PERSON>", "k25": "<PERSON><PERSON>", "k26": "String", "k27": "<PERSON><PERSON>", "k28": "<PERSON><PERSON>", "k29": "<PERSON><PERSON>", "k30": "<PERSON><PERSON>", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "<PERSON><PERSON>", "k37": "String", "k38": "<PERSON><PERSON>", "k39": "<PERSON><PERSON>", "k40": "Number", "k41": "String", "k42": "<PERSON><PERSON>", "k43": "<PERSON><PERSON>", "k44": "ObjectId", "k45": "String", "k46": "String", "k47": "String"}}, {"colName": "collection", "count": 1, "size": 662, "storageSize": 16384, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 662, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "bf48f422-2ade-4361-bbd5-6bcfe9ed9017"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "String", "k2": "BSON", "k3": "String", "k4": "Number", "k5": "Number", "k6": "Number", "k7": "String", "k8": "String", "k9": "Number", "k10": "String", "k11": "String", "k12": "Number", "k13": "Date", "k14": "Number", "k15": "String", "k16": "String", "k17": "Number", "k18": "String", "k19": "String", "k20": "String", "k21": "Date", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "Number", "k32": "Date", "k33": "Date", "k34": "Number", "k35": "String", "k36": "String"}}, {"colName": "collection", "count": 1, "size": 1210, "storageSize": 32768, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 1210, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "555c1319-25f3-4cd9-9027-ea2ea1455b1d"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "String", "k2": "String", "k3": "BSON", "k4": "String", "k5": "String", "k6": "Date", "k7": "Number", "k8": "Number", "k9": "Number", "k10": "Number", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "Number", "k16": "String", "k17": "String", "k18": "Number", "k19": "String", "k20": "String", "k21": "Number", "k22": "Date", "k23": "String", "k24": "String", "k25": "Number", "k26": "Date", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "Number", "k32": "Date", "k33": "String", "k34": "Number", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "String", "k48": "String", "k49": "Date", "k50": "String", "k51": "String", "k52": "Number", "k53": "String", "k54": "String", "k55": "Number", "k56": "String", "k57": "Date", "k58": "String", "k59": "Date", "k60": "Number", "k61": "String", "k62": "String", "k63": "String", "k64": "String", "k65": "String", "k66": "String", "k67": "String"}}, {"colName": "collection", "count": 214, "size": 185459, "storageSize": 45056, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 866, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "af2f4a07-3b7e-4205-9f6c-88d51aa1d55f"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "String", "k6": "Number", "k7": "String", "k8": "<PERSON><PERSON>", "k9": "Number", "k10": "String", "k11": "String", "k12": "NumberLong", "k13": "Number", "k14": "<PERSON><PERSON>", "k15": "String", "k16": "<PERSON><PERSON>", "k17": "String", "k18": "String", "k19": "<PERSON><PERSON>", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "<PERSON><PERSON>", "k31": "Number", "k32": "String", "k33": "<PERSON><PERSON>", "k34": "String", "k35": "<PERSON><PERSON>", "k36": "String", "k37": "String"}}, {"colName": "collection", "count": 1, "size": 881, "storageSize": 16384, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 881, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "50cd2b39-3fb9-435e-90c7-8a0ee211ee6e"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "Number", "k9": "Number", "k10": "Number", "k11": "String", "k12": "String", "k13": "Number", "k14": "String", "k15": "String", "k16": "Number", "k17": "Date", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "Date", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "Number", "k40": "Date", "k41": "Date", "k42": "Number", "k43": "Number", "k44": "String", "k45": "String", "k46": "String"}}, {"colName": "collection", "count": 2000, "size": 1311698, "storageSize": 409600, "totalIndexSize": 28672, "indexSizes": {"_id_": 28672}, "avgObjSize": 655, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "3d0f9d36-377d-4f64-8cd9-18ad98830874"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "Number", "k6": "<PERSON><PERSON>", "k7": "String", "k8": "String", "k9": "Number", "k10": "String", "k11": "<PERSON><PERSON>", "k12": "NumberLong", "k13": "<PERSON><PERSON>", "k14": "String", "k15": "String", "k16": "String", "k17": "<PERSON><PERSON>", "k18": "<PERSON><PERSON>", "k19": "String", "k20": "String", "k21": "String", "k22": "<PERSON><PERSON>", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "Number", "k30": "String", "k31": "<PERSON><PERSON>", "k32": "Number", "k33": "Number", "k34": "String", "k35": "String"}}, {"colName": "collection", "count": 1, "size": 755, "storageSize": 16384, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 755, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "b560d19b-6add-4d3d-a58c-3e03693ebd2b"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "String", "k4": "Number", "k5": "Number", "k6": "Number", "k7": "Number", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "Number", "k16": "Date", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "Date", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "Number", "k37": "String", "k38": "String", "k39": "String", "k40": "String"}}, {"colName": "collection", "count": 3, "size": 2638, "storageSize": 16384, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 879, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "b47e89af-1f9e-413c-8093-47ad497552bd"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "String", "k6": "String", "k7": "Number", "k8": "String", "k9": "<PERSON><PERSON>", "k10": "Number", "k11": "String", "k12": "String", "k13": "NumberLong", "k14": "Number", "k15": "<PERSON><PERSON>", "k16": "String", "k17": "<PERSON><PERSON>", "k18": "String", "k19": "<PERSON><PERSON>", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "<PERSON><PERSON>", "k31": "Number", "k32": "String", "k33": "String", "k34": "<PERSON><PERSON>", "k35": "<PERSON><PERSON>", "k36": "String", "k37": "String"}}, {"colName": "collection", "count": 120, "size": 112880, "storageSize": 28672, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 940, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "d54bf934-007a-4171-b093-2eb8fbfa8b7f"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "Number", "k3": "String", "k4": "String", "k5": "String", "k6": "<PERSON><PERSON>", "k7": "<PERSON><PERSON>", "k8": "Number", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "NumberLong", "k14": "Number", "k15": "<PERSON><PERSON>", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "<PERSON><PERSON>", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "<PERSON><PERSON>", "k33": "Number", "k34": "String", "k35": "String", "k36": "String", "k37": "Number", "k38": "Number"}}, {"colName": "collection", "count": 14, "size": 13834, "storageSize": 16384, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 988, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "5983a92e-8ed8-4ef1-976c-691cd446e7df"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String"}}, {"colName": "collection", "count": 1, "size": 1172, "storageSize": 16384, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 1172, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "a351904e-f31f-4c49-b6e3-1ef58262edd8"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "String", "k2": "String", "k3": "BSON", "k4": "String", "k5": "String", "k6": "Date", "k7": "Number", "k8": "Number", "k9": "Number", "k10": "Number", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "Number", "k16": "String", "k17": "String", "k18": "Number", "k19": "String", "k20": "String", "k21": "String", "k22": "Number", "k23": "Date", "k24": "String", "k25": "String", "k26": "Number", "k27": "Date", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "Number", "k33": "Date", "k34": "String", "k35": "Number", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "String", "k48": "Date", "k49": "String", "k50": "String", "k51": "Number", "k52": "String", "k53": "String", "k54": "Number", "k55": "String", "k56": "Date", "k57": "String", "k58": "Date", "k59": "Number", "k60": "String", "k61": "String", "k62": "String", "k63": "String", "k64": "String", "k65": "String"}}, {"colName": "collection", "count": 13, "size": 11086, "storageSize": 16384, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 852, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "070296a8-befd-46b4-b9f4-12e817af79ff"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "Number", "k6": "String", "k7": "String", "k8": "Number", "k9": "String", "k10": "<PERSON><PERSON>", "k11": "NumberLong", "k12": "Number", "k13": "<PERSON><PERSON>", "k14": "String", "k15": "String", "k16": "<PERSON><PERSON>", "k17": "String", "k18": "<PERSON><PERSON>", "k19": "String", "k20": "<PERSON><PERSON>", "k21": "<PERSON><PERSON>", "k22": "<PERSON><PERSON>", "k23": "<PERSON><PERSON>", "k24": "<PERSON><PERSON>", "k25": "<PERSON><PERSON>", "k26": "String", "k27": "String", "k28": "String", "k29": "<PERSON><PERSON>", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "Number", "k37": "String", "k38": "<PERSON><PERSON>", "k39": "Number", "k40": "String", "k41": "String"}}, {"colName": "collection", "count": 2381, "size": 6294193, "storageSize": 1982464, "totalIndexSize": 32768, "indexSizes": {"_id_": 32768}, "avgObjSize": 2643, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "207e423c-5744-46a9-887a-3063acc73bf4"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "<PERSON><PERSON>", "k6": "<PERSON><PERSON>", "k7": "<PERSON><PERSON>", "k8": "<PERSON><PERSON>", "k9": "<PERSON><PERSON>", "k10": "<PERSON><PERSON>", "k11": "<PERSON><PERSON>", "k12": "<PERSON><PERSON>", "k13": "<PERSON><PERSON>", "k14": "Number", "k15": "<PERSON><PERSON>", "k16": "<PERSON><PERSON>", "k17": "<PERSON><PERSON>", "k18": "<PERSON><PERSON>", "k19": "<PERSON><PERSON>", "k20": "<PERSON><PERSON>", "k21": "String", "k22": "String", "k23": "Number", "k24": "String", "k25": "<PERSON><PERSON>", "k26": "String", "k27": "NumberLong", "k28": "Number", "k29": "String", "k30": "<PERSON><PERSON>", "k31": "<PERSON><PERSON>", "k32": "<PERSON><PERSON>", "k33": "<PERSON><PERSON>", "k34": "String", "k35": "String", "k36": "<PERSON><PERSON>", "k37": "<PERSON><PERSON>", "k38": "<PERSON><PERSON>", "k39": "<PERSON><PERSON>", "k40": "<PERSON><PERSON>", "k41": "String", "k42": "<PERSON><PERSON>", "k43": "<PERSON><PERSON>", "k44": "<PERSON><PERSON>", "k45": "<PERSON><PERSON>", "k46": "<PERSON><PERSON>", "k47": "<PERSON><PERSON>", "k48": "<PERSON><PERSON>", "k49": "<PERSON><PERSON>", "k50": "<PERSON><PERSON>", "k51": "<PERSON><PERSON>", "k52": "<PERSON><PERSON>", "k53": "<PERSON><PERSON>", "k54": "<PERSON><PERSON>", "k55": "<PERSON><PERSON>", "k56": "<PERSON><PERSON>", "k57": "<PERSON><PERSON>", "k58": "<PERSON><PERSON>", "k59": "<PERSON><PERSON>", "k60": "String", "k61": "String", "k62": "String", "k63": "<PERSON><PERSON>", "k64": "<PERSON><PERSON>", "k65": "<PERSON><PERSON>", "k66": "String", "k67": "String", "k68": "String", "k69": "String", "k70": "String", "k71": "String", "k72": "String", "k73": "String", "k74": "String", "k75": "<PERSON><PERSON>", "k76": "String", "k77": "String", "k78": "String", "k79": "String", "k80": "Number", "k81": "String", "k82": "String", "k83": "<PERSON><PERSON>", "k84": "<PERSON><PERSON>", "k85": "<PERSON><PERSON>", "k86": "<PERSON><PERSON>", "k87": "<PERSON><PERSON>", "k88": "<PERSON><PERSON>", "k89": "String", "k90": "<PERSON><PERSON>", "k91": "<PERSON><PERSON>", "k92": "<PERSON><PERSON>", "k93": "<PERSON><PERSON>", "k94": "<PERSON><PERSON>", "k95": "<PERSON><PERSON>", "k96": "<PERSON><PERSON>", "k97": "String", "k98": "<PERSON><PERSON>", "k99": "<PERSON><PERSON>", "k100": "<PERSON><PERSON>", "k101": "<PERSON><PERSON>", "k102": "<PERSON><PERSON>", "k103": "<PERSON><PERSON>", "k104": "<PERSON><PERSON>", "k105": "<PERSON><PERSON>", "k106": "<PERSON><PERSON>", "k107": "<PERSON><PERSON>", "k108": "<PERSON><PERSON>", "k109": "<PERSON><PERSON>", "k110": "<PERSON><PERSON>", "k111": "<PERSON><PERSON>", "k112": "<PERSON><PERSON>", "k113": "Number", "k114": "String", "k115": "String", "k116": "String", "k117": "String", "k118": "Number"}}, {"colName": "collection", "count": 14, "size": 15036, "storageSize": 16384, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 1074, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "cf6a6006-b89f-4ce5-9b5c-9a7cbd331f5e"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String"}}, {"colName": "collection", "count": 1, "size": 1185, "storageSize": 32768, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 1185, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "35fa7b99-4f37-409f-a20d-a4e67d92b5c1"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "String", "k2": "String", "k3": "String", "k4": "BSON", "k5": "String", "k6": "String", "k7": "Date", "k8": "Number", "k9": "Number", "k10": "Number", "k11": "Number", "k12": "String", "k13": "String", "k14": "String", "k15": "Number", "k16": "String", "k17": "String", "k18": "Number", "k19": "String", "k20": "String", "k21": "Number", "k22": "Date", "k23": "String", "k24": "String", "k25": "Number", "k26": "Date", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "Number", "k32": "Date", "k33": "String", "k34": "Number", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "Date", "k48": "String", "k49": "String", "k50": "Number", "k51": "String", "k52": "String", "k53": "Number", "k54": "String", "k55": "Date", "k56": "String", "k57": "Date", "k58": "String", "k59": "Number", "k60": "String", "k61": "String", "k62": "String", "k63": "String", "k64": "String", "k65": "String", "k66": "String"}}, {"colName": "collection", "count": 1, "size": 1135, "storageSize": 16384, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 1135, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "7aeff240-f2b3-4785-9b4e-************"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "String", "k2": "String", "k3": "BSON", "k4": "String", "k5": "String", "k6": "Date", "k7": "Number", "k8": "Number", "k9": "Number", "k10": "Number", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "Number", "k16": "String", "k17": "String", "k18": "Number", "k19": "String", "k20": "String", "k21": "Number", "k22": "Date", "k23": "String", "k24": "String", "k25": "Number", "k26": "Date", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "Number", "k32": "Date", "k33": "String", "k34": "Number", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "String", "k48": "String", "k49": "Number", "k50": "String", "k51": "String", "k52": "Number", "k53": "String", "k54": "Date", "k55": "String", "k56": "Date", "k57": "Date", "k58": "Number", "k59": "String", "k60": "String", "k61": "String", "k62": "String", "k63": "String"}}, {"colName": "collection", "count": 1, "size": 722, "storageSize": 16384, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 722, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "7f294be0-fd68-4f20-81a1-cf05beb2a579"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "Number", "k4": "Number", "k5": "Number", "k6": "String", "k7": "String", "k8": "Number", "k9": "String", "k10": "String", "k11": "Number", "k12": "String", "k13": "Date", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "Date", "k31": "String", "k32": "String", "k33": "Number", "k34": "Date", "k35": "String", "k36": "Number", "k37": "String", "k38": "String", "k39": "String"}}, {"colName": "collection", "count": 10755, "size": 7095661, "storageSize": 2101248, "totalIndexSize": 106496, "indexSizes": {"_id_": 106496}, "avgObjSize": 659, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "2e7aa754-ceb3-4544-8003-c5c972aff007"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "Number", "k2": "Number", "k3": "Number", "k4": "Number", "k5": "BSON", "k6": "String", "k7": "Number", "k8": "Number", "k9": "<PERSON><PERSON>", "k10": "Number", "k11": "String", "k12": "String", "k13": "String", "k14": "<PERSON><PERSON>", "k15": "<PERSON><PERSON>", "k16": "Number", "k17": "Date", "k18": "String", "k19": "String", "k20": "<PERSON><PERSON>", "k21": "Number", "k22": "<PERSON><PERSON>", "k23": "String", "k24": "String", "k25": "String", "k26": "<PERSON><PERSON>", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "Date", "k34": "<PERSON><PERSON>", "k35": "Number", "k36": "Number", "k37": "String", "k38": "String"}}, {"colName": "collection", "count": 5, "size": 1532, "storageSize": 16384, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 306, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "2d6d4d72-1234-4504-970b-4d650999c0e2"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String"}}, {"colName": "collection", "count": 1, "size": 806, "storageSize": 36864, "totalIndexSize": 36864, "indexSizes": {"_id_": 36864}, "avgObjSize": 806, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "43b8ec06-a14b-4987-9356-1ad11b32fa5f"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "String", "k2": "Number", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "BSON", "k15": "String", "k16": "Number", "k17": "Number", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "Date", "k23": "String", "k24": "String", "k25": "String", "k26": "Boolean", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "Boolean", "k40": "Boolean", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "String", "k48": "Number", "k49": "Boolean"}}, {"colName": "collection", "count": 231, "size": 162260, "storageSize": 126976, "totalIndexSize": 24576, "indexSizes": {"_id_": 24576}, "avgObjSize": 702, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "17766f8d-518f-4a08-854c-7a1a7ac8e582"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "<PERSON><PERSON>", "k2": "Number", "k3": "BSON", "k4": "String", "k5": "<PERSON><PERSON>", "k6": "Number", "k7": "<PERSON><PERSON>", "k8": "Number", "k9": "<PERSON><PERSON>", "k10": "<PERSON><PERSON>", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "<PERSON><PERSON>", "k18": "ObjectId", "k19": "Date", "k20": "<PERSON><PERSON>", "k21": "<PERSON><PERSON>", "k22": "<PERSON><PERSON>", "k23": "Date", "k24": "Date", "k25": "String", "k26": "<PERSON><PERSON>", "k27": "<PERSON><PERSON>", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "Date", "k33": "String", "k34": "String"}}, {"colName": "collection", "count": 1002, "size": 632078, "storageSize": 217088, "totalIndexSize": 20480, "indexSizes": {"_id_": 20480}, "avgObjSize": 630, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "91f8b432-8837-4434-97c4-6816a13a12b1"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "<PERSON><PERSON>", "k6": "<PERSON><PERSON>", "k7": "<PERSON><PERSON>", "k8": "Number", "k9": "String", "k10": "<PERSON><PERSON>", "k11": "<PERSON><PERSON>", "k12": "NumberLong", "k13": "Number", "k14": "String", "k15": "<PERSON><PERSON>", "k16": "<PERSON><PERSON>", "k17": "<PERSON><PERSON>", "k18": "String", "k19": "<PERSON><PERSON>", "k20": "<PERSON><PERSON>", "k21": "String", "k22": "<PERSON><PERSON>", "k23": "<PERSON><PERSON>", "k24": "<PERSON><PERSON>", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "<PERSON><PERSON>", "k30": "<PERSON><PERSON>", "k31": "<PERSON><PERSON>", "k32": "String", "k33": "String"}}, {"colName": "collection", "count": 563, "size": 396169, "storageSize": 143360, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 703, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "94c1d6d8-426c-4363-be84-1a546d23dcc6"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "Number", "k4": "String", "k5": "String", "k6": "<PERSON><PERSON>", "k7": "<PERSON><PERSON>", "k8": "NumberLong", "k9": "Number", "k10": "String", "k11": "<PERSON><PERSON>", "k12": "<PERSON><PERSON>", "k13": "<PERSON><PERSON>", "k14": "<PERSON><PERSON>", "k15": "<PERSON><PERSON>", "k16": "<PERSON><PERSON>", "k17": "<PERSON><PERSON>", "k18": "<PERSON><PERSON>", "k19": "String", "k20": "<PERSON><PERSON>", "k21": "<PERSON><PERSON>", "k22": "<PERSON><PERSON>", "k23": "<PERSON><PERSON>", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "<PERSON><PERSON>", "k30": "<PERSON><PERSON>", "k31": "<PERSON><PERSON>", "k32": "<PERSON><PERSON>", "k33": "<PERSON><PERSON>", "k34": "<PERSON><PERSON>", "k35": "String", "k36": "String", "k37": "<PERSON><PERSON>"}}, {"colName": "collection", "count": 6241, "size": 6490340, "storageSize": 3227648, "totalIndexSize": 65536, "indexSizes": {"_id_": 65536}, "avgObjSize": 1039, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "af429b42-ca4b-497b-bf55-7d3e7f2b4bfc"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "Date", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "String", "k48": "String", "k49": "String", "k50": "String", "k51": "String"}}, {"colName": "collection", "count": 5166, "size": 7300244, "storageSize": 2166784, "totalIndexSize": 57344, "indexSizes": {"_id_": 57344}, "avgObjSize": 1413, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "affc220c-3644-4242-a224-ceafb482d033"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "String", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "BSON", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "Number", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "Date", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "String", "k48": "String", "k49": "Number", "k50": "String", "k51": "String", "k52": "String", "k53": "String", "k54": "String", "k55": "String", "k56": "String", "k57": "String", "k58": "String", "k59": "String", "k60": "String", "k61": "String", "k62": "String", "k63": "String", "k64": "String", "k65": "String", "k66": "String", "k67": "String", "k68": "String", "k69": "String", "k70": "String", "k71": "String", "k72": "String", "k73": "String", "k74": "String", "k75": "String"}}, {"colName": "collection", "count": 41, "size": 37201, "storageSize": 45056, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 907, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "a194967b-2711-4595-b036-a13ddac98016"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "String", "k48": "String"}}, {"colName": "collection", "count": 4684, "size": 4546779, "storageSize": 1966080, "totalIndexSize": 81920, "indexSizes": {"_id_": 81920}, "avgObjSize": 970, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "9885e1b4-d552-415a-aef9-cb3c4b2a6e8f"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "Date", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "String"}}, {"colName": "collection", "count": 3220, "size": 4667151, "storageSize": 1413120, "totalIndexSize": 40960, "indexSizes": {"_id_": 40960}, "avgObjSize": 1449, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "49559baf-8101-4aef-b5d9-9b6ad060b894"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "String", "k48": "String", "k49": "String", "k50": "String", "k51": "String", "k52": "String", "k53": "String", "k54": "String", "k55": "String", "k56": "String", "k57": "String", "k58": "String", "k59": "String", "k60": "String", "k61": "String", "k62": "String", "k63": "String", "k64": "String", "k65": "String", "k66": "String", "k67": "String", "k68": "String", "k69": "String", "k70": "String", "k71": "String", "k72": "String", "k73": "String", "k74": "String", "k75": "String", "k76": "String", "k77": "String", "k78": "String", "k79": "String", "k80": "String", "k81": "String", "k82": "String", "k83": "String", "k84": "String", "k85": "String", "k86": "String"}}, {"colName": "collection", "count": 6631, "size": 5590933, "storageSize": 2904064, "totalIndexSize": 69632, "indexSizes": {"_id_": 69632}, "avgObjSize": 843, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "533338bd-55fd-4538-9fd5-f59dd069a4b9"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String"}}, {"colName": "collection", "count": 6130, "size": 4337200, "storageSize": 2191360, "totalIndexSize": 65536, "indexSizes": {"_id_": 65536}, "avgObjSize": 707, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "c600392b-77fe-4485-8e08-6ce5c61ea28c"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String"}}, {"colName": "collection", "count": 2, "size": 1774, "storageSize": 36864, "totalIndexSize": 36864, "indexSizes": {"_id_": 36864}, "avgObjSize": 887, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "57bd3de1-7e68-46a5-be32-c46bff0a31b5"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "Number", "k2": "<PERSON><PERSON>", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "BSON", "k6": "String", "k7": "Number", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "<PERSON><PERSON>", "k14": "String", "k15": "<PERSON><PERSON>", "k16": "<PERSON><PERSON>", "k17": "String", "k18": "Date", "k19": "<PERSON><PERSON>", "k20": "Date", "k21": "Date", "k22": "String", "k23": "Number", "k24": "Number", "k25": "Number", "k26": "<PERSON><PERSON>", "k27": "<PERSON><PERSON>", "k28": "Number", "k29": "Number", "k30": "Number", "k31": "Number", "k32": "String", "k33": "<PERSON><PERSON>", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "Date", "k40": "String", "k41": "String", "k42": "String"}}, {"colName": "collection", "count": 1101, "size": 2001719, "storageSize": 315392, "totalIndexSize": 20480, "indexSizes": {"_id_": 20480}, "avgObjSize": 1818, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "6a4f1639-cc30-4ed1-8fbc-ab52eb10bcfc"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "Number", "k2": "String", "k3": "String", "k4": "Number", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "BSON", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "Number", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "Number", "k38": "Number", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "String", "k48": "String", "k49": "String", "k50": "Number", "k51": "String", "k52": "String", "k53": "String", "k54": "String", "k55": "String", "k56": "Date", "k57": "String", "k58": "String", "k59": "String", "k60": "String", "k61": "String", "k62": "String", "k63": "String", "k64": "String", "k65": "Number", "k66": "Number", "k67": "String", "k68": "String", "k69": "Number", "k70": "String", "k71": "String", "k72": "String", "k73": "String", "k74": "String", "k75": "String", "k76": "String", "k77": "String", "k78": "String", "k79": "String", "k80": "String", "k81": "String", "k82": "String", "k83": "String", "k84": "String", "k85": "String", "k86": "String", "k87": "String", "k88": "String", "k89": "String", "k90": "String", "k91": "String", "k92": "String", "k93": "String"}}, {"colName": "collection", "count": 4, "size": 4498, "storageSize": 32768, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 1124, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "140e1e74-b8c0-4b7c-9362-3c71de1bc353"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "String", "k48": "String", "k49": "String", "k50": "String", "k51": "String"}}, {"colName": "collection", "count": 6107, "size": 5131938, "storageSize": 2293760, "totalIndexSize": 65536, "indexSizes": {"_id_": 65536}, "avgObjSize": 840, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "04f00b37-0bdf-4109-bc73-96b54b97e943"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String"}}, {"colName": "collection", "count": 425, "size": 496955, "storageSize": 98304, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 1169, "colInfo": [{"name": "collection", "type": "collection", "options": {"validationLevel": "strict", "validationAction": "error"}, "info": {"readOnly": false, "uuid": "2cc03806-bd16-4331-9baf-9e846cde9465"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "String", "k48": "String", "k49": "String", "k50": "String", "k51": "String", "k52": "String", "k53": "String", "k54": "String", "k55": "String"}}, {"colName": "collection", "count": 8683, "size": 7091947, "storageSize": 4091904, "totalIndexSize": 90112, "indexSizes": {"_id_": 90112}, "avgObjSize": 816, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "fc8dee93-af9c-4d65-85c8-948ad387451e"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String"}}, {"colName": "collection", "count": 14765, "size": 12900949, "storageSize": 3829760, "totalIndexSize": 159744, "indexSizes": {"_id_": 159744}, "avgObjSize": 873, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "2d8b6337-aa6f-4eff-a591-4f9adcc47d7c"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "<PERSON><PERSON>", "k2": "<PERSON><PERSON>", "k3": "Number", "k4": "String", "k5": "<PERSON><PERSON>", "k6": "String", "k7": "<PERSON><PERSON>", "k8": "BSON", "k9": "String", "k10": "String", "k11": "<PERSON><PERSON>", "k12": "String", "k13": "Number", "k14": "String", "k15": "Number", "k16": "String", "k17": "String", "k18": "String", "k19": "<PERSON><PERSON>", "k20": "ObjectId", "k21": "Date", "k22": "String", "k23": "<PERSON><PERSON>", "k24": "Date", "k25": "Date", "k26": "String", "k27": "<PERSON><PERSON>", "k28": "<PERSON><PERSON>", "k29": "<PERSON><PERSON>", "k30": "<PERSON><PERSON>", "k31": "String", "k32": "<PERSON><PERSON>", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "<PERSON><PERSON>", "k38": "Number", "k39": "Number", "k40": "Boolean", "k41": "Number", "k42": "Number", "k43": "Date", "k44": "<PERSON><PERSON>", "k45": "String", "k46": "String"}}, {"colName": "collection", "count": 307, "size": 259128, "storageSize": 110592, "totalIndexSize": 20480, "indexSizes": {"_id_": 20480}, "avgObjSize": 844, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "59ac1152-a01d-4ea9-bd06-71aca418ce32"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "<PERSON><PERSON>", "k2": "<PERSON><PERSON>", "k3": "Number", "k4": "String", "k5": "<PERSON><PERSON>", "k6": "String", "k7": "<PERSON><PERSON>", "k8": "BSON", "k9": "String", "k10": "String", "k11": "<PERSON><PERSON>", "k12": "String", "k13": "Number", "k14": "String", "k15": "Number", "k16": "String", "k17": "String", "k18": "String", "k19": "<PERSON><PERSON>", "k20": "ObjectId", "k21": "Date", "k22": "String", "k23": "<PERSON><PERSON>", "k24": "String", "k25": "Date", "k26": "Date", "k27": "String", "k28": "<PERSON><PERSON>", "k29": "<PERSON><PERSON>", "k30": "<PERSON><PERSON>", "k31": "<PERSON><PERSON>", "k32": "String", "k33": "<PERSON><PERSON>", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "<PERSON><PERSON>", "k39": "Number", "k40": "String", "k41": "Boolean", "k42": "Number", "k43": "Number", "k44": "Date", "k45": "<PERSON><PERSON>", "k46": "String", "k47": "String"}}, {"colName": "collection", "count": 10, "size": 11687, "storageSize": 36864, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 1168, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "69893eff-d096-4101-bb62-a6f397e17b32"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "String", "k48": "String", "k49": "String", "k50": "String", "k51": "String", "k52": "String", "k53": "String", "k54": "String"}}, {"colName": "collection", "count": 11, "size": 6742, "storageSize": 36864, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 612, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "8d28a720-bb1e-44a7-a604-b1c07de790ff"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String"}}, {"colName": "collection", "count": 104, "size": 116408, "storageSize": 69632, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 1119, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "0f1061b8-2f85-4a65-a4ae-b33d6588bb14"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "String", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "BSON", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "String", "k48": "String", "k49": "String", "k50": "String", "k51": "String", "k52": "String", "k53": "String", "k54": "String", "k55": "String", "k56": "String", "k57": "String", "k58": "String", "k59": "String", "k60": "String", "k61": "String", "k62": "String"}}, {"colName": "collection", "count": 5519, "size": 7953528, "storageSize": 2412544, "totalIndexSize": 61440, "indexSizes": {"_id_": 61440}, "avgObjSize": 1441, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "69c6f39d-0c16-4c1b-921a-1fd59790313f"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "String", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "BSON", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "String", "k48": "String", "k49": "String", "k50": "String", "k51": "String", "k52": "String", "k53": "String", "k54": "String", "k55": "String", "k56": "String", "k57": "String", "k58": "String", "k59": "String", "k60": "String", "k61": "String", "k62": "String", "k63": "String", "k64": "String", "k65": "String", "k66": "String", "k67": "String", "k68": "String", "k69": "String", "k70": "String", "k71": "String", "k72": "String", "k73": "String", "k74": "String", "k75": "String", "k76": "String"}}, {"colName": "collection", "count": 3207, "size": 2888467, "storageSize": 888832, "totalIndexSize": 40960, "indexSizes": {"_id_": 40960}, "avgObjSize": 900, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "c4e2957d-b0f4-47e1-a025-7040754209e2"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "String", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "BSON", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "String", "k48": "String", "k49": "String", "k50": "String", "k51": "String", "k52": "String", "k53": "String"}}, {"colName": "collection", "count": 9653, "size": 9442313, "storageSize": 2854912, "totalIndexSize": 98304, "indexSizes": {"_id_": 98304}, "avgObjSize": 978, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "fdd2d45e-5e47-4d43-8e58-4b3812164dba"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "String", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "BSON", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "String", "k48": "String", "k49": "String", "k50": "String", "k51": "String", "k52": "String", "k53": "String"}}, {"colName": "collection", "count": 4626, "size": 3367180, "storageSize": 1011712, "totalIndexSize": 53248, "indexSizes": {"_id_": 53248}, "avgObjSize": 727, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "9981d4a9-6b85-409b-a1c6-925a8127e3c3"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "String", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "BSON", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String"}}, {"colName": "collection", "count": 18, "size": 15840, "storageSize": 36864, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 880, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "a1c3f207-3fc2-476d-8e0b-45ce64340df2"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "String", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "BSON", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "String", "k48": "String", "k49": "String", "k50": "String", "k51": "String"}}, {"colName": "collection", "count": 6, "size": 6394, "storageSize": 36864, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 1065, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "6f4c6521-333b-410b-aad7-f98adaad909b"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "String", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "BSON", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "String", "k48": "String", "k49": "String", "k50": "String", "k51": "String", "k52": "String", "k53": "String", "k54": "String", "k55": "String", "k56": "String"}}, {"colName": "collection", "count": 3603, "size": 2996256, "storageSize": 1765376, "totalIndexSize": 45056, "indexSizes": {"_id_": 45056}, "avgObjSize": 831, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "23e1d356-2230-4f92-9184-82df25bbcdba"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "String", "k2": "String", "k3": "String", "k4": "String", "k5": "BSON", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String"}}, {"colName": "collection", "count": 2229, "size": 2301687, "storageSize": 688128, "totalIndexSize": 32768, "indexSizes": {"_id_": 32768}, "avgObjSize": 1032, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "8087c5a4-898a-425c-945e-71f42642bf4b"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "String", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "BSON", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "String", "k48": "String", "k49": "String", "k50": "String", "k51": "String"}}, {"colName": "collection", "count": 2, "size": 2314, "storageSize": 4665344, "totalIndexSize": 249856, "indexSizes": {"_id_": 249856}, "avgObjSize": 1157, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "dccfe246-31ca-4d43-9162-65a6857e2050"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "<PERSON><PERSON>", "k2": "<PERSON><PERSON>", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "<PERSON><PERSON>", "k6": "String", "k7": "String", "k8": "<PERSON><PERSON>", "k9": "<PERSON><PERSON>", "k10": "<PERSON><PERSON>", "k11": "BSON", "k12": "String", "k13": "<PERSON><PERSON>", "k14": "String", "k15": "<PERSON><PERSON>", "k16": "String", "k17": "<PERSON><PERSON>", "k18": "<PERSON><PERSON>", "k19": "String", "k20": "String", "k21": "NumberLong", "k22": "Number", "k23": "<PERSON><PERSON>", "k24": "Number", "k25": "String", "k26": "NumberLong", "k27": "Number", "k28": "String", "k29": "Number", "k30": "String", "k31": "String", "k32": "<PERSON><PERSON>", "k33": "<PERSON><PERSON>", "k34": "<PERSON><PERSON>", "k35": "Number", "k36": "<PERSON><PERSON>", "k37": "<PERSON><PERSON>", "k38": "String", "k39": "String", "k40": "<PERSON><PERSON>", "k41": "<PERSON><PERSON>", "k42": "Number", "k43": "<PERSON><PERSON>", "k44": "Number", "k45": "<PERSON><PERSON>", "k46": "Number", "k47": "String", "k48": "String", "k49": "String", "k50": "String", "k51": "String", "k52": "String", "k53": "Number", "k54": "<PERSON><PERSON>", "k55": "Number", "k56": "Number", "k57": "Number", "k58": "<PERSON><PERSON>", "k59": "<PERSON><PERSON>", "k60": "<PERSON><PERSON>", "k61": "<PERSON><PERSON>", "k62": "<PERSON><PERSON>", "k63": "String", "k64": "String", "k65": "<PERSON><PERSON>", "k66": "<PERSON><PERSON>", "k67": "<PERSON><PERSON>", "k68": "<PERSON><PERSON>", "k69": "<PERSON><PERSON>", "k70": "<PERSON><PERSON>", "k71": "<PERSON><PERSON>", "k72": "<PERSON><PERSON>", "k73": "<PERSON><PERSON>", "k74": "<PERSON><PERSON>", "k75": "<PERSON><PERSON>", "k76": "<PERSON><PERSON>", "k77": "<PERSON><PERSON>", "k78": "<PERSON><PERSON>", "k79": "<PERSON><PERSON>", "k80": "<PERSON><PERSON>", "k81": "<PERSON><PERSON>", "k82": "<PERSON><PERSON>", "k83": "<PERSON><PERSON>", "k84": "<PERSON><PERSON>", "k85": "<PERSON><PERSON>", "k86": "<PERSON><PERSON>"}}, {"colName": "collection", "count": 27915, "size": 32521619, "storageSize": 9621504, "totalIndexSize": 827392, "indexSizes": {"__tapd8.ts_1": 299008, "_id_": 528384}, "avgObjSize": 1165, "colInfo": [{"name": "collection", "type": "collection", "options": {"validator": {"unitID": {"$regex": "{Han}a-zA-Z0-9]{1,2}"}, "siteID": {"$regex": "{Han}a-zA-Z0-9]{1,2}"}, "siteNam": {"$regex": "{Han}a-zA-Z0-9]{1,16}"}, "senTypID": {"$regex": "{Han}a-zA-Z0-9]{1,2}"}, "senPlaTyPID": {"$regex": "{Han}a-zA-Z0-9]{1,2}"}, "antNam": {"$regex": "{Han}a-zA-Z0-9]{1,20}"}}, "validationLevel": "strict", "validationAction": "error"}, "info": {"readOnly": false, "uuid": "3b127813-b250-4bae-ae89-238506a77428"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "<PERSON><PERSON>", "k2": "<PERSON><PERSON>", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "<PERSON><PERSON>", "k6": "String", "k7": "String", "k8": "<PERSON><PERSON>", "k9": "<PERSON><PERSON>", "k10": "<PERSON><PERSON>", "k11": "BSON", "k12": "String", "k13": "<PERSON><PERSON>", "k14": "String", "k15": "<PERSON><PERSON>", "k16": "String", "k17": "<PERSON><PERSON>", "k18": "<PERSON><PERSON>", "k19": "String", "k20": "String", "k21": "String", "k22": "Date", "k23": "Number", "k24": "<PERSON><PERSON>", "k25": "Number", "k26": "String", "k27": "Date", "k28": "Number", "k29": "String", "k30": "Number", "k31": "String", "k32": "String", "k33": "<PERSON><PERSON>", "k34": "<PERSON><PERSON>", "k35": "<PERSON><PERSON>", "k36": "Number", "k37": "<PERSON><PERSON>", "k38": "<PERSON><PERSON>", "k39": "String", "k40": "String", "k41": "<PERSON><PERSON>", "k42": "<PERSON><PERSON>", "k43": "Number", "k44": "<PERSON><PERSON>", "k45": "<PERSON><PERSON>", "k46": "Number", "k47": "String", "k48": "String", "k49": "String", "k50": "String", "k51": "String", "k52": "String", "k53": "Number", "k54": "<PERSON><PERSON>", "k55": "Number", "k56": "Number", "k57": "Number", "k58": "<PERSON><PERSON>", "k59": "<PERSON><PERSON>", "k60": "<PERSON><PERSON>", "k61": "<PERSON><PERSON>", "k62": "<PERSON><PERSON>", "k63": "String", "k64": "<PERSON><PERSON>", "k65": "<PERSON><PERSON>", "k66": "<PERSON><PERSON>", "k67": "<PERSON><PERSON>", "k68": "<PERSON><PERSON>", "k69": "<PERSON><PERSON>", "k70": "<PERSON><PERSON>", "k71": "<PERSON><PERSON>", "k72": "<PERSON><PERSON>", "k73": "<PERSON><PERSON>", "k74": "<PERSON><PERSON>", "k75": "<PERSON><PERSON>", "k76": "<PERSON><PERSON>", "k77": "<PERSON><PERSON>", "k78": "<PERSON><PERSON>", "k79": "<PERSON><PERSON>", "k80": "<PERSON><PERSON>", "k81": "<PERSON><PERSON>", "k82": "<PERSON><PERSON>", "k83": "<PERSON><PERSON>", "k84": "<PERSON><PERSON>", "k85": "<PERSON><PERSON>"}}, {"colName": "collection", "count": 11447, "size": 7840558, "storageSize": 1667072, "totalIndexSize": 204800, "indexSizes": {"_id_": 204800}, "avgObjSize": 684, "colInfo": [{"name": "collection", "type": "collection", "options": {"validator": {"unitID": {"$regex": "{Han}a-zA-Z0-9]{1,2}"}, "siteID": {"$regex": "{Han}a-zA-Z0-9]{1,2}"}, "siteNam": {"$regex": "{Han}a-zA-Z0-9]{1,16}"}, "senTypID": {"$regex": "{Han}a-zA-Z0-9]{1,2}"}, "senPlaTypID": {"$regex": "{Han}a-zA-Z0-9]{1,2}"}, "antNam": {"$regex": "{Han}a-zA-Z0-9]{1,20}"}}, "validationLevel": "off", "validationAction": "error"}, "info": {"readOnly": false, "uuid": "a2c8c0de-bdee-4f63-b896-5a4fd1a66e21"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "Number", "k2": "Number", "k3": "String", "k4": "String", "k5": "Number", "k6": "Number", "k7": "Number", "k8": "Number", "k9": "Number", "k10": "Date", "k11": "BSON", "k12": "String", "k13": "<PERSON><PERSON>", "k14": "String", "k15": "Number", "k16": "<PERSON><PERSON>", "k17": "<PERSON><PERSON>", "k18": "Number", "k19": "<PERSON><PERSON>", "k20": "String", "k21": "String", "k22": "Date", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "Number", "k28": "Number", "k29": "Number", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "Number", "k36": "Number", "k37": "String"}}, {"colName": "collection", "count": 11447, "size": 10488189, "storageSize": 2187264, "totalIndexSize": 241664, "indexSizes": {"_id_": 241664}, "avgObjSize": 916, "colInfo": [{"name": "collection", "type": "collection", "options": {"validator": {"unitID": {"$regex": "{Han}a-zA-Z0-9]{1,2}"}, "siteID": {"$regex": "{Han}a-zA-Z0-9]{1,2}"}, "siteNam": {"$regex": "{Han}a-zA-Z0-9]{1,16}"}, "senTypID": {"$regex": "{Han}a-zA-Z0-9]{1,2}"}, "senPlaTypID": {"$regex": "{Han}a-zA-Z0-9]{1,2}"}, "antNam": {"$regex": "{Han}a-zA-Z0-9]{1,20}"}}, "validationLevel": "strict", "validationAction": "error"}, "info": {"readOnly": false, "uuid": "0f83ccd3-bf1f-419e-a208-e400d687f8fb"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "Number", "k2": "Number", "k3": "Number", "k4": "Number", "k5": "String", "k6": "Number", "k7": "String", "k8": "String", "k9": "String", "k10": "Number", "k11": "Number", "k12": "NumberLong", "k13": "BSON", "k14": "String", "k15": "<PERSON><PERSON>", "k16": "String", "k17": "String", "k18": "Number", "k19": "<PERSON><PERSON>", "k20": "String", "k21": "<PERSON><PERSON>", "k22": "Number", "k23": "Number", "k24": "String", "k25": "String", "k26": "String", "k27": "Date", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "Number", "k34": "String", "k35": "String", "k36": "String", "k37": "Number", "k38": "Number", "k39": "Number", "k40": "<PERSON><PERSON>", "k41": "Number", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "Number", "k48": "Number", "k49": "String", "k50": "String"}}, {"colName": "collection", "count": 11447, "size": 10724229, "storageSize": 2879488, "totalIndexSize": 237568, "indexSizes": {"_id_": 237568}, "avgObjSize": 936, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "29ac5588-0d49-46cb-bab5-a84d2729cbfd"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "Number", "k2": "Number", "k3": "Number", "k4": "Number", "k5": "String", "k6": "Number", "k7": "String", "k8": "String", "k9": "String", "k10": "Number", "k11": "Number", "k12": "NumberLong", "k13": "BSON", "k14": "String", "k15": "<PERSON><PERSON>", "k16": "String", "k17": "String", "k18": "Number", "k19": "<PERSON><PERSON>", "k20": "String", "k21": "<PERSON><PERSON>", "k22": "Number", "k23": "Number", "k24": "String", "k25": "String", "k26": "String", "k27": "Date", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "Number", "k34": "String", "k35": "String", "k36": "String", "k37": "Number", "k38": "Number", "k39": "Number", "k40": "<PERSON><PERSON>", "k41": "Number", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "Number", "k48": "Number", "k49": "String", "k50": "String", "k51": "Number"}}, {"colName": "collection", "count": 18137, "size": 9939076, "storageSize": 2969600, "totalIndexSize": 176128, "indexSizes": {"_id_": 176128}, "avgObjSize": 548, "colInfo": [{"name": "collection", "type": "collection", "options": {"validator": {"unitID": {"$regex": "{Han}a-zA-Z0-9]{1,2}"}, "siteID": {"$regex": "{Han}a-zA-Z0-9]{1,2}"}, "siteNam": {"$regex": "{Han}a-zA-Z0-9]{1,16}"}, "senTypID": {"$regex": "{Han}a-zA-Z0-9]{1,2}"}, "senPlaTypID": {"$regex": "{Han}a-zA-Z0-9]{1,2}"}, "antNam": {"$regex": "{Han}a-zA-Z0-9]{1,20}"}}, "validationLevel": "strict", "validationAction": "error"}, "info": {"readOnly": false, "uuid": "5c28f98b-87b1-4e2e-8dfa-9208f58e103f"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "Number", "k2": "Number", "k3": "String", "k4": "String", "k5": "Number", "k6": "Number", "k7": "BSON", "k8": "String", "k9": "<PERSON><PERSON>", "k10": "String", "k11": "<PERSON><PERSON>", "k12": "String", "k13": "String", "k14": "Date", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "Number", "k20": "Number", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String"}}, {"colName": "collection", "count": 14, "size": 37688, "storageSize": 45056, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 2692, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "e7a1fb84-5d3f-4a9d-be48-08ce103a7136"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "String", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "BSON", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "String", "k48": "String", "k49": "String", "k50": "String", "k51": "String", "k52": "String", "k53": "String", "k54": "String", "k55": "String", "k56": "String", "k57": "String", "k58": "String", "k59": "String", "k60": "String", "k61": "String", "k62": "String", "k63": "String", "k64": "String", "k65": "String", "k66": "String", "k67": "String", "k68": "String", "k69": "String", "k70": "String", "k71": "String", "k72": "String", "k73": "String", "k74": "String", "k75": "String", "k76": "String", "k77": "String", "k78": "String", "k79": "String", "k80": "String", "k81": "String", "k82": "String", "k83": "String", "k84": "String", "k85": "String", "k86": "String", "k87": "String", "k88": "String", "k89": "String", "k90": "String", "k91": "String", "k92": "String", "k93": "String", "k94": "String", "k95": "String", "k96": "String", "k97": "String", "k98": "String", "k99": "String", "k100": "String", "k101": "String", "k102": "String", "k103": "String", "k104": "String", "k105": "String", "k106": "String", "k107": "String", "k108": "String", "k109": "String", "k110": "String", "k111": "String", "k112": "String", "k113": "String", "k114": "String", "k115": "String", "k116": "String", "k117": "String", "k118": "String", "k119": "String"}}, {"colName": "collection", "count": 13, "size": 23755, "storageSize": 36864, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 1827, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "41400f75-c65f-4fd1-b177-1170cd251903"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "String", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "BSON", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "String", "k48": "String", "k49": "String", "k50": "String", "k51": "String", "k52": "String", "k53": "String", "k54": "String", "k55": "String", "k56": "String", "k57": "String", "k58": "String", "k59": "String", "k60": "String", "k61": "String", "k62": "String", "k63": "String", "k64": "String", "k65": "String", "k66": "String", "k67": "String", "k68": "String", "k69": "String", "k70": "String", "k71": "String", "k72": "String", "k73": "String", "k74": "String", "k75": "String", "k76": "String", "k77": "String", "k78": "String", "k79": "String", "k80": "String", "k81": "String", "k82": "String"}}, {"colName": "collection", "count": 17, "size": 45942, "storageSize": 45056, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 2702, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "af3341f5-7011-45d8-a4f6-81e5f31ad488"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "String", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "BSON", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "String", "k48": "String", "k49": "String", "k50": "String", "k51": "String", "k52": "String", "k53": "String", "k54": "String", "k55": "String", "k56": "String", "k57": "String", "k58": "String", "k59": "String", "k60": "String", "k61": "String", "k62": "String", "k63": "String", "k64": "String", "k65": "String", "k66": "String", "k67": "String", "k68": "String", "k69": "String", "k70": "String", "k71": "String", "k72": "String", "k73": "String", "k74": "String", "k75": "String", "k76": "String", "k77": "String", "k78": "String", "k79": "String", "k80": "String", "k81": "String", "k82": "String", "k83": "String", "k84": "String", "k85": "String", "k86": "String", "k87": "String", "k88": "String", "k89": "String", "k90": "String", "k91": "String", "k92": "String", "k93": "String", "k94": "String", "k95": "String", "k96": "String", "k97": "String", "k98": "String", "k99": "String", "k100": "String", "k101": "String", "k102": "String", "k103": "String", "k104": "String", "k105": "String", "k106": "String", "k107": "String", "k108": "String", "k109": "String", "k110": "String", "k111": "String", "k112": "String", "k113": "String", "k114": "String", "k115": "String", "k116": "String", "k117": "String", "k118": "String", "k119": "String"}}, {"colName": "collection", "count": 9386, "size": 2346999, "storageSize": 925696, "totalIndexSize": 94208, "indexSizes": {"_id_": 94208}, "avgObjSize": 250, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "2c9763d3-8d23-4cd7-a66e-244617d22a0a"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "Number", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "Boolean", "k11": "String", "k12": "String", "k13": "Date"}}, {"colName": "collection", "count": 10, "size": 4932, "storageSize": 36864, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 493, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "5bc3e75b-25ea-410b-94a1-8ce5afe86788"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "String", "k2": "BSON", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String"}}, {"colName": "collection", "count": 14, "size": 19976, "storageSize": 36864, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 1426, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "4a28bef0-7eba-4ec6-bc51-96514b5a9a99"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "String", "k2": "BSON", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "String", "k48": "String", "k49": "String", "k50": "String", "k51": "String", "k52": "String", "k53": "String", "k54": "String", "k55": "String", "k56": "String", "k57": "String", "k58": "String", "k59": "String", "k60": "String", "k61": "String", "k62": "String", "k63": "String", "k64": "String", "k65": "String", "k66": "String"}}, {"colName": "collection", "count": 5, "size": 6468, "storageSize": 36864, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 1293, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "de79deda-13ca-47a5-a6b1-96d5d592dde7"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "String", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "BSON", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String"}}, {"colName": "collection", "count": 13, "size": 12792, "storageSize": 36864, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 984, "colInfo": [{"name": "collection", "type": "collection", "options": {"validationLevel": "off", "validationAction": "error"}, "info": {"readOnly": false, "uuid": "e555e11c-b808-452e-b901-8b410f380092"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "String", "k2": "String", "k3": "String", "k4": "String", "k5": "BSON", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "String", "k48": "String", "k49": "String", "k50": "String", "k51": "String", "k52": "String", "k53": "String"}}, {"colName": "collection", "count": 21, "size": 29523, "storageSize": 28672, "totalIndexSize": 24576, "indexSizes": {"_id_": 24576}, "avgObjSize": 1405, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "e430c910-ff29-43df-8fa7-f5248620f7c5"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "String", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "BSON", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "String", "k48": "String", "k49": "String", "k50": "String", "k51": "String", "k52": "String", "k53": "String", "k54": "String", "k55": "String"}}, {"colName": "collection", "count": 10, "size": 4301, "storageSize": 36864, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 430, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "b9be99d5-8789-4596-a7e5-56d408ea4b05"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "String", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "BSON", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String"}}, {"colName": "collection", "count": 14, "size": 19976, "storageSize": 16384, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 1426, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "ed6738b0-af3c-4d8c-ad71-3bcddc68d8f0"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "String", "k2": "BSON", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "String", "k48": "String", "k49": "String", "k50": "String", "k51": "String", "k52": "String", "k53": "String", "k54": "String", "k55": "String", "k56": "String", "k57": "String", "k58": "String", "k59": "String", "k60": "String", "k61": "String", "k62": "String", "k63": "String", "k64": "String", "k65": "String", "k66": "String"}}, {"colName": "collection", "count": 980, "size": 366210, "storageSize": 241664, "totalIndexSize": 20480, "indexSizes": {"_id_": 20480}, "avgObjSize": 373, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "527516a8-5885-424e-beb9-af82931808b7"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "String", "k2": "String", "k3": "BSON", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String"}}, {"colName": "collection", "count": 6488, "size": 3012270, "storageSize": 1789952, "totalIndexSize": 69632, "indexSizes": {"_id_": 69632}, "avgObjSize": 464, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "cea7fb5d-4fa4-44a7-bea8-afa450d98611"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "String", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "BSON", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String"}}, {"colName": "collection", "count": 14, "size": 4961, "storageSize": 36864, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 354, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "8c3c9fa4-1dfa-4110-b778-4681067aa076"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "String", "k2": "Number", "k3": "BSON", "k4": "Date", "k5": "Number", "k6": "Date", "k7": "String", "k8": "String", "k9": "Number", "k10": "Number", "k11": "String", "k12": "String", "k13": "Number", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "Date"}}, {"colName": "collection", "count": 8085, "size": 2442570, "storageSize": 831488, "totalIndexSize": 81920, "indexSizes": {"_id_": 81920}, "avgObjSize": 302, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "ec57c207-12d1-493d-b1bc-a7869e687c71"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "Number", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "Date"}}, {"colName": "collection", "count": 6364, "size": 2685778, "storageSize": 1576960, "totalIndexSize": 69632, "indexSizes": {"_id_": 69632}, "avgObjSize": 422, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "d52e1734-f748-4d8e-84b5-0461a588d75a"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "String", "k2": "String", "k3": "BSON", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String"}}, {"colName": "collection", "count": 15101, "size": 8563064, "storageSize": 7606272, "totalIndexSize": 147456, "indexSizes": {"_id_": 147456}, "avgObjSize": 567, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "8629420c-1014-4910-8002-ef124eaa82c1"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String"}}, {"colName": "collection", "count": 7268, "size": 2434233, "storageSize": 1454080, "totalIndexSize": 77824, "indexSizes": {"_id_": 77824}, "avgObjSize": 334, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "42463d3a-2f87-4cf8-b48e-30f98cb805cc"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "String", "k2": "String", "k3": "Number", "k4": "String", "k5": "BSON", "k6": "String", "k7": "Number", "k8": "Date", "k9": "String", "k10": "String", "k11": "Number", "k12": "Number", "k13": "String", "k14": "String", "k15": "Number", "k16": "String", "k17": "String", "k18": "Date"}}, {"colName": "collection", "count": 5134, "size": 3824898, "storageSize": 1134592, "totalIndexSize": 57344, "indexSizes": {"_id_": 57344}, "avgObjSize": 745, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "eafca71f-d748-40e9-9306-26a59bb52753"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "String", "k2": "String", "k3": "String", "k4": "BSON", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String"}}, {"colName": "collection", "count": 8897, "size": 4564268, "storageSize": 2674688, "totalIndexSize": 90112, "indexSizes": {"_id_": 90112}, "avgObjSize": 513, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "70bcc086-914f-4a02-bc53-80c24ec0fc87"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String"}}, {"colName": "collection", "count": 1410, "size": 741323, "storageSize": 462848, "totalIndexSize": 24576, "indexSizes": {"_id_": 24576}, "avgObjSize": 525, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "8ffb572b-**************-3a232472d90f"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String"}}, {"colName": "collection", "count": 12, "size": 11458, "storageSize": 36864, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 954, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "564c0156-a3e8-4dd2-a298-9750b6e416a8"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String"}}, {"colName": "collection", "count": 1218, "size": 433608, "storageSize": 159744, "totalIndexSize": 53248, "indexSizes": {"_id_": 53248}, "avgObjSize": 356, "colInfo": [{"name": "collection", "type": "collection", "options": {"validator": {"unitID": {"$regex": "{Han}a-zA-Z0-9]{1,2}"}, "siteID": {"$regex": "{Han}a-zA-Z0-9]{1,2}"}, "siteNam": {"$regex": "{Han}a-zA-Z0-9]{1,16}"}, "senTypID": {"$regex": "{Han}a-zA-Z0-9]{1,2}"}, "senPlaTypID": {"$regex": "{Han}a-zA-Z0-9]{1,2}"}, "antNam": {"$regex": "{Han}a-zA-Z0-9]{1,20}"}}, "validationLevel": "off", "validationAction": "error"}, "info": {"readOnly": false, "uuid": "363072bd-3942-4cb7-8eda-bf38dfea493c"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "Number", "k2": "String", "k3": "BSON", "k4": "<PERSON><PERSON>", "k5": "<PERSON><PERSON>", "k6": "<PERSON><PERSON>", "k7": "String", "k8": "<PERSON><PERSON>", "k9": "<PERSON><PERSON>", "k10": "<PERSON><PERSON>", "k11": "<PERSON><PERSON>", "k12": "<PERSON><PERSON>", "k13": "<PERSON><PERSON>", "k14": "<PERSON><PERSON>", "k15": "<PERSON><PERSON>", "k16": "<PERSON><PERSON>", "k17": "String", "k18": "<PERSON><PERSON>", "k19": "String", "k20": "<PERSON><PERSON>", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String"}}, {"colName": "collection", "count": 206256, "size": 630318336, "storageSize": 130428928, "totalIndexSize": 2088960, "indexSizes": {"_id_": 2088960}, "avgObjSize": 3056, "colInfo": [{"name": "collection", "type": "collection", "options": {"validator": {"$and": [{"unitID": {"$regex": "{Han}a-zA-Z0-9]{1,2}"}}, {"siteID": {"$regex": "{Han}a-zA-Z0-9]{1,2}"}}, {"$or": [{"siteNam": {"$exists": false}}, {"siteNam": null}, {"siteNam": {"$regex": "{Han}a-zA-Z0-9]{1,16}"}}]}, {"senTypID": {"$regex": "{Han}a-zA-Z0-9]{1,2}"}}, {"$or": [{"senPlaTypID": {"$exists": false}}, {"senPlaTypID": null}, {"senPlaTypID": {"$regex": "{Han}a-zA-Z0-9]{1,2}"}}]}, {"eqpID": {"$regex": "{Han}a-zA-Z0-9]{14,14}"}}, {"ObjID": {"$regex": "{Han}a-zA-Z0-9]{1,70}"}}, {"$or": [{"chaID": {"$exists": false}}, {"chaID": null}, {"chaID": {"$gte": 1, "$lte": 255}}]}]}, "validationLevel": "strict", "validationAction": "error"}, "info": {"readOnly": false, "uuid": "5dec2def-4064-4fbd-a435-44d63f923aa9"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "String", "k2": "Number", "k3": "Date", "k4": "String", "k5": "String", "k6": "Number", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "Number", "k13": "Number", "k14": "String", "k15": "Date", "k16": "Number", "k17": "Number", "k18": "String", "k19": "String", "k20": "Date", "k21": "String", "k22": "Number", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "<PERSON><PERSON>", "k32": "<PERSON><PERSON>", "k33": "<PERSON><PERSON>", "k34": "Number", "k35": "Number", "k36": "String", "k37": "<PERSON><PERSON>", "k38": "<PERSON><PERSON>", "k39": "<PERSON><PERSON>", "k40": "<PERSON><PERSON>", "k41": "<PERSON><PERSON>", "k42": "<PERSON><PERSON>", "k43": "<PERSON><PERSON>", "k44": "BSON", "k45": "String", "k46": "Number", "k47": "String", "k48": "String"}}, {"colName": "collection", "count": 1218, "size": 1037893, "storageSize": 327680, "totalIndexSize": 24576, "indexSizes": {"_id_": 24576}, "avgObjSize": 852, "colInfo": [{"name": "collection", "type": "collection", "options": {"validator": {"unitID": {"$regex": "{Han}a-zA-Z0-9]{1,2}"}, "siteID": {"$regex": "{Han}a-zA-Z0-9]{1,2}"}, "siteNam": {"$regex": "{Han}a-zA-Z0-9]{1,2}"}, "antNam": {"$regex": "{Han}a-zA-Z0-9]{1,20}"}}, "validationLevel": "strict", "validationAction": "error"}, "info": {"readOnly": false, "uuid": "c2c1202b-6e05-40eb-9c97-221b8ac69301"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "<PERSON><PERSON>", "k2": "<PERSON><PERSON>", "k3": "Number", "k4": "String", "k5": "String", "k6": "BSON", "k7": "String", "k8": "Number", "k9": "String", "k10": "String", "k11": "String", "k12": "Number", "k13": "String", "k14": "Number", "k15": "String", "k16": "<PERSON><PERSON>", "k17": "<PERSON><PERSON>", "k18": "Number", "k19": "String", "k20": "<PERSON><PERSON>", "k21": "<PERSON><PERSON>", "k22": "<PERSON><PERSON>", "k23": "<PERSON><PERSON>", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "Number", "k32": "Number", "k33": "String", "k34": "String", "k35": "String", "k36": "<PERSON><PERSON>", "k37": "Number", "k38": "<PERSON><PERSON>", "k39": "String", "k40": "<PERSON><PERSON>", "k41": "<PERSON><PERSON>", "k42": "<PERSON><PERSON>", "k43": "<PERSON><PERSON>", "k44": "<PERSON><PERSON>", "k45": "<PERSON><PERSON>", "k46": "<PERSON><PERSON>", "k47": "<PERSON><PERSON>", "k48": "<PERSON><PERSON>", "k49": "<PERSON><PERSON>", "k50": "<PERSON><PERSON>", "k51": "<PERSON><PERSON>", "k52": "<PERSON><PERSON>", "k53": "String", "k54": "String", "k55": "Number", "k56": "Number", "k57": "Number"}}, {"colName": "collection", "count": 26092, "size": 10984664, "storageSize": 4849664, "totalIndexSize": 249856, "indexSizes": {"_id_": 249856}, "avgObjSize": 420, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "329dbd72-202e-42ff-befe-ac1d58c90062"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "String", "k2": "String", "k3": "String", "k4": "Number", "k5": "Number", "k6": "Number", "k7": "Number", "k8": "Number", "k9": "Number", "k10": "Number", "k11": "Number", "k12": "Number", "k13": "Number", "k14": "Number", "k15": "Number", "k16": "NumberLong", "k17": "Number", "k18": "String", "k19": "BSON"}}, {"colName": "collection", "count": 2, "size": 176, "storageSize": 16384, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 88, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "eeec8a65-7b57-4caa-8356-0d9aad689d95"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "Number", "k3": "String", "k4": "Number"}}, {"colName": "collection", "count": 7, "size": 758040, "storageSize": 270336, "totalIndexSize": 73728, "indexSizes": {"_id_": 36864, "files_id_1_n_1": 36864}, "avgObjSize": 108291, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "ff830f35-14b5-4f9b-8d08-499b80043f09"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "ObjectId", "k2": "Number", "k3": "BinData"}}, {"colName": "collection", "count": 3, "size": 1637, "storageSize": 36864, "totalIndexSize": 73728, "indexSizes": {"_id_": 36864, "filename_1_uploadDate_1": 36864}, "avgObjSize": 545, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "f24cacfd-0b0d-44bb-992f-991c7c9f0da6"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "String", "k2": "NumberLong", "k3": "Number", "k4": "Date", "k5": "String", "k6": "BSON"}}, {"colName": "collection", "count": 4395, "size": 4723452, "storageSize": 704512, "totalIndexSize": 49152, "indexSizes": {"_id_": 49152}, "avgObjSize": 1074, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "e303fe3a-9960-4863-a95d-dd61090975ab"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "String", "k48": "String", "k49": "String", "k50": "String", "k51": "String"}}, {"colName": "collection", "count": 3008, "size": 3383170, "storageSize": 507904, "totalIndexSize": 40960, "indexSizes": {"_id_": 40960}, "avgObjSize": 1124, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "0e60c788-7ec2-4bc8-937c-44a2ef8b40b6"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "String", "k48": "String", "k49": "String", "k50": "String", "k51": "String", "k52": "String", "k53": "String", "k54": "String"}}, {"colName": "collection", "count": 2355, "size": 4817719, "storageSize": 1241088, "totalIndexSize": 32768, "indexSizes": {"_id_": 32768}, "avgObjSize": 2045, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "6536474a-d205-4bf6-a91a-3c78ba7a2baa"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "String", "k48": "String", "k49": "String", "k50": "String", "k51": "String", "k52": "String", "k53": "String", "k54": "String", "k55": "String", "k56": "String", "k57": "String", "k58": "String", "k59": "String", "k60": "String", "k61": "String", "k62": "String", "k63": "String", "k64": "String", "k65": "String", "k66": "String", "k67": "String", "k68": "String", "k69": "String", "k70": "String", "k71": "String", "k72": "String", "k73": "String", "k74": "String", "k75": "String", "k76": "String", "k77": "String", "k78": "String", "k79": "String", "k80": "String", "k81": "String", "k82": "String", "k83": "String", "k84": "String", "k85": "String", "k86": "String", "k87": "String", "k88": "String", "k89": "String", "k90": "String", "k91": "String", "k92": "String", "k93": "String", "k94": "String", "k95": "String", "k96": "String", "k97": "String", "k98": "String", "k99": "String", "k100": "String", "k101": "String", "k102": "String", "k103": "String", "k104": "String", "k105": "String", "k106": "String", "k107": "String", "k108": "String"}}, {"colName": "collection", "count": 1787, "size": 3135082, "storageSize": 946176, "totalIndexSize": 28672, "indexSizes": {"_id_": 28672}, "avgObjSize": 1754, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "9c1c0756-e15d-4689-9024-6a7343573c43"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "String", "k48": "String", "k49": "String", "k50": "String", "k51": "String", "k52": "String", "k53": "String", "k54": "String", "k55": "String", "k56": "String", "k57": "String", "k58": "String", "k59": "String", "k60": "String", "k61": "String", "k62": "String", "k63": "String", "k64": "String", "k65": "String", "k66": "String", "k67": "String", "k68": "String", "k69": "String", "k70": "String", "k71": "String", "k72": "String", "k73": "String", "k74": "String", "k75": "String", "k76": "String", "k77": "String", "k78": "String", "k79": "String", "k80": "String", "k81": "String", "k82": "String", "k83": "String", "k84": "String", "k85": "String", "k86": "String", "k87": "String", "k88": "String", "k89": "String", "k90": "String", "k91": "String"}}, {"colName": "collection", "count": 4884, "size": 4617546, "storageSize": 679936, "totalIndexSize": 57344, "indexSizes": {"_id_": 57344}, "avgObjSize": 945, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "8bc1f084-0e8a-44c0-bc23-429e9160a7ed"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String"}}, {"colName": "collection", "count": 3911, "size": 3792290, "storageSize": 1134592, "totalIndexSize": 45056, "indexSizes": {"_id_": 45056}, "avgObjSize": 969, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "0382f3e8-e888-40c4-9bad-fb473968fcb9"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String"}}, {"colName": "collection", "count": 3036, "size": 8692605, "storageSize": 1921024, "totalIndexSize": 40960, "indexSizes": {"_id_": 40960}, "avgObjSize": 2863, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "c66d3df3-5b37-467c-81ec-8cb4a61c7a49"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "String", "k48": "String", "k49": "String", "k50": "String", "k51": "String", "k52": "String", "k53": "String", "k54": "String", "k55": "String", "k56": "String", "k57": "String", "k58": "String", "k59": "String", "k60": "String", "k61": "String", "k62": "String", "k63": "String", "k64": "String", "k65": "String", "k66": "String", "k67": "String", "k68": "String", "k69": "String", "k70": "String", "k71": "String", "k72": "String", "k73": "String", "k74": "String", "k75": "String", "k76": "String", "k77": "String", "k78": "String", "k79": "String", "k80": "String", "k81": "String", "k82": "String", "k83": "String", "k84": "String", "k85": "String", "k86": "String", "k87": "String", "k88": "String", "k89": "String", "k90": "String", "k91": "String", "k92": "String", "k93": "String", "k94": "String", "k95": "String", "k96": "String", "k97": "String", "k98": "String", "k99": "String", "k100": "String", "k101": "String", "k102": "String", "k103": "String", "k104": "String", "k105": "String", "k106": "String", "k107": "String", "k108": "String", "k109": "String", "k110": "String", "k111": "String", "k112": "String", "k113": "String", "k114": "String", "k115": "String", "k116": "String", "k117": "String", "k118": "String", "k119": "String", "k120": "String", "k121": "String", "k122": "String", "k123": "String", "k124": "String", "k125": "String", "k126": "String", "k127": "String", "k128": "String", "k129": "String", "k130": "String", "k131": "String", "k132": "String", "k133": "String", "k134": "String", "k135": "String", "k136": "String", "k137": "String", "k138": "String", "k139": "String", "k140": "String", "k141": "String", "k142": "String", "k143": "String", "k144": "String", "k145": "String", "k146": "String", "k147": "String", "k148": "String", "k149": "String", "k150": "String", "k151": "String", "k152": "String", "k153": "String", "k154": "String", "k155": "String", "k156": "String", "k157": "String", "k158": "String", "k159": "String", "k160": "String"}}, {"colName": "collection", "count": 9319, "size": 7045199, "storageSize": 4145152, "totalIndexSize": 94208, "indexSizes": {"_id_": 94208}, "avgObjSize": 756, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "494d17cf-6692-42d4-8bf9-bce815a1c3b0"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String"}}, {"colName": "collection", "count": 5720, "size": 4919947, "storageSize": 745472, "totalIndexSize": 61440, "indexSizes": {"_id_": 61440}, "avgObjSize": 860, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "81547e1f-1e8b-4630-a374-479ffdbb2da9"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String"}}, {"colName": "collection", "count": 5576, "size": 4659490, "storageSize": 978944, "totalIndexSize": 61440, "indexSizes": {"_id_": 61440}, "avgObjSize": 835, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "7394bd36-331d-4c28-8531-08aa94ce02ec"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String"}}, {"colName": "collection", "count": 3326, "size": 3941615, "storageSize": 880640, "totalIndexSize": 40960, "indexSizes": {"_id_": 40960}, "avgObjSize": 1185, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "f69577d9-a584-4acd-84b0-b8a2f74d92ce"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "String", "k48": "String", "k49": "String", "k50": "String", "k51": "String", "k52": "String", "k53": "String", "k54": "String", "k55": "String"}}, {"colName": "collection", "count": 432, "size": 3871214, "storageSize": 2387968, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 8961, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "e188e02b-cc19-4131-8f53-7281675ab488"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "String", "k48": "String", "k49": "String", "k50": "String", "k51": "String", "k52": "String", "k53": "String", "k54": "String", "k55": "String", "k56": "String", "k57": "String", "k58": "String", "k59": "String", "k60": "String", "k61": "String", "k62": "String", "k63": "String", "k64": "String", "k65": "String", "k66": "String", "k67": "String", "k68": "String", "k69": "String", "k70": "String", "k71": "String", "k72": "String", "k73": "String", "k74": "String", "k75": "String", "k76": "String", "k77": "String", "k78": "String", "k79": "String", "k80": "String", "k81": "String", "k82": "String", "k83": "String", "k84": "String", "k85": "String", "k86": "String", "k87": "String", "k88": "String", "k89": "String", "k90": "String", "k91": "String", "k92": "String", "k93": "String", "k94": "String", "k95": "String", "k96": "String", "k97": "String", "k98": "String", "k99": "String", "k100": "String", "k101": "String", "k102": "String", "k103": "String", "k104": "String", "k105": "String", "k106": "String", "k107": "String", "k108": "String", "k109": "String", "k110": "String", "k111": "String", "k112": "String", "k113": "String", "k114": "String", "k115": "String", "k116": "String", "k117": "String", "k118": "String", "k119": "String", "k120": "String", "k121": "String", "k122": "String", "k123": "String", "k124": "String", "k125": "String", "k126": "String", "k127": "String", "k128": "String", "k129": "String", "k130": "String", "k131": "String", "k132": "String", "k133": "String", "k134": "String", "k135": "String", "k136": "String", "k137": "String", "k138": "String", "k139": "String", "k140": "String", "k141": "String", "k142": "String", "k143": "String", "k144": "String", "k145": "String", "k146": "String", "k147": "String", "k148": "String", "k149": "String", "k150": "String", "k151": "String", "k152": "String", "k153": "String", "k154": "String", "k155": "String", "k156": "String", "k157": "String", "k158": "String", "k159": "String", "k160": "String", "k161": "String", "k162": "String", "k163": "String", "k164": "String", "k165": "String", "k166": "String", "k167": "String", "k168": "String", "k169": "String", "k170": "String", "k171": "String", "k172": "String", "k173": "String", "k174": "String", "k175": "String", "k176": "String", "k177": "String", "k178": "String", "k179": "String", "k180": "String", "k181": "String", "k182": "String", "k183": "String", "k184": "String", "k185": "String", "k186": "String", "k187": "String", "k188": "String", "k189": "String", "k190": "String", "k191": "String", "k192": "String", "k193": "String", "k194": "String", "k195": "String", "k196": "String", "k197": "String", "k198": "String", "k199": "String", "k200": "String", "k201": "String", "k202": "String", "k203": "String", "k204": "String", "k205": "String", "k206": "String", "k207": "String", "k208": "String", "k209": "String", "k210": "String", "k211": "String", "k212": "String", "k213": "String", "k214": "String", "k215": "String", "k216": "String", "k217": "String", "k218": "String", "k219": "String", "k220": "String", "k221": "String", "k222": "String", "k223": "String", "k224": "String", "k225": "String", "k226": "String", "k227": "String", "k228": "String", "k229": "String", "k230": "String", "k231": "String", "k232": "String", "k233": "String", "k234": "String", "k235": "String", "k236": "String", "k237": "String", "k238": "String", "k239": "String", "k240": "String", "k241": "String", "k242": "String", "k243": "String", "k244": "String", "k245": "String", "k246": "String", "k247": "String", "k248": "String", "k249": "String", "k250": "String", "k251": "String", "k252": "String", "k253": "String", "k254": "String", "k255": "String", "k256": "String", "k257": "String", "k258": "String", "k259": "String", "k260": "String", "k261": "String", "k262": "String", "k263": "String", "k264": "String", "k265": "String", "k266": "String", "k267": "String", "k268": "String", "k269": "String", "k270": "String", "k271": "String", "k272": "String", "k273": "String", "k274": "String", "k275": "String", "k276": "String", "k277": "String", "k278": "String", "k279": "String", "k280": "String", "k281": "String", "k282": "String", "k283": "String", "k284": "String", "k285": "String", "k286": "String", "k287": "String", "k288": "String", "k289": "String", "k290": "String", "k291": "String", "k292": "String", "k293": "String", "k294": "String", "k295": "String", "k296": "String", "k297": "String", "k298": "String", "k299": "String", "k300": "String", "k301": "String", "k302": "String", "k303": "String", "k304": "String", "k305": "String", "k306": "String", "k307": "String", "k308": "String", "k309": "String", "k310": "String", "k311": "String", "k312": "String", "k313": "String", "k314": "String", "k315": "String", "k316": "String", "k317": "String", "k318": "String", "k319": "String", "k320": "String", "k321": "String", "k322": "String", "k323": "String", "k324": "String", "k325": "String", "k326": "String", "k327": "String", "k328": "String", "k329": "String", "k330": "String", "k331": "String", "k332": "String", "k333": "String", "k334": "String", "k335": "String", "k336": "String", "k337": "String", "k338": "String", "k339": "String", "k340": "String", "k341": "String", "k342": "String", "k343": "String", "k344": "String", "k345": "String", "k346": "String", "k347": "String", "k348": "String", "k349": "String", "k350": "String", "k351": "String", "k352": "String", "k353": "String", "k354": "String", "k355": "String", "k356": "String", "k357": "String", "k358": "String", "k359": "String", "k360": "String", "k361": "String", "k362": "String", "k363": "String", "k364": "String", "k365": "String", "k366": "String", "k367": "String"}}, {"colName": "collection", "count": 2593, "size": 4079878, "storageSize": 2396160, "totalIndexSize": 32768, "indexSizes": {"_id_": 32768}, "avgObjSize": 1573, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "e2450d10-6d22-456f-b161-08c78bafeb92"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "BSON", "k46": "String", "k47": "String", "k48": "String", "k49": "String", "k50": "String", "k51": "String", "k52": "String", "k53": "String", "k54": "String", "k55": "String", "k56": "String", "k57": "String", "k58": "String", "k59": "String", "k60": "String", "k61": "String", "k62": "String", "k63": "String", "k64": "String", "k65": "String", "k66": "String", "k67": "String", "k68": "String", "k69": "String", "k70": "String"}}, {"colName": "collection", "count": 22, "size": 16902, "storageSize": 16384, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 768, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "0c1899d2-883c-4907-81f0-1d28eaff1b90"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "<PERSON><PERSON>", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "<PERSON><PERSON>", "k6": "<PERSON><PERSON>", "k7": "<PERSON><PERSON>", "k8": "<PERSON><PERSON>", "k9": "<PERSON><PERSON>", "k10": "<PERSON><PERSON>", "k11": "<PERSON><PERSON>", "k12": "<PERSON><PERSON>", "k13": "<PERSON><PERSON>", "k14": "<PERSON><PERSON>", "k15": "String", "k16": "<PERSON><PERSON>", "k17": "Number", "k18": "String", "k19": "<PERSON><PERSON>", "k20": "Number", "k21": "<PERSON><PERSON>", "k22": "<PERSON><PERSON>", "k23": "Number", "k24": "Date", "k25": "<PERSON><PERSON>", "k26": "String", "k27": "<PERSON><PERSON>", "k28": "<PERSON><PERSON>", "k29": "<PERSON><PERSON>", "k30": "<PERSON><PERSON>", "k31": "<PERSON><PERSON>", "k32": "<PERSON><PERSON>", "k33": "<PERSON><PERSON>", "k34": "<PERSON><PERSON>", "k35": "<PERSON><PERSON>", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "<PERSON><PERSON>", "k41": "<PERSON><PERSON>", "k42": "<PERSON><PERSON>", "k43": "<PERSON><PERSON>", "k44": "String", "k45": "<PERSON><PERSON>", "k46": "Number", "k47": "Date", "k48": "<PERSON><PERSON>", "k49": "<PERSON><PERSON>", "k50": "<PERSON><PERSON>", "k51": "<PERSON><PERSON>", "k52": "<PERSON><PERSON>", "k53": "<PERSON><PERSON>", "k54": "<PERSON><PERSON>", "k55": "<PERSON><PERSON>", "k56": "String", "k57": "<PERSON><PERSON>"}}, {"colName": "collection", "count": 3731, "size": 3198656, "storageSize": 962560, "totalIndexSize": 45056, "indexSizes": {"_id_": 45056}, "avgObjSize": 857, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "9f803444-98ea-405b-8e7f-86cf390ae9a8"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "BSON", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String"}}, {"colName": "collection", "count": 109, "size": 87983, "storageSize": 24576, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 807, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "e521ffd3-82d5-478e-a6c3-cb14f05402ad"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "<PERSON><PERSON>", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "<PERSON><PERSON>", "k6": "<PERSON><PERSON>", "k7": "<PERSON><PERSON>", "k8": "<PERSON><PERSON>", "k9": "<PERSON><PERSON>", "k10": "<PERSON><PERSON>", "k11": "<PERSON><PERSON>", "k12": "<PERSON><PERSON>", "k13": "String", "k14": "String", "k15": "String", "k16": "<PERSON><PERSON>", "k17": "Number", "k18": "<PERSON><PERSON>", "k19": "<PERSON><PERSON>", "k20": "Number", "k21": "<PERSON><PERSON>", "k22": "<PERSON><PERSON>", "k23": "<PERSON><PERSON>", "k24": "<PERSON><PERSON>", "k25": "<PERSON><PERSON>", "k26": "String", "k27": "<PERSON><PERSON>", "k28": "<PERSON><PERSON>", "k29": "<PERSON><PERSON>", "k30": "<PERSON><PERSON>", "k31": "<PERSON><PERSON>", "k32": "<PERSON><PERSON>", "k33": "String", "k34": "<PERSON><PERSON>", "k35": "<PERSON><PERSON>", "k36": "<PERSON><PERSON>", "k37": "<PERSON><PERSON>", "k38": "<PERSON><PERSON>", "k39": "<PERSON><PERSON>", "k40": "<PERSON><PERSON>", "k41": "<PERSON><PERSON>", "k42": "<PERSON><PERSON>", "k43": "String", "k44": "<PERSON><PERSON>", "k45": "Number", "k46": "Date", "k47": "<PERSON><PERSON>", "k48": "String", "k49": "<PERSON><PERSON>", "k50": "<PERSON><PERSON>", "k51": "<PERSON><PERSON>", "k52": "<PERSON><PERSON>", "k53": "String", "k54": "<PERSON><PERSON>", "k55": "<PERSON><PERSON>", "k56": "<PERSON><PERSON>", "k57": "<PERSON><PERSON>", "k58": "<PERSON><PERSON>", "k59": "String", "k60": "String", "k61": "<PERSON><PERSON>", "k62": "<PERSON><PERSON>"}}, {"colName": "collection", "count": 2000, "size": 1836600, "storageSize": 569344, "totalIndexSize": 28672, "indexSizes": {"_id_": 28672}, "avgObjSize": 918, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "d1afdef3-f2a2-41cd-a636-9f4fb4f19b86"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "<PERSON><PERSON>", "k6": "Number", "k7": "<PERSON><PERSON>", "k8": "String", "k9": "<PERSON><PERSON>", "k10": "Number", "k11": "String", "k12": "<PERSON><PERSON>", "k13": "NumberLong", "k14": "Number", "k15": "Number", "k16": "Date", "k17": "String", "k18": "<PERSON><PERSON>", "k19": "<PERSON><PERSON>", "k20": "<PERSON><PERSON>", "k21": "<PERSON><PERSON>", "k22": "<PERSON><PERSON>", "k23": "<PERSON><PERSON>", "k24": "Number", "k25": "<PERSON><PERSON>", "k26": "<PERSON><PERSON>", "k27": "String", "k28": "<PERSON><PERSON>", "k29": "String", "k30": "String", "k31": "<PERSON><PERSON>", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "<PERSON><PERSON>", "k37": "String", "k38": "<PERSON><PERSON>", "k39": "Date", "k40": "<PERSON><PERSON>", "k41": "<PERSON><PERSON>", "k42": "<PERSON><PERSON>", "k43": "<PERSON><PERSON>", "k44": "String", "k45": "String", "k46": "<PERSON><PERSON>", "k47": "<PERSON><PERSON>", "k48": "<PERSON><PERSON>"}}, {"colName": "collection", "count": 7231, "size": 4644735, "storageSize": 1384448, "totalIndexSize": 77824, "indexSizes": {"_id_": 77824}, "avgObjSize": 642, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "26e32171-c3c8-4359-9560-d43948bc6920"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "<PERSON><PERSON>", "k6": "String", "k7": "String", "k8": "String", "k9": "<PERSON><PERSON>", "k10": "Number", "k11": "Number", "k12": "<PERSON><PERSON>", "k13": "Date", "k14": "String", "k15": "String", "k16": "<PERSON><PERSON>", "k17": "Number", "k18": "Number", "k19": "<PERSON><PERSON>", "k20": "String", "k21": "String", "k22": "String", "k23": "<PERSON><PERSON>", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "Date", "k31": "<PERSON><PERSON>", "k32": "Number", "k33": "Number", "k34": "<PERSON><PERSON>", "k35": "String", "k36": "String"}}, {"colName": "collection", "count": 3256, "size": 5886792, "storageSize": 3584000, "totalIndexSize": 86016, "indexSizes": {"_id_": 86016}, "avgObjSize": 1807, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "49261b78-640a-41b2-9d76-50318629006d"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "String", "k48": "String", "k49": "String", "k50": "String", "k51": "String", "k52": "String", "k53": "String", "k54": "String", "k55": "BSON", "k56": "String", "k57": "String", "k58": "String", "k59": "String", "k60": "String", "k61": "String", "k62": "String", "k63": "String", "k64": "String", "k65": "String", "k66": "String", "k67": "String", "k68": "String", "k69": "String", "k70": "String", "k71": "String", "k72": "String", "k73": "String", "k74": "String", "k75": "String", "k76": "String", "k77": "String", "k78": "String"}}, {"colName": "collection", "count": 6, "size": 8880, "storageSize": 36864, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 1480, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "425ed881-98d8-466e-925b-5ad0921eb7db"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "<PERSON><PERSON>", "k17": "String", "k18": "String", "k19": "<PERSON><PERSON>", "k20": "String", "k21": "String", "k22": "<PERSON><PERSON>", "k23": "<PERSON><PERSON>", "k24": "<PERSON><PERSON>", "k25": "String", "k26": "<PERSON><PERSON>", "k27": "<PERSON><PERSON>", "k28": "<PERSON><PERSON>", "k29": "String", "k30": "<PERSON><PERSON>", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "<PERSON><PERSON>", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "BSON", "k47": "String", "k48": "String", "k49": "String", "k50": "String", "k51": "String", "k52": "String", "k53": "String", "k54": "String", "k55": "String", "k56": "String", "k57": "<PERSON><PERSON>", "k58": "<PERSON><PERSON>", "k59": "String", "k60": "String", "k61": "String", "k62": "String", "k63": "String", "k64": "<PERSON><PERSON>", "k65": "<PERSON><PERSON>", "k66": "String", "k67": "String", "k68": "String"}}, {"colName": "collection", "count": 523, "size": 482987, "storageSize": 86016, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 923, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "191548ea-1d62-4f43-aaf6-81e56f28fbd5"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "Number", "k6": "<PERSON><PERSON>", "k7": "String", "k8": "<PERSON><PERSON>", "k9": "String", "k10": "Number", "k11": "String", "k12": "<PERSON><PERSON>", "k13": "Number", "k14": "Number", "k15": "Number", "k16": "<PERSON><PERSON>", "k17": "Number", "k18": "String", "k19": "<PERSON><PERSON>", "k20": "String", "k21": "<PERSON><PERSON>", "k22": "String", "k23": "String", "k24": "Number", "k25": "<PERSON><PERSON>", "k26": "Number", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "Number", "k33": "String", "k34": "<PERSON><PERSON>", "k35": "String", "k36": "String", "k37": "Number", "k38": "String", "k39": "<PERSON><PERSON>", "k40": "Date", "k41": "<PERSON><PERSON>", "k42": "<PERSON><PERSON>", "k43": "Number", "k44": "Number", "k45": "Number", "k46": "String", "k47": "String", "k48": "<PERSON><PERSON>"}}, {"colName": "collection", "count": 4690, "size": 4127962, "storageSize": 1290240, "totalIndexSize": 53248, "indexSizes": {"_id_": 53248}, "avgObjSize": 880, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "675a36ed-42ef-402a-9d2c-c9b4ed7011d6"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "BSON", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String"}}, {"colName": "collection", "count": 8, "size": 9024, "storageSize": 16384, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 1128, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "7aa38ac0-24ed-490e-9bed-f7ad3cdb9656"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "<PERSON><PERSON>", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "<PERSON><PERSON>", "k6": "<PERSON><PERSON>", "k7": "<PERSON><PERSON>", "k8": "<PERSON><PERSON>", "k9": "<PERSON><PERSON>", "k10": "<PERSON><PERSON>", "k11": "<PERSON><PERSON>", "k12": "<PERSON><PERSON>", "k13": "<PERSON><PERSON>", "k14": "String", "k15": "String", "k16": "String", "k17": "<PERSON><PERSON>", "k18": "Number", "k19": "String", "k20": "<PERSON><PERSON>", "k21": "<PERSON><PERSON>", "k22": "<PERSON><PERSON>", "k23": "<PERSON><PERSON>", "k24": "NumberLong", "k25": "<PERSON><PERSON>", "k26": "<PERSON><PERSON>", "k27": "Number", "k28": "String", "k29": "String", "k30": "String", "k31": "<PERSON><PERSON>", "k32": "<PERSON><PERSON>", "k33": "<PERSON><PERSON>", "k34": "String", "k35": "<PERSON><PERSON>", "k36": "<PERSON><PERSON>", "k37": "String", "k38": "<PERSON><PERSON>", "k39": "<PERSON><PERSON>", "k40": "<PERSON><PERSON>", "k41": "<PERSON><PERSON>", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "<PERSON><PERSON>", "k47": "<PERSON><PERSON>", "k48": "<PERSON><PERSON>", "k49": "<PERSON><PERSON>", "k50": "<PERSON><PERSON>", "k51": "<PERSON><PERSON>", "k52": "String", "k53": "String", "k54": "<PERSON><PERSON>", "k55": "Number", "k56": "String", "k57": "<PERSON><PERSON>", "k58": "String", "k59": "String", "k60": "<PERSON><PERSON>", "k61": "<PERSON><PERSON>", "k62": "<PERSON><PERSON>", "k63": "<PERSON><PERSON>", "k64": "<PERSON><PERSON>", "k65": "<PERSON><PERSON>"}}, {"colName": "collection", "count": 2000, "size": 1440169, "storageSize": 843776, "totalIndexSize": 28672, "indexSizes": {"_id_": 28672}, "avgObjSize": 720, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "c3bb0557-f913-4219-b8da-53975c9b2b29"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "Number", "k2": "BSON", "k3": "NumberLong", "k4": "String", "k5": "<PERSON><PERSON>", "k6": "<PERSON><PERSON>", "k7": "<PERSON><PERSON>", "k8": "String", "k9": "String", "k10": "Number", "k11": "String", "k12": "<PERSON><PERSON>", "k13": "Number", "k14": "Number", "k15": "Number", "k16": "Number", "k17": "Number", "k18": "<PERSON><PERSON>", "k19": "Date", "k20": "String", "k21": "String", "k22": "<PERSON><PERSON>", "k23": "Number", "k24": "String", "k25": "<PERSON><PERSON>", "k26": "String", "k27": "String", "k28": "String", "k29": "<PERSON><PERSON>", "k30": "String", "k31": "String", "k32": "Number", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "Number", "k38": "Date", "k39": "<PERSON><PERSON>", "k40": "Number", "k41": "String", "k42": "String", "k43": "Number"}}, {"colName": "collection", "count": 1, "size": 396033, "storageSize": 53248, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 396033, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "b8714192-e3da-4e9f-b17b-51197efac820"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "Array", "k2": "BSON"}}, {"colName": "collection", "count": 2, "size": 216, "storageSize": 32768, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 108, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "fa981d93-0c85-432c-834b-25ea42e42c7d"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "Number", "k3": "String", "k4": "String", "k5": "Number"}}, {"colName": "collection", "count": 2, "size": 1329, "storageSize": 16384, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 664, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "d3ab3dd1-7cb0-43fb-b961-d05086654875"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "Number", "k6": "String", "k7": "<PERSON><PERSON>", "k8": "Number", "k9": "String", "k10": "String", "k11": "NumberLong", "k12": "Number", "k13": "String", "k14": "String", "k15": "<PERSON><PERSON>", "k16": "String", "k17": "<PERSON><PERSON>", "k18": "<PERSON><PERSON>", "k19": "String", "k20": "String", "k21": "<PERSON><PERSON>", "k22": "<PERSON><PERSON>", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "<PERSON><PERSON>", "k30": "Number", "k31": "String", "k32": "<PERSON><PERSON>", "k33": "<PERSON><PERSON>", "k34": "String", "k35": "String"}}, {"colName": "collection", "count": 704, "size": 645330, "storageSize": 221184, "totalIndexSize": 20480, "indexSizes": {"_id_": 20480}, "avgObjSize": 916, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "b2c40189-2914-4c2d-a370-1d488efc5a80"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "<PERSON><PERSON>", "k6": "<PERSON><PERSON>", "k7": "<PERSON><PERSON>", "k8": "<PERSON><PERSON>", "k9": "<PERSON><PERSON>", "k10": "<PERSON><PERSON>", "k11": "<PERSON><PERSON>", "k12": "<PERSON><PERSON>", "k13": "<PERSON><PERSON>", "k14": "Number", "k15": "<PERSON><PERSON>", "k16": "<PERSON><PERSON>", "k17": "String", "k18": "<PERSON><PERSON>", "k19": "Number", "k20": "String", "k21": "<PERSON><PERSON>", "k22": "NumberLong", "k23": "<PERSON><PERSON>", "k24": "<PERSON><PERSON>", "k25": "Number", "k26": "String", "k27": "<PERSON><PERSON>", "k28": "String", "k29": "<PERSON><PERSON>", "k30": "<PERSON><PERSON>", "k31": "<PERSON><PERSON>", "k32": "<PERSON><PERSON>", "k33": "<PERSON><PERSON>", "k34": "<PERSON><PERSON>", "k35": "<PERSON><PERSON>", "k36": "String", "k37": "String", "k38": "Number", "k39": "String", "k40": "<PERSON><PERSON>", "k41": "String", "k42": "String", "k43": "String", "k44": "<PERSON><PERSON>", "k45": "<PERSON><PERSON>", "k46": "<PERSON><PERSON>", "k47": "<PERSON><PERSON>", "k48": "String", "k49": "String", "k50": "<PERSON><PERSON>", "k51": "String", "k52": "String", "k53": "String", "k54": "<PERSON><PERSON>", "k55": "Number", "k56": "String", "k57": "<PERSON><PERSON>", "k58": "<PERSON><PERSON>", "k59": "<PERSON><PERSON>", "k60": "<PERSON><PERSON>", "k61": "String", "k62": "String", "k63": "<PERSON><PERSON>", "k64": "<PERSON><PERSON>", "k65": "String", "k66": "<PERSON><PERSON>"}}, {"colName": "collection", "count": 150, "size": 134036, "storageSize": 32768, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 893, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "28d0684a-e329-4f53-935a-0fb4af5e030a"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "<PERSON><PERSON>", "k6": "<PERSON><PERSON>", "k7": "<PERSON><PERSON>", "k8": "<PERSON><PERSON>", "k9": "<PERSON><PERSON>", "k10": "<PERSON><PERSON>", "k11": "String", "k12": "<PERSON><PERSON>", "k13": "Number", "k14": "<PERSON><PERSON>", "k15": "<PERSON><PERSON>", "k16": "String", "k17": "<PERSON><PERSON>", "k18": "Number", "k19": "String", "k20": "String", "k21": "Number", "k22": "<PERSON><PERSON>", "k23": "<PERSON><PERSON>", "k24": "Number", "k25": "<PERSON><PERSON>", "k26": "<PERSON><PERSON>", "k27": "String", "k28": "<PERSON><PERSON>", "k29": "<PERSON><PERSON>", "k30": "<PERSON><PERSON>", "k31": "<PERSON><PERSON>", "k32": "String", "k33": "<PERSON><PERSON>", "k34": "<PERSON><PERSON>", "k35": "<PERSON><PERSON>", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "<PERSON><PERSON>", "k46": "Number", "k47": "Date", "k48": "<PERSON><PERSON>", "k49": "<PERSON><PERSON>", "k50": "<PERSON><PERSON>", "k51": "<PERSON><PERSON>", "k52": "String", "k53": "String", "k54": "String", "k55": "String", "k56": "String", "k57": "<PERSON><PERSON>", "k58": "<PERSON><PERSON>", "k59": "<PERSON><PERSON>"}}, {"colName": "collection", "count": 10, "size": 8162, "storageSize": 16384, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 816, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "a20b6fbd-8941-49cc-98ee-9e630082c7c7"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "<PERSON><PERSON>", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "<PERSON><PERSON>", "k6": "<PERSON><PERSON>", "k7": "<PERSON><PERSON>", "k8": "String", "k9": "<PERSON><PERSON>", "k10": "Number", "k11": "<PERSON><PERSON>", "k12": "String", "k13": "<PERSON><PERSON>", "k14": "<PERSON><PERSON>", "k15": "<PERSON><PERSON>", "k16": "NumberLong", "k17": "<PERSON><PERSON>", "k18": "<PERSON><PERSON>", "k19": "<PERSON><PERSON>", "k20": "String", "k21": "<PERSON><PERSON>", "k22": "<PERSON><PERSON>", "k23": "String", "k24": "<PERSON><PERSON>", "k25": "<PERSON><PERSON>", "k26": "<PERSON><PERSON>", "k27": "<PERSON><PERSON>", "k28": "String", "k29": "<PERSON><PERSON>", "k30": "<PERSON><PERSON>", "k31": "<PERSON><PERSON>", "k32": "<PERSON><PERSON>", "k33": "<PERSON><PERSON>", "k34": "<PERSON><PERSON>", "k35": "<PERSON><PERSON>", "k36": "<PERSON><PERSON>", "k37": "<PERSON><PERSON>", "k38": "<PERSON><PERSON>", "k39": "String", "k40": "<PERSON><PERSON>", "k41": "Number", "k42": "<PERSON><PERSON>", "k43": "String", "k44": "<PERSON><PERSON>", "k45": "<PERSON><PERSON>", "k46": "<PERSON><PERSON>", "k47": "<PERSON><PERSON>"}}, {"colName": "collection", "count": 17, "size": 12599, "storageSize": 16384, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 741, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "edc1c714-b0cc-40fb-a2a7-4f61641dd0d5"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "<PERSON><PERSON>", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "String", "k6": "String", "k7": "<PERSON><PERSON>", "k8": "String", "k9": "<PERSON><PERSON>", "k10": "Number", "k11": "String", "k12": "<PERSON><PERSON>", "k13": "<PERSON><PERSON>", "k14": "NumberLong", "k15": "<PERSON><PERSON>", "k16": "<PERSON><PERSON>", "k17": "String", "k18": "<PERSON><PERSON>", "k19": "<PERSON><PERSON>", "k20": "String", "k21": "Number", "k22": "String", "k23": "String", "k24": "<PERSON><PERSON>", "k25": "<PERSON><PERSON>", "k26": "<PERSON><PERSON>", "k27": "<PERSON><PERSON>", "k28": "<PERSON><PERSON>", "k29": "<PERSON><PERSON>", "k30": "<PERSON><PERSON>", "k31": "<PERSON><PERSON>", "k32": "<PERSON><PERSON>", "k33": "String", "k34": "<PERSON><PERSON>", "k35": "Number", "k36": "String", "k37": "String", "k38": "<PERSON><PERSON>", "k39": "<PERSON><PERSON>", "k40": "String", "k41": "<PERSON><PERSON>", "k42": "<PERSON><PERSON>", "k43": "<PERSON><PERSON>"}}, {"colName": "collection", "count": 235, "size": 210973, "storageSize": 45056, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 897, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "94cf35dd-1eeb-4774-9d88-1fb9d29cc8f6"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "<PERSON><PERSON>", "k6": "<PERSON><PERSON>", "k7": "<PERSON><PERSON>", "k8": "String", "k9": "Number", "k10": "<PERSON><PERSON>", "k11": "<PERSON><PERSON>", "k12": "<PERSON><PERSON>", "k13": "<PERSON><PERSON>", "k14": "<PERSON><PERSON>", "k15": "<PERSON><PERSON>", "k16": "<PERSON><PERSON>", "k17": "<PERSON><PERSON>", "k18": "<PERSON><PERSON>", "k19": "<PERSON><PERSON>", "k20": "<PERSON><PERSON>", "k21": "<PERSON><PERSON>", "k22": "<PERSON><PERSON>", "k23": "<PERSON><PERSON>", "k24": "<PERSON><PERSON>", "k25": "<PERSON><PERSON>", "k26": "Number", "k27": "String", "k28": "<PERSON><PERSON>", "k29": "Number", "k30": "String", "k31": "String", "k32": "NumberLong", "k33": "Number", "k34": "<PERSON><PERSON>", "k35": "String", "k36": "<PERSON><PERSON>", "k37": "String", "k38": "<PERSON><PERSON>", "k39": "<PERSON><PERSON>", "k40": "<PERSON><PERSON>", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "String", "k48": "String", "k49": "String", "k50": "<PERSON><PERSON>", "k51": "Number", "k52": "<PERSON><PERSON>", "k53": "String", "k54": "<PERSON><PERSON>", "k55": "<PERSON><PERSON>", "k56": "<PERSON><PERSON>", "k57": "String", "k58": "String"}}, {"colName": "collection", "count": 1583, "size": 1377590, "storageSize": 217088, "totalIndexSize": 24576, "indexSizes": {"_id_": 24576}, "avgObjSize": 870, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "94f130af-3a2d-4c38-9b63-e9c8e5f982da"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "<PERSON><PERSON>", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "<PERSON><PERSON>", "k6": "<PERSON><PERSON>", "k7": "<PERSON><PERSON>", "k8": "<PERSON><PERSON>", "k9": "<PERSON><PERSON>", "k10": "<PERSON><PERSON>", "k11": "<PERSON><PERSON>", "k12": "<PERSON><PERSON>", "k13": "<PERSON><PERSON>", "k14": "<PERSON><PERSON>", "k15": "<PERSON><PERSON>", "k16": "<PERSON><PERSON>", "k17": "<PERSON><PERSON>", "k18": "<PERSON><PERSON>", "k19": "<PERSON><PERSON>", "k20": "<PERSON><PERSON>", "k21": "<PERSON><PERSON>", "k22": "<PERSON><PERSON>", "k23": "String", "k24": "<PERSON><PERSON>", "k25": "Number", "k26": "<PERSON><PERSON>", "k27": "<PERSON><PERSON>", "k28": "<PERSON><PERSON>", "k29": "<PERSON><PERSON>", "k30": "NumberLong", "k31": "<PERSON><PERSON>", "k32": "<PERSON><PERSON>", "k33": "<PERSON><PERSON>", "k34": "<PERSON><PERSON>", "k35": "<PERSON><PERSON>", "k36": "<PERSON><PERSON>", "k37": "<PERSON><PERSON>", "k38": "<PERSON><PERSON>", "k39": "String", "k40": "<PERSON><PERSON>", "k41": "<PERSON><PERSON>", "k42": "<PERSON><PERSON>", "k43": "<PERSON><PERSON>", "k44": "<PERSON><PERSON>", "k45": "<PERSON><PERSON>", "k46": "<PERSON><PERSON>", "k47": "String", "k48": "Number", "k49": "<PERSON><PERSON>", "k50": "<PERSON><PERSON>", "k51": "<PERSON><PERSON>", "k52": "<PERSON><PERSON>", "k53": "<PERSON><PERSON>", "k54": "<PERSON><PERSON>", "k55": "<PERSON><PERSON>", "k56": "<PERSON><PERSON>", "k57": "<PERSON><PERSON>", "k58": "<PERSON><PERSON>", "k59": "<PERSON><PERSON>", "k60": "String", "k61": "<PERSON><PERSON>", "k62": "Number", "k63": "String", "k64": "<PERSON><PERSON>", "k65": "<PERSON><PERSON>", "k66": "<PERSON><PERSON>", "k67": "<PERSON><PERSON>", "k68": "<PERSON><PERSON>", "k69": "<PERSON><PERSON>", "k70": "<PERSON><PERSON>", "k71": "<PERSON><PERSON>", "k72": "<PERSON><PERSON>", "k73": "<PERSON><PERSON>", "k74": "<PERSON><PERSON>", "k75": "<PERSON><PERSON>"}}, {"colName": "collection", "count": 5000, "size": 7474138, "storageSize": 2002944, "totalIndexSize": 57344, "indexSizes": {"_id_": 57344}, "avgObjSize": 1494, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "5d5a5adf-edee-461b-a7ff-884ab47f7eee"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "<PERSON><PERSON>", "k6": "String", "k7": "String", "k8": "<PERSON><PERSON>", "k9": "<PERSON><PERSON>", "k10": "<PERSON><PERSON>", "k11": "String", "k12": "<PERSON><PERSON>", "k13": "String", "k14": "<PERSON><PERSON>", "k15": "Number", "k16": "String", "k17": "<PERSON><PERSON>", "k18": "Number", "k19": "String", "k20": "<PERSON><PERSON>", "k21": "String", "k22": "<PERSON><PERSON>", "k23": "NumberLong", "k24": "String", "k25": "Number", "k26": "String", "k27": "String", "k28": "<PERSON><PERSON>", "k29": "<PERSON><PERSON>", "k30": "String", "k31": "String", "k32": "<PERSON><PERSON>", "k33": "<PERSON><PERSON>", "k34": "<PERSON><PERSON>", "k35": "<PERSON><PERSON>", "k36": "<PERSON><PERSON>", "k37": "<PERSON><PERSON>", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "<PERSON><PERSON>", "k46": "<PERSON><PERSON>", "k47": "String", "k48": "String", "k49": "String", "k50": "<PERSON><PERSON>", "k51": "Number", "k52": "String", "k53": "<PERSON><PERSON>", "k54": "String", "k55": "String", "k56": "<PERSON><PERSON>", "k57": "<PERSON><PERSON>", "k58": "<PERSON><PERSON>", "k59": "String", "k60": "String", "k61": "String", "k62": "String", "k63": "String", "k64": "String", "k65": "<PERSON><PERSON>", "k66": "<PERSON><PERSON>", "k67": "<PERSON><PERSON>", "k68": "<PERSON><PERSON>"}}, {"colName": "collection", "count": 27, "size": 22086, "storageSize": 16384, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 818, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "bebe9f46-e380-4385-b1ca-96462bcf8103"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "Number", "k6": "String", "k7": "<PERSON><PERSON>", "k8": "Number", "k9": "String", "k10": "String", "k11": "NumberLong", "k12": "Number", "k13": "<PERSON><PERSON>", "k14": "String", "k15": "<PERSON><PERSON>", "k16": "String", "k17": "<PERSON><PERSON>", "k18": "<PERSON><PERSON>", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "<PERSON><PERSON>", "k30": "Number", "k31": "String", "k32": "<PERSON><PERSON>", "k33": "<PERSON><PERSON>", "k34": "String", "k35": "String"}}, {"colName": "collection", "count": 92, "size": 144471, "storageSize": 40960, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 1570, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "d91b8d43-b282-49f9-9994-9cc5fa71cace"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "<PERSON><PERSON>", "k6": "<PERSON><PERSON>", "k7": "<PERSON><PERSON>", "k8": "<PERSON><PERSON>", "k9": "<PERSON><PERSON>", "k10": "<PERSON><PERSON>", "k11": "Number", "k12": "String", "k13": "<PERSON><PERSON>", "k14": "Number", "k15": "<PERSON><PERSON>", "k16": "<PERSON><PERSON>", "k17": "<PERSON><PERSON>", "k18": "<PERSON><PERSON>", "k19": "String", "k20": "String", "k21": "String", "k22": "NumberLong", "k23": "Number", "k24": "<PERSON><PERSON>", "k25": "String", "k26": "<PERSON><PERSON>", "k27": "String", "k28": "<PERSON><PERSON>", "k29": "String", "k30": "<PERSON><PERSON>", "k31": "String", "k32": "<PERSON><PERSON>", "k33": "<PERSON><PERSON>", "k34": "<PERSON><PERSON>", "k35": "<PERSON><PERSON>", "k36": "<PERSON><PERSON>", "k37": "<PERSON><PERSON>", "k38": "<PERSON><PERSON>", "k39": "<PERSON><PERSON>", "k40": "<PERSON><PERSON>", "k41": "<PERSON><PERSON>", "k42": "String", "k43": "<PERSON><PERSON>", "k44": "String", "k45": "String", "k46": "String", "k47": "String", "k48": "String", "k49": "String", "k50": "<PERSON><PERSON>", "k51": "<PERSON><PERSON>", "k52": "String", "k53": "String", "k54": "<PERSON><PERSON>", "k55": "String", "k56": "String", "k57": "String", "k58": "String", "k59": "String", "k60": "String", "k61": "String", "k62": "String", "k63": "String", "k64": "String", "k65": "<PERSON><PERSON>", "k66": "Number", "k67": "String", "k68": "<PERSON><PERSON>", "k69": "String", "k70": "<PERSON><PERSON>", "k71": "String", "k72": "String", "k73": "<PERSON><PERSON>", "k74": "<PERSON><PERSON>", "k75": "<PERSON><PERSON>"}}, {"colName": "collection", "count": 2075, "size": 2021859, "storageSize": 536576, "totalIndexSize": 28672, "indexSizes": {"_id_": 28672}, "avgObjSize": 974, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "8fca6de8-236c-41a4-bbdb-9c1cc347e219"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "<PERSON><PERSON>", "k6": "<PERSON><PERSON>", "k7": "<PERSON><PERSON>", "k8": "<PERSON><PERSON>", "k9": "<PERSON><PERSON>", "k10": "String", "k11": "<PERSON><PERSON>", "k12": "<PERSON><PERSON>", "k13": "<PERSON><PERSON>", "k14": "Number", "k15": "String", "k16": "<PERSON><PERSON>", "k17": "Number", "k18": "String", "k19": "<PERSON><PERSON>", "k20": "String", "k21": "<PERSON><PERSON>", "k22": "<PERSON><PERSON>", "k23": "<PERSON><PERSON>", "k24": "NumberLong", "k25": "<PERSON><PERSON>", "k26": "Number", "k27": "String", "k28": "String", "k29": "<PERSON><PERSON>", "k30": "<PERSON><PERSON>", "k31": "String", "k32": "<PERSON><PERSON>", "k33": "<PERSON><PERSON>", "k34": "<PERSON><PERSON>", "k35": "<PERSON><PERSON>", "k36": "<PERSON><PERSON>", "k37": "<PERSON><PERSON>", "k38": "<PERSON><PERSON>", "k39": "<PERSON><PERSON>", "k40": "String", "k41": "<PERSON><PERSON>", "k42": "<PERSON><PERSON>", "k43": "String", "k44": "<PERSON><PERSON>", "k45": "String", "k46": "String", "k47": "String", "k48": "String", "k49": "String", "k50": "<PERSON><PERSON>", "k51": "Number", "k52": "<PERSON><PERSON>", "k53": "<PERSON><PERSON>", "k54": "<PERSON><PERSON>", "k55": "<PERSON><PERSON>", "k56": "<PERSON><PERSON>", "k57": "<PERSON><PERSON>", "k58": "String", "k59": "<PERSON><PERSON>", "k60": "<PERSON><PERSON>", "k61": "String", "k62": "String", "k63": "<PERSON><PERSON>", "k64": "<PERSON><PERSON>", "k65": "<PERSON><PERSON>"}}, {"colName": "collection", "count": 74, "size": 49989, "storageSize": 20480, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 675, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "8d4cde08-0f1b-4ad0-ba8a-03951107a54b"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "<PERSON><PERSON>", "k3": "String", "k4": "<PERSON><PERSON>", "k5": "<PERSON><PERSON>", "k6": "<PERSON><PERSON>", "k7": "Number", "k8": "<PERSON><PERSON>", "k9": "<PERSON><PERSON>", "k10": "<PERSON><PERSON>", "k11": "<PERSON><PERSON>", "k12": "String", "k13": "<PERSON><PERSON>", "k14": "<PERSON><PERSON>", "k15": "<PERSON><PERSON>", "k16": "<PERSON><PERSON>", "k17": "<PERSON><PERSON>", "k18": "<PERSON><PERSON>", "k19": "<PERSON><PERSON>", "k20": "<PERSON><PERSON>", "k21": "<PERSON><PERSON>", "k22": "Number", "k23": "String", "k24": "<PERSON><PERSON>", "k25": "<PERSON><PERSON>", "k26": "<PERSON><PERSON>", "k27": "<PERSON><PERSON>", "k28": "<PERSON><PERSON>", "k29": "<PERSON><PERSON>", "k30": "String", "k31": "<PERSON><PERSON>", "k32": "String", "k33": "<PERSON><PERSON>", "k34": "<PERSON><PERSON>", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "<PERSON><PERSON>", "k40": "<PERSON><PERSON>", "k41": "<PERSON><PERSON>", "k42": "<PERSON><PERSON>", "k43": "<PERSON><PERSON>", "k44": "String", "k45": "String", "k46": "<PERSON><PERSON>"}}, {"colName": "collection", "count": 0, "size": 0, "storageSize": 4096, "totalIndexSize": 4096, "indexSizes": {"_id_": 4096}, "avgObjSize": 0, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "b9f497c6-0f4c-45f1-8cf4-77e7e8a38fd4"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {}}, {"colName": "collection", "count": 17999, "size": 19085274, "storageSize": 5636096, "totalIndexSize": 172032, "indexSizes": {"_id_": 172032}, "avgObjSize": 1060, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "381ae2ed-3455-48ec-9126-1360852ec701"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "<PERSON><PERSON>", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "<PERSON><PERSON>", "k6": "<PERSON><PERSON>", "k7": "<PERSON><PERSON>", "k8": "<PERSON><PERSON>", "k9": "<PERSON><PERSON>", "k10": "<PERSON><PERSON>", "k11": "<PERSON><PERSON>", "k12": "<PERSON><PERSON>", "k13": "<PERSON><PERSON>", "k14": "<PERSON><PERSON>", "k15": "String", "k16": "String", "k17": "String", "k18": "<PERSON><PERSON>", "k19": "<PERSON><PERSON>", "k20": "Number", "k21": "String", "k22": "<PERSON><PERSON>", "k23": "NumberLong", "k24": "<PERSON><PERSON>", "k25": "<PERSON><PERSON>", "k26": "Number", "k27": "String", "k28": "String", "k29": "String", "k30": "<PERSON><PERSON>", "k31": "<PERSON><PERSON>", "k32": "String", "k33": "<PERSON><PERSON>", "k34": "String", "k35": "<PERSON><PERSON>", "k36": "<PERSON><PERSON>", "k37": "String", "k38": "<PERSON><PERSON>", "k39": "<PERSON><PERSON>", "k40": "<PERSON><PERSON>", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "<PERSON><PERSON>", "k47": "<PERSON><PERSON>", "k48": "<PERSON><PERSON>", "k49": "<PERSON><PERSON>", "k50": "String", "k51": "<PERSON><PERSON>", "k52": "Number", "k53": "<PERSON><PERSON>", "k54": "String", "k55": "<PERSON><PERSON>", "k56": "<PERSON><PERSON>", "k57": "String", "k58": "<PERSON><PERSON>", "k59": "<PERSON><PERSON>", "k60": "<PERSON><PERSON>", "k61": "<PERSON><PERSON>", "k62": "<PERSON><PERSON>", "k63": "<PERSON><PERSON>", "k64": "<PERSON><PERSON>"}}, {"colName": "collection", "count": 17, "size": 10193, "storageSize": 16384, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 599, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "80f85a25-e452-4591-85ba-940928c4165c"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "<PERSON><PERSON>", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "<PERSON><PERSON>", "k6": "<PERSON><PERSON>", "k7": "<PERSON><PERSON>", "k8": "String", "k9": "<PERSON><PERSON>", "k10": "Number", "k11": "<PERSON><PERSON>", "k12": "<PERSON><PERSON>", "k13": "NumberLong", "k14": "<PERSON><PERSON>", "k15": "<PERSON><PERSON>", "k16": "String", "k17": "<PERSON><PERSON>", "k18": "String", "k19": "<PERSON><PERSON>", "k20": "String", "k21": "<PERSON><PERSON>", "k22": "<PERSON><PERSON>", "k23": "<PERSON><PERSON>", "k24": "<PERSON><PERSON>", "k25": "<PERSON><PERSON>", "k26": "<PERSON><PERSON>", "k27": "<PERSON><PERSON>", "k28": "<PERSON><PERSON>", "k29": "<PERSON><PERSON>", "k30": "<PERSON><PERSON>", "k31": "<PERSON><PERSON>", "k32": "String", "k33": "<PERSON><PERSON>", "k34": "Number", "k35": "String", "k36": "<PERSON><PERSON>", "k37": "<PERSON><PERSON>", "k38": "String", "k39": "<PERSON><PERSON>", "k40": "<PERSON><PERSON>", "k41": "Number", "k42": "<PERSON><PERSON>", "k43": "<PERSON><PERSON>", "k44": "<PERSON><PERSON>", "k45": "<PERSON><PERSON>"}}, {"colName": "collection", "count": 179, "size": 127129, "storageSize": 32768, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 710, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "984ae7c1-726a-4624-a140-ed08f913439b"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "<PERSON><PERSON>", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "<PERSON><PERSON>", "k6": "<PERSON><PERSON>", "k7": "String", "k8": "<PERSON><PERSON>", "k9": "Number", "k10": "String", "k11": "<PERSON><PERSON>", "k12": "<PERSON><PERSON>", "k13": "<PERSON><PERSON>", "k14": "NumberLong", "k15": "Number", "k16": "<PERSON><PERSON>", "k17": "String", "k18": "<PERSON><PERSON>", "k19": "<PERSON><PERSON>", "k20": "<PERSON><PERSON>", "k21": "<PERSON><PERSON>", "k22": "<PERSON><PERSON>", "k23": "<PERSON><PERSON>", "k24": "<PERSON><PERSON>", "k25": "<PERSON><PERSON>", "k26": "<PERSON><PERSON>", "k27": "<PERSON><PERSON>", "k28": "<PERSON><PERSON>", "k29": "<PERSON><PERSON>", "k30": "<PERSON><PERSON>", "k31": "<PERSON><PERSON>", "k32": "<PERSON><PERSON>", "k33": "<PERSON><PERSON>", "k34": "<PERSON><PERSON>", "k35": "<PERSON><PERSON>", "k36": "String", "k37": "<PERSON><PERSON>", "k38": "Number", "k39": "<PERSON><PERSON>", "k40": "String", "k41": "<PERSON><PERSON>", "k42": "<PERSON><PERSON>", "k43": "<PERSON><PERSON>", "k44": "<PERSON><PERSON>"}}, {"colName": "collection", "count": 118, "size": 144192, "storageSize": 53248, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 1221, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "115a28b4-fd56-45c0-b08e-60e4a4d368e6"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "<PERSON><PERSON>", "k6": "String", "k7": "<PERSON><PERSON>", "k8": "<PERSON><PERSON>", "k9": "<PERSON><PERSON>", "k10": "<PERSON><PERSON>", "k11": "String", "k12": "<PERSON><PERSON>", "k13": "Number", "k14": "String", "k15": "<PERSON><PERSON>", "k16": "Number", "k17": "String", "k18": "String", "k19": "String", "k20": "<PERSON><PERSON>", "k21": "NumberLong", "k22": "Number", "k23": "<PERSON><PERSON>", "k24": "String", "k25": "<PERSON><PERSON>", "k26": "<PERSON><PERSON>", "k27": "Number", "k28": "<PERSON><PERSON>", "k29": "Number", "k30": "<PERSON><PERSON>", "k31": "<PERSON><PERSON>", "k32": "<PERSON><PERSON>", "k33": "String", "k34": "<PERSON><PERSON>", "k35": "Number", "k36": "String", "k37": "<PERSON><PERSON>", "k38": "Number", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "Number", "k47": "String", "k48": "<PERSON><PERSON>", "k49": "Number", "k50": "String", "k51": "String", "k52": "String", "k53": "Number", "k54": "String", "k55": "String", "k56": "<PERSON><PERSON>", "k57": "<PERSON><PERSON>", "k58": "<PERSON><PERSON>", "k59": "String", "k60": "String"}}, {"colName": "collection", "count": 11, "size": 8731, "storageSize": 16384, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 793, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "4e4157aa-84d8-4872-a755-a2769c2cbb2a"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "<PERSON><PERSON>", "k6": "String", "k7": "<PERSON><PERSON>", "k8": "String", "k9": "<PERSON><PERSON>", "k10": "Number", "k11": "<PERSON><PERSON>", "k12": "NumberLong", "k13": "<PERSON><PERSON>", "k14": "<PERSON><PERSON>", "k15": "String", "k16": "String", "k17": "Number", "k18": "NumberLong", "k19": "<PERSON><PERSON>", "k20": "<PERSON><PERSON>", "k21": "String", "k22": "<PERSON><PERSON>", "k23": "<PERSON><PERSON>", "k24": "String", "k25": "String", "k26": "<PERSON><PERSON>", "k27": "<PERSON><PERSON>", "k28": "<PERSON><PERSON>", "k29": "<PERSON><PERSON>", "k30": "<PERSON><PERSON>", "k31": "<PERSON><PERSON>", "k32": "<PERSON><PERSON>", "k33": "<PERSON><PERSON>", "k34": "<PERSON><PERSON>", "k35": "String", "k36": "<PERSON><PERSON>", "k37": "Number", "k38": "String", "k39": "<PERSON><PERSON>", "k40": "<PERSON><PERSON>", "k41": "<PERSON><PERSON>", "k42": "<PERSON><PERSON>", "k43": "Number"}}, {"colName": "collection", "count": 40, "size": 39384, "storageSize": 20480, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 984, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "98221cec-bd77-4e8e-bc9a-7010e6ef82d5"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "<PERSON><PERSON>", "k6": "<PERSON><PERSON>", "k7": "Number", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "<PERSON><PERSON>", "k13": "Number", "k14": "String", "k15": "String", "k16": "NumberLong", "k17": "<PERSON><PERSON>", "k18": "Number", "k19": "<PERSON><PERSON>", "k20": "String", "k21": "<PERSON><PERSON>", "k22": "<PERSON><PERSON>", "k23": "String", "k24": "String", "k25": "String", "k26": "<PERSON><PERSON>", "k27": "<PERSON><PERSON>", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "<PERSON><PERSON>", "k35": "String", "k36": "String", "k37": "String", "k38": "<PERSON><PERSON>", "k39": "Number", "k40": "String", "k41": "String", "k42": "<PERSON><PERSON>", "k43": "<PERSON><PERSON>", "k44": "String", "k45": "String", "k46": "<PERSON><PERSON>"}}, {"colName": "collection", "count": 8, "size": 9840, "storageSize": 16384, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 1230, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "cc7ea529-989b-4714-bca9-a085bc04ce76"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "<PERSON><PERSON>", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "<PERSON><PERSON>", "k6": "String", "k7": "<PERSON><PERSON>", "k8": "Number", "k9": "<PERSON><PERSON>", "k10": "<PERSON><PERSON>", "k11": "String", "k12": "String", "k13": "<PERSON><PERSON>", "k14": "<PERSON><PERSON>", "k15": "String", "k16": "<PERSON><PERSON>", "k17": "String", "k18": "NumberLong", "k19": "<PERSON><PERSON>", "k20": "<PERSON><PERSON>", "k21": "String", "k22": "<PERSON><PERSON>", "k23": "<PERSON><PERSON>", "k24": "<PERSON><PERSON>", "k25": "<PERSON><PERSON>", "k26": "<PERSON><PERSON>", "k27": "<PERSON><PERSON>", "k28": "<PERSON><PERSON>", "k29": "<PERSON><PERSON>", "k30": "<PERSON><PERSON>", "k31": "<PERSON><PERSON>", "k32": "String", "k33": "<PERSON><PERSON>", "k34": "<PERSON><PERSON>", "k35": "<PERSON><PERSON>", "k36": "<PERSON><PERSON>", "k37": "<PERSON><PERSON>", "k38": "<PERSON><PERSON>", "k39": "String", "k40": "<PERSON><PERSON>", "k41": "Number", "k42": "String", "k43": "String", "k44": "<PERSON><PERSON>", "k45": "<PERSON><PERSON>", "k46": "String", "k47": "String", "k48": "<PERSON><PERSON>", "k49": "<PERSON><PERSON>", "k50": "<PERSON><PERSON>"}}, {"colName": "collection", "count": 10, "size": 5723, "storageSize": 16384, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 572, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "984602fc-a560-4699-9e90-3f402baddf3d"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "<PERSON><PERSON>", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "<PERSON><PERSON>", "k6": "<PERSON><PERSON>", "k7": "<PERSON><PERSON>", "k8": "<PERSON><PERSON>", "k9": "NumberLong", "k10": "Number", "k11": "<PERSON><PERSON>", "k12": "String", "k13": "<PERSON><PERSON>", "k14": "String", "k15": "<PERSON><PERSON>", "k16": "String", "k17": "<PERSON><PERSON>", "k18": "String", "k19": "<PERSON><PERSON>", "k20": "String", "k21": "<PERSON><PERSON>", "k22": "<PERSON><PERSON>", "k23": "<PERSON><PERSON>", "k24": "<PERSON><PERSON>", "k25": "<PERSON><PERSON>", "k26": "<PERSON><PERSON>", "k27": "<PERSON><PERSON>", "k28": "String", "k29": "<PERSON><PERSON>", "k30": "<PERSON><PERSON>", "k31": "<PERSON><PERSON>", "k32": "<PERSON><PERSON>"}}, {"colName": "collection", "count": 13, "size": 19240, "storageSize": 32768, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 1480, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "3d09ab47-d28b-4c41-9301-a35ac793cb9a"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "<PERSON><PERSON>", "k6": "<PERSON><PERSON>", "k7": "<PERSON><PERSON>", "k8": "<PERSON><PERSON>", "k9": "<PERSON><PERSON>", "k10": "String", "k11": "String", "k12": "Number", "k13": "String", "k14": "<PERSON><PERSON>", "k15": "Number", "k16": "<PERSON><PERSON>", "k17": "<PERSON><PERSON>", "k18": "<PERSON><PERSON>", "k19": "<PERSON><PERSON>", "k20": "String", "k21": "String", "k22": "String", "k23": "<PERSON><PERSON>", "k24": "NumberLong", "k25": "Number", "k26": "<PERSON><PERSON>", "k27": "<PERSON><PERSON>", "k28": "String", "k29": "<PERSON><PERSON>", "k30": "<PERSON><PERSON>", "k31": "<PERSON><PERSON>", "k32": "String", "k33": "<PERSON><PERSON>", "k34": "String", "k35": "String", "k36": "<PERSON><PERSON>", "k37": "<PERSON><PERSON>", "k38": "<PERSON><PERSON>", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "<PERSON><PERSON>", "k46": "<PERSON><PERSON>", "k47": "String", "k48": "<PERSON><PERSON>", "k49": "String", "k50": "<PERSON><PERSON>", "k51": "String", "k52": "String", "k53": "String", "k54": "<PERSON><PERSON>", "k55": "String", "k56": "String", "k57": "String", "k58": "String", "k59": "<PERSON><PERSON>", "k60": "String", "k61": "String", "k62": "String", "k63": "<PERSON><PERSON>", "k64": "Number", "k65": "String", "k66": "<PERSON><PERSON>", "k67": "String", "k68": "<PERSON><PERSON>", "k69": "String", "k70": "String", "k71": "<PERSON><PERSON>", "k72": "<PERSON><PERSON>", "k73": "String"}}, {"colName": "collection", "count": 93, "size": 114565, "storageSize": 32768, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 1231, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "4b193722-767e-4ae2-8503-bb5e5748ba44"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "<PERSON><PERSON>", "k6": "<PERSON><PERSON>", "k7": "<PERSON><PERSON>", "k8": "<PERSON><PERSON>", "k9": "<PERSON><PERSON>", "k10": "<PERSON><PERSON>", "k11": "<PERSON><PERSON>", "k12": "Number", "k13": "String", "k14": "String", "k15": "<PERSON><PERSON>", "k16": "<PERSON><PERSON>", "k17": "<PERSON><PERSON>", "k18": "String", "k19": "<PERSON><PERSON>", "k20": "Number", "k21": "String", "k22": "String", "k23": "String", "k24": "NumberLong", "k25": "<PERSON><PERSON>", "k26": "<PERSON><PERSON>", "k27": "Number", "k28": "String", "k29": "String", "k30": "String", "k31": "<PERSON><PERSON>", "k32": "<PERSON><PERSON>", "k33": "<PERSON><PERSON>", "k34": "String", "k35": "String", "k36": "<PERSON><PERSON>", "k37": "String", "k38": "<PERSON><PERSON>", "k39": "<PERSON><PERSON>", "k40": "<PERSON><PERSON>", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "String", "k48": "String", "k49": "String", "k50": "<PERSON><PERSON>", "k51": "Number", "k52": "String", "k53": "<PERSON><PERSON>", "k54": "<PERSON><PERSON>", "k55": "<PERSON><PERSON>", "k56": "<PERSON><PERSON>", "k57": "<PERSON><PERSON>", "k58": "String", "k59": "<PERSON><PERSON>", "k60": "String", "k61": "String", "k62": "<PERSON><PERSON>", "k63": "<PERSON><PERSON>", "k64": "<PERSON><PERSON>"}}, {"colName": "collection", "count": 4, "size": 3963, "storageSize": 16384, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 990, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "3a608cdb-73f3-4fc6-8bd3-1c4786a86184"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "<PERSON><PERSON>", "k6": "Number", "k7": "String", "k8": "<PERSON><PERSON>", "k9": "Number", "k10": "String", "k11": "<PERSON><PERSON>", "k12": "<PERSON><PERSON>", "k13": "NumberLong", "k14": "String", "k15": "String", "k16": "String", "k17": "Number", "k18": "<PERSON><PERSON>", "k19": "NumberLong", "k20": "Number", "k21": "<PERSON><PERSON>", "k22": "String", "k23": "<PERSON><PERSON>", "k24": "String", "k25": "String", "k26": "<PERSON><PERSON>", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "<PERSON><PERSON>", "k38": "Number", "k39": "String", "k40": "<PERSON><PERSON>", "k41": "<PERSON><PERSON>", "k42": "<PERSON><PERSON>", "k43": "String", "k44": "String", "k45": "String"}}, {"colName": "collection", "count": 603, "size": 359739, "storageSize": 65536, "totalIndexSize": 20480, "indexSizes": {"_id_": 20480}, "avgObjSize": 596, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "fbda9b3d-a2f5-4e0a-8cdd-48661e9baafe"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "<PERSON><PERSON>", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "<PERSON><PERSON>", "k6": "String", "k7": "<PERSON><PERSON>", "k8": "String", "k9": "<PERSON><PERSON>", "k10": "Number", "k11": "<PERSON><PERSON>", "k12": "<PERSON><PERSON>", "k13": "<PERSON><PERSON>", "k14": "NumberLong", "k15": "<PERSON><PERSON>", "k16": "<PERSON><PERSON>", "k17": "String", "k18": "<PERSON><PERSON>", "k19": "<PERSON><PERSON>", "k20": "<PERSON><PERSON>", "k21": "String", "k22": "<PERSON><PERSON>", "k23": "<PERSON><PERSON>", "k24": "<PERSON><PERSON>", "k25": "<PERSON><PERSON>", "k26": "<PERSON><PERSON>", "k27": "<PERSON><PERSON>", "k28": "<PERSON><PERSON>", "k29": "<PERSON><PERSON>", "k30": "<PERSON><PERSON>", "k31": "<PERSON><PERSON>", "k32": "<PERSON><PERSON>", "k33": "<PERSON><PERSON>", "k34": "<PERSON><PERSON>", "k35": "<PERSON><PERSON>", "k36": "<PERSON><PERSON>", "k37": "String", "k38": "<PERSON><PERSON>", "k39": "Number", "k40": "String", "k41": "<PERSON><PERSON>", "k42": "<PERSON><PERSON>", "k43": "<PERSON><PERSON>", "k44": "<PERSON><PERSON>"}}, {"colName": "collection", "count": 4000, "size": 3477645, "storageSize": 557056, "totalIndexSize": 49152, "indexSizes": {"_id_": 49152}, "avgObjSize": 869, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "f1bac40e-fb8e-4e5c-be0c-184b265db87d"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "Number", "k6": "<PERSON><PERSON>", "k7": "Number", "k8": "String", "k9": "<PERSON><PERSON>", "k10": "Number", "k11": "<PERSON><PERSON>", "k12": "String", "k13": "<PERSON><PERSON>", "k14": "NumberLong", "k15": "Number", "k16": "String", "k17": "String", "k18": "<PERSON><PERSON>", "k19": "<PERSON><PERSON>", "k20": "String", "k21": "<PERSON><PERSON>", "k22": "String", "k23": "String", "k24": "String", "k25": "<PERSON><PERSON>", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "<PERSON><PERSON>", "k32": "Number", "k33": "NumberLong", "k34": "String", "k35": "<PERSON><PERSON>", "k36": "<PERSON><PERSON>", "k37": "Number", "k38": "String", "k39": "String"}}, {"colName": "collection", "count": 859, "size": 511105, "storageSize": 90112, "totalIndexSize": 20480, "indexSizes": {"_id_": 20480}, "avgObjSize": 595, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "2bad2e3e-e54f-461a-b3e0-3a5df2ca74fa"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "<PERSON><PERSON>", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "<PERSON><PERSON>", "k6": "String", "k7": "<PERSON><PERSON>", "k8": "String", "k9": "Number", "k10": "<PERSON><PERSON>", "k11": "String", "k12": "String", "k13": "<PERSON><PERSON>", "k14": "NumberLong", "k15": "<PERSON><PERSON>", "k16": "<PERSON><PERSON>", "k17": "String", "k18": "<PERSON><PERSON>", "k19": "<PERSON><PERSON>", "k20": "<PERSON><PERSON>", "k21": "String", "k22": "<PERSON><PERSON>", "k23": "<PERSON><PERSON>", "k24": "<PERSON><PERSON>", "k25": "<PERSON><PERSON>", "k26": "<PERSON><PERSON>", "k27": "<PERSON><PERSON>", "k28": "<PERSON><PERSON>", "k29": "<PERSON><PERSON>", "k30": "String", "k31": "<PERSON><PERSON>", "k32": "String", "k33": "Number", "k34": "String", "k35": "<PERSON><PERSON>", "k36": "<PERSON><PERSON>", "k37": "<PERSON><PERSON>", "k38": "<PERSON><PERSON>"}}, {"colName": "collection", "count": 836, "size": 692091, "storageSize": 114688, "totalIndexSize": 20480, "indexSizes": {"_id_": 20480}, "avgObjSize": 827, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "7f09d432-c505-41cf-99b5-04e3733d57be"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "<PERSON><PERSON>", "k6": "<PERSON><PERSON>", "k7": "<PERSON><PERSON>", "k8": "<PERSON><PERSON>", "k9": "<PERSON><PERSON>", "k10": "<PERSON><PERSON>", "k11": "<PERSON><PERSON>", "k12": "Number", "k13": "String", "k14": "String", "k15": "String", "k16": "<PERSON><PERSON>", "k17": "Number", "k18": "String", "k19": "String", "k20": "NumberLong", "k21": "<PERSON><PERSON>", "k22": "<PERSON><PERSON>", "k23": "Number", "k24": "String", "k25": "String", "k26": "String", "k27": "<PERSON><PERSON>", "k28": "<PERSON><PERSON>", "k29": "<PERSON><PERSON>", "k30": "String", "k31": "String", "k32": "<PERSON><PERSON>", "k33": "String", "k34": "<PERSON><PERSON>", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "<PERSON><PERSON>", "k47": "Number", "k48": "String", "k49": "<PERSON><PERSON>", "k50": "<PERSON><PERSON>", "k51": "String", "k52": "<PERSON><PERSON>", "k53": "String", "k54": "String", "k55": "<PERSON><PERSON>", "k56": "<PERSON><PERSON>", "k57": "<PERSON><PERSON>"}}, {"colName": "collection", "count": 352, "size": 313080, "storageSize": 57344, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 889, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "77834e41-b1b8-4bdf-9fd6-531c75ab0ae9"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "<PERSON><PERSON>", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "<PERSON><PERSON>", "k6": "<PERSON><PERSON>", "k7": "<PERSON><PERSON>", "k8": "String", "k9": "<PERSON><PERSON>", "k10": "Number", "k11": "<PERSON><PERSON>", "k12": "<PERSON><PERSON>", "k13": "NumberLong", "k14": "<PERSON><PERSON>", "k15": "<PERSON><PERSON>", "k16": "<PERSON><PERSON>", "k17": "<PERSON><PERSON>", "k18": "<PERSON><PERSON>", "k19": "<PERSON><PERSON>", "k20": "<PERSON><PERSON>", "k21": "<PERSON><PERSON>", "k22": "String", "k23": "<PERSON><PERSON>", "k24": "<PERSON><PERSON>", "k25": "String", "k26": "<PERSON><PERSON>", "k27": "<PERSON><PERSON>", "k28": "<PERSON><PERSON>", "k29": "<PERSON><PERSON>", "k30": "<PERSON><PERSON>", "k31": "<PERSON><PERSON>", "k32": "String", "k33": "<PERSON><PERSON>", "k34": "<PERSON><PERSON>", "k35": "<PERSON><PERSON>", "k36": "<PERSON><PERSON>", "k37": "<PERSON><PERSON>", "k38": "<PERSON><PERSON>", "k39": "String", "k40": "<PERSON><PERSON>", "k41": "Number", "k42": "String", "k43": "<PERSON><PERSON>", "k44": "<PERSON><PERSON>", "k45": "<PERSON><PERSON>", "k46": "<PERSON><PERSON>", "k47": "<PERSON><PERSON>", "k48": "<PERSON><PERSON>", "k49": "<PERSON><PERSON>", "k50": "<PERSON><PERSON>", "k51": "<PERSON><PERSON>", "k52": "<PERSON><PERSON>", "k53": "<PERSON><PERSON>", "k54": "<PERSON><PERSON>", "k55": "<PERSON><PERSON>", "k56": "<PERSON><PERSON>", "k57": "<PERSON><PERSON>", "k58": "<PERSON><PERSON>", "k59": "<PERSON><PERSON>", "k60": "<PERSON><PERSON>", "k61": "<PERSON><PERSON>", "k62": "<PERSON><PERSON>", "k63": "<PERSON><PERSON>", "k64": "<PERSON><PERSON>", "k65": "<PERSON><PERSON>", "k66": "<PERSON><PERSON>", "k67": "<PERSON><PERSON>", "k68": "<PERSON><PERSON>", "k69": "String", "k70": "<PERSON><PERSON>"}}, {"colName": "collection", "count": 1966, "size": 3571040, "storageSize": 1196032, "totalIndexSize": 28672, "indexSizes": {"_id_": 28672}, "avgObjSize": 1816, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "1c478d97-7a25-471a-b00b-6a4a758c900e"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "<PERSON><PERSON>", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "<PERSON><PERSON>", "k6": "<PERSON><PERSON>", "k7": "<PERSON><PERSON>", "k8": "String", "k9": "<PERSON><PERSON>", "k10": "<PERSON><PERSON>", "k11": "<PERSON><PERSON>", "k12": "<PERSON><PERSON>", "k13": "<PERSON><PERSON>", "k14": "String", "k15": "String", "k16": "<PERSON><PERSON>", "k17": "<PERSON><PERSON>", "k18": "<PERSON><PERSON>", "k19": "<PERSON><PERSON>", "k20": "<PERSON><PERSON>", "k21": "<PERSON><PERSON>", "k22": "String", "k23": "<PERSON><PERSON>", "k24": "<PERSON><PERSON>", "k25": "<PERSON><PERSON>", "k26": "String", "k27": "String", "k28": "<PERSON><PERSON>", "k29": "<PERSON><PERSON>", "k30": "<PERSON><PERSON>", "k31": "<PERSON><PERSON>", "k32": "<PERSON><PERSON>", "k33": "<PERSON><PERSON>", "k34": "<PERSON><PERSON>", "k35": "<PERSON><PERSON>", "k36": "<PERSON><PERSON>", "k37": "<PERSON><PERSON>", "k38": "String", "k39": "<PERSON><PERSON>", "k40": "<PERSON><PERSON>", "k41": "String", "k42": "<PERSON><PERSON>", "k43": "Number", "k44": "<PERSON><PERSON>", "k45": "<PERSON><PERSON>", "k46": "<PERSON><PERSON>", "k47": "<PERSON><PERSON>", "k48": "String", "k49": "NumberLong", "k50": "<PERSON><PERSON>", "k51": "<PERSON><PERSON>", "k52": "String", "k53": "<PERSON><PERSON>", "k54": "<PERSON><PERSON>", "k55": "<PERSON><PERSON>", "k56": "<PERSON><PERSON>", "k57": "<PERSON><PERSON>", "k58": "<PERSON><PERSON>", "k59": "<PERSON><PERSON>", "k60": "<PERSON><PERSON>", "k61": "<PERSON><PERSON>", "k62": "<PERSON><PERSON>", "k63": "<PERSON><PERSON>", "k64": "<PERSON><PERSON>", "k65": "<PERSON><PERSON>", "k66": "<PERSON><PERSON>", "k67": "<PERSON><PERSON>", "k68": "<PERSON><PERSON>", "k69": "<PERSON><PERSON>", "k70": "<PERSON><PERSON>", "k71": "<PERSON><PERSON>", "k72": "<PERSON><PERSON>", "k73": "<PERSON><PERSON>", "k74": "<PERSON><PERSON>", "k75": "<PERSON><PERSON>", "k76": "<PERSON><PERSON>", "k77": "<PERSON><PERSON>", "k78": "<PERSON><PERSON>", "k79": "<PERSON><PERSON>", "k80": "<PERSON><PERSON>", "k81": "<PERSON><PERSON>", "k82": "<PERSON><PERSON>", "k83": "<PERSON><PERSON>", "k84": "<PERSON><PERSON>", "k85": "<PERSON><PERSON>", "k86": "<PERSON><PERSON>", "k87": "<PERSON><PERSON>", "k88": "<PERSON><PERSON>", "k89": "<PERSON><PERSON>", "k90": "<PERSON><PERSON>", "k91": "String", "k92": "<PERSON><PERSON>", "k93": "Number", "k94": "<PERSON><PERSON>", "k95": "String", "k96": "<PERSON><PERSON>", "k97": "<PERSON><PERSON>", "k98": "<PERSON><PERSON>", "k99": "<PERSON><PERSON>", "k100": "<PERSON><PERSON>", "k101": "<PERSON><PERSON>", "k102": "<PERSON><PERSON>", "k103": "<PERSON><PERSON>", "k104": "String", "k105": "<PERSON><PERSON>", "k106": "String", "k107": "<PERSON><PERSON>"}}, {"colName": "collection", "count": 2871, "size": 2328029, "storageSize": 430080, "totalIndexSize": 36864, "indexSizes": {"_id_": 36864}, "avgObjSize": 810, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "b36447a3-b2e5-479b-83b7-be182e489408"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "String", "k4": "<PERSON><PERSON>", "k5": "<PERSON><PERSON>", "k6": "Number", "k7": "<PERSON><PERSON>", "k8": "String", "k9": "<PERSON><PERSON>", "k10": "<PERSON><PERSON>", "k11": "String", "k12": "String", "k13": "Number", "k14": "NumberLong", "k15": "Number", "k16": "String", "k17": "<PERSON><PERSON>", "k18": "String", "k19": "<PERSON><PERSON>", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "<PERSON><PERSON>", "k29": "<PERSON><PERSON>", "k30": "<PERSON><PERSON>", "k31": "String", "k32": "String", "k33": "String"}}, {"colName": "collection", "count": 284, "size": 239714, "storageSize": 49152, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 844, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "8d77cea2-b700-402a-b138-2c7a8422ae5a"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "Number", "k6": "String", "k7": "<PERSON><PERSON>", "k8": "Number", "k9": "String", "k10": "String", "k11": "NumberLong", "k12": "Number", "k13": "String", "k14": "String", "k15": "<PERSON><PERSON>", "k16": "String", "k17": "String", "k18": "<PERSON><PERSON>", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "<PERSON><PERSON>", "k29": "Number", "k30": "String", "k31": "<PERSON><PERSON>", "k32": "<PERSON><PERSON>", "k33": "String", "k34": "String"}}, {"colName": "collection", "count": 315, "size": 247554, "storageSize": 49152, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 785, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "25b65111-a8cc-4468-97f4-cffa4971f029"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "<PERSON><PERSON>", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "<PERSON><PERSON>", "k6": "String", "k7": "<PERSON><PERSON>", "k8": "Number", "k9": "String", "k10": "Number", "k11": "<PERSON><PERSON>", "k12": "NumberLong", "k13": "Number", "k14": "String", "k15": "String", "k16": "<PERSON><PERSON>", "k17": "<PERSON><PERSON>", "k18": "<PERSON><PERSON>", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "<PERSON><PERSON>", "k24": "<PERSON><PERSON>", "k25": "<PERSON><PERSON>", "k26": "<PERSON><PERSON>", "k27": "String", "k28": "<PERSON><PERSON>", "k29": "Number", "k30": "String", "k31": "<PERSON><PERSON>", "k32": "<PERSON><PERSON>", "k33": "<PERSON><PERSON>", "k34": "<PERSON><PERSON>"}}, {"colName": "collection", "count": 19, "size": 32474, "storageSize": 20480, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 1709, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "8be38b55-d3a1-469e-a45a-aa87a7ee7f72"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "<PERSON><PERSON>", "k6": "<PERSON><PERSON>", "k7": "<PERSON><PERSON>", "k8": "<PERSON><PERSON>", "k9": "<PERSON><PERSON>", "k10": "<PERSON><PERSON>", "k11": "<PERSON><PERSON>", "k12": "<PERSON><PERSON>", "k13": "<PERSON><PERSON>", "k14": "<PERSON><PERSON>", "k15": "<PERSON><PERSON>", "k16": "<PERSON><PERSON>", "k17": "<PERSON><PERSON>", "k18": "<PERSON><PERSON>", "k19": "<PERSON><PERSON>", "k20": "Number", "k21": "String", "k22": "<PERSON><PERSON>", "k23": "Number", "k24": "<PERSON><PERSON>", "k25": "<PERSON><PERSON>", "k26": "<PERSON><PERSON>", "k27": "<PERSON><PERSON>", "k28": "<PERSON><PERSON>", "k29": "String", "k30": "String", "k31": "String", "k32": "NumberLong", "k33": "Number", "k34": "String", "k35": "String", "k36": "<PERSON><PERSON>", "k37": "<PERSON><PERSON>", "k38": "<PERSON><PERSON>", "k39": "String", "k40": "<PERSON><PERSON>", "k41": "<PERSON><PERSON>", "k42": "<PERSON><PERSON>", "k43": "<PERSON><PERSON>", "k44": "<PERSON><PERSON>", "k45": "<PERSON><PERSON>", "k46": "<PERSON><PERSON>", "k47": "<PERSON><PERSON>", "k48": "<PERSON><PERSON>", "k49": "<PERSON><PERSON>", "k50": "<PERSON><PERSON>", "k51": "<PERSON><PERSON>", "k52": "<PERSON><PERSON>", "k53": "<PERSON><PERSON>", "k54": "<PERSON><PERSON>", "k55": "<PERSON><PERSON>", "k56": "<PERSON><PERSON>", "k57": "String", "k58": "<PERSON><PERSON>", "k59": "String", "k60": "String", "k61": "String", "k62": "<PERSON><PERSON>", "k63": "<PERSON><PERSON>", "k64": "<PERSON><PERSON>", "k65": "<PERSON><PERSON>", "k66": "<PERSON><PERSON>", "k67": "String", "k68": "String", "k69": "<PERSON><PERSON>", "k70": "String", "k71": "String", "k72": "<PERSON><PERSON>", "k73": "<PERSON><PERSON>", "k74": "String", "k75": "<PERSON><PERSON>", "k76": "Number", "k77": "<PERSON><PERSON>", "k78": "String", "k79": "<PERSON><PERSON>", "k80": "<PERSON><PERSON>", "k81": "<PERSON><PERSON>", "k82": "<PERSON><PERSON>", "k83": "String", "k84": "String", "k85": "<PERSON><PERSON>", "k86": "<PERSON><PERSON>", "k87": "<PERSON><PERSON>"}}, {"colName": "collection", "count": 1428, "size": 1516600, "storageSize": 241664, "totalIndexSize": 24576, "indexSizes": {"_id_": 24576}, "avgObjSize": 1062, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "0153495e-f98d-47a5-8404-47374e2a3355"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "<PERSON><PERSON>", "k6": "<PERSON><PERSON>", "k7": "<PERSON><PERSON>", "k8": "<PERSON><PERSON>", "k9": "<PERSON><PERSON>", "k10": "<PERSON><PERSON>", "k11": "<PERSON><PERSON>", "k12": "Number", "k13": "<PERSON><PERSON>", "k14": "String", "k15": "String", "k16": "<PERSON><PERSON>", "k17": "Number", "k18": "<PERSON><PERSON>", "k19": "<PERSON><PERSON>", "k20": "String", "k21": "String", "k22": "String", "k23": "<PERSON><PERSON>", "k24": "NumberLong", "k25": "<PERSON><PERSON>", "k26": "<PERSON><PERSON>", "k27": "Number", "k28": "String", "k29": "<PERSON><PERSON>", "k30": "String", "k31": "<PERSON><PERSON>", "k32": "<PERSON><PERSON>", "k33": "<PERSON><PERSON>", "k34": "<PERSON><PERSON>", "k35": "String", "k36": "<PERSON><PERSON>", "k37": "<PERSON><PERSON>", "k38": "<PERSON><PERSON>", "k39": "<PERSON><PERSON>", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "String", "k48": "String", "k49": "<PERSON><PERSON>", "k50": "Number", "k51": "String", "k52": "<PERSON><PERSON>", "k53": "<PERSON><PERSON>", "k54": "<PERSON><PERSON>", "k55": "<PERSON><PERSON>", "k56": "String", "k57": "String", "k58": "<PERSON><PERSON>", "k59": "<PERSON><PERSON>", "k60": "<PERSON><PERSON>"}}, {"colName": "collection", "count": 28, "size": 29228, "storageSize": 16384, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 1043, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "d8ce2f87-2947-411f-a63e-c5e8e1844c67"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "<PERSON><PERSON>", "k6": "<PERSON><PERSON>", "k7": "<PERSON><PERSON>", "k8": "<PERSON><PERSON>", "k9": "<PERSON><PERSON>", "k10": "String", "k11": "<PERSON><PERSON>", "k12": "<PERSON><PERSON>", "k13": "Number", "k14": "<PERSON><PERSON>", "k15": "<PERSON><PERSON>", "k16": "String", "k17": "<PERSON><PERSON>", "k18": "Number", "k19": "String", "k20": "String", "k21": "NumberLong", "k22": "<PERSON><PERSON>", "k23": "<PERSON><PERSON>", "k24": "Number", "k25": "String", "k26": "<PERSON><PERSON>", "k27": "String", "k28": "<PERSON><PERSON>", "k29": "<PERSON><PERSON>", "k30": "<PERSON><PERSON>", "k31": "<PERSON><PERSON>", "k32": "<PERSON><PERSON>", "k33": "String", "k34": "<PERSON><PERSON>", "k35": "<PERSON><PERSON>", "k36": "<PERSON><PERSON>", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "<PERSON><PERSON>", "k46": "String", "k47": "<PERSON><PERSON>", "k48": "Number", "k49": "String", "k50": "<PERSON><PERSON>", "k51": "<PERSON><PERSON>", "k52": "<PERSON><PERSON>", "k53": "<PERSON><PERSON>", "k54": "String", "k55": "String", "k56": "<PERSON><PERSON>", "k57": "<PERSON><PERSON>", "k58": "<PERSON><PERSON>", "k59": "<PERSON><PERSON>"}}, {"colName": "collection", "count": 18, "size": 22277, "storageSize": 16384, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 1237, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "ff958141-d659-432b-b7a0-8039e0c95b63"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "String", "k4": "<PERSON><PERSON>", "k5": "<PERSON><PERSON>", "k6": "String", "k7": "Number", "k8": "String", "k9": "Number", "k10": "<PERSON><PERSON>", "k11": "<PERSON><PERSON>", "k12": "<PERSON><PERSON>", "k13": "String", "k14": "<PERSON><PERSON>", "k15": "Number", "k16": "String", "k17": "<PERSON><PERSON>", "k18": "Number", "k19": "String", "k20": "String", "k21": "NumberLong", "k22": "Number", "k23": "<PERSON><PERSON>", "k24": "String", "k25": "String", "k26": "<PERSON><PERSON>", "k27": "String", "k28": "<PERSON><PERSON>", "k29": "String", "k30": "Number", "k31": "Number", "k32": "<PERSON><PERSON>", "k33": "String", "k34": "<PERSON><PERSON>", "k35": "<PERSON><PERSON>", "k36": "<PERSON><PERSON>", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "<PERSON><PERSON>", "k46": "Number", "k47": "String", "k48": "<PERSON><PERSON>", "k49": "Number", "k50": "String", "k51": "String", "k52": "<PERSON><PERSON>", "k53": "String", "k54": "String", "k55": "String", "k56": "<PERSON><PERSON>", "k57": "<PERSON><PERSON>", "k58": "<PERSON><PERSON>", "k59": "<PERSON><PERSON>", "k60": "<PERSON><PERSON>", "k61": "String", "k62": "String"}}, {"colName": "collection", "count": 2909, "size": 2617123, "storageSize": 425984, "totalIndexSize": 36864, "indexSizes": {"_id_": 36864}, "avgObjSize": 899, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "91354f85-c4a8-4136-9ddb-c80fc9967528"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "<PERSON><PERSON>", "k6": "Number", "k7": "String", "k8": "<PERSON><PERSON>", "k9": "Number", "k10": "String", "k11": "<PERSON><PERSON>", "k12": "<PERSON><PERSON>", "k13": "String", "k14": "Number", "k15": "<PERSON><PERSON>", "k16": "NumberLong", "k17": "Number", "k18": "String", "k19": "String", "k20": "<PERSON><PERSON>", "k21": "<PERSON><PERSON>", "k22": "<PERSON><PERSON>", "k23": "String", "k24": "String", "k25": "<PERSON><PERSON>", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "<PERSON><PERSON>", "k36": "Number", "k37": "String", "k38": "<PERSON><PERSON>", "k39": "<PERSON><PERSON>", "k40": "String", "k41": "String", "k42": "String"}}, {"colName": "collection", "count": 3, "size": 2532, "storageSize": 16384, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 844, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "1f968ebc-b3f4-46bd-9299-f9f354c376f5"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "<PERSON><PERSON>", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "<PERSON><PERSON>", "k6": "String", "k7": "<PERSON><PERSON>", "k8": "Number", "k9": "String", "k10": "String", "k11": "<PERSON><PERSON>", "k12": "Number", "k13": "NumberLong", "k14": "Number", "k15": "String", "k16": "String", "k17": "<PERSON><PERSON>", "k18": "<PERSON><PERSON>", "k19": "String", "k20": "<PERSON><PERSON>", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "<PERSON><PERSON>", "k26": "<PERSON><PERSON>", "k27": "<PERSON><PERSON>", "k28": "<PERSON><PERSON>", "k29": "String", "k30": "<PERSON><PERSON>", "k31": "Number", "k32": "String", "k33": "<PERSON><PERSON>", "k34": "<PERSON><PERSON>", "k35": "<PERSON><PERSON>", "k36": "<PERSON><PERSON>"}}, {"colName": "collection", "count": 12, "size": 13578, "storageSize": 16384, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 1131, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "47b14140-90da-438c-85be-7c7c0bbec313"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "<PERSON><PERSON>", "k6": "<PERSON><PERSON>", "k7": "<PERSON><PERSON>", "k8": "<PERSON><PERSON>", "k9": "<PERSON><PERSON>", "k10": "<PERSON><PERSON>", "k11": "<PERSON><PERSON>", "k12": "<PERSON><PERSON>", "k13": "Number", "k14": "<PERSON><PERSON>", "k15": "<PERSON><PERSON>", "k16": "String", "k17": "<PERSON><PERSON>", "k18": "Number", "k19": "String", "k20": "String", "k21": "NumberLong", "k22": "<PERSON><PERSON>", "k23": "<PERSON><PERSON>", "k24": "Number", "k25": "String", "k26": "<PERSON><PERSON>", "k27": "String", "k28": "<PERSON><PERSON>", "k29": "<PERSON><PERSON>", "k30": "<PERSON><PERSON>", "k31": "<PERSON><PERSON>", "k32": "String", "k33": "<PERSON><PERSON>", "k34": "<PERSON><PERSON>", "k35": "<PERSON><PERSON>", "k36": "<PERSON><PERSON>", "k37": "String", "k38": "String", "k39": "String", "k40": "<PERSON><PERSON>", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "<PERSON><PERSON>", "k48": "Number", "k49": "String", "k50": "<PERSON><PERSON>", "k51": "String", "k52": "<PERSON><PERSON>", "k53": "<PERSON><PERSON>", "k54": "<PERSON><PERSON>", "k55": "<PERSON><PERSON>", "k56": "String", "k57": "String", "k58": "String", "k59": "String", "k60": "<PERSON><PERSON>", "k61": "<PERSON><PERSON>", "k62": "<PERSON><PERSON>"}}, {"colName": "collection", "count": 7000, "size": 7734034, "storageSize": 1814528, "totalIndexSize": 73728, "indexSizes": {"_id_": 73728}, "avgObjSize": 1104, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "f2d7359f-ab2e-4f03-beac-2b324537ea61"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "<PERSON><PERSON>", "k6": "Number", "k7": "String", "k8": "<PERSON><PERSON>", "k9": "Number", "k10": "<PERSON><PERSON>", "k11": "String", "k12": "<PERSON><PERSON>", "k13": "<PERSON><PERSON>", "k14": "<PERSON><PERSON>", "k15": "<PERSON><PERSON>", "k16": "<PERSON><PERSON>", "k17": "<PERSON><PERSON>", "k18": "NumberLong", "k19": "Number", "k20": "String", "k21": "String", "k22": "<PERSON><PERSON>", "k23": "<PERSON><PERSON>", "k24": "<PERSON><PERSON>", "k25": "<PERSON><PERSON>", "k26": "<PERSON><PERSON>", "k27": "<PERSON><PERSON>", "k28": "<PERSON><PERSON>", "k29": "<PERSON><PERSON>", "k30": "<PERSON><PERSON>", "k31": "<PERSON><PERSON>", "k32": "<PERSON><PERSON>", "k33": "String", "k34": "String", "k35": "String", "k36": "<PERSON><PERSON>", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "<PERSON><PERSON>", "k43": "Number", "k44": "String", "k45": "<PERSON><PERSON>", "k46": "<PERSON><PERSON>", "k47": "<PERSON><PERSON>", "k48": "String", "k49": "String", "k50": "String", "k51": "String", "k52": "Number", "k53": "String", "k54": "String", "k55": "<PERSON><PERSON>"}}, {"colName": "collection", "count": 160, "size": 183752, "storageSize": 40960, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 1148, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "b017e776-2246-42cb-805e-ecb48646c6ed"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "<PERSON><PERSON>", "k4": "<PERSON><PERSON>", "k5": "<PERSON><PERSON>", "k6": "<PERSON><PERSON>", "k7": "<PERSON><PERSON>", "k8": "<PERSON><PERSON>", "k9": "<PERSON><PERSON>", "k10": "<PERSON><PERSON>", "k11": "<PERSON><PERSON>", "k12": "Number", "k13": "String", "k14": "<PERSON><PERSON>", "k15": "String", "k16": "String", "k17": "Number", "k18": "String", "k19": "String", "k20": "NumberLong", "k21": "<PERSON><PERSON>", "k22": "String", "k23": "Number", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "<PERSON><PERSON>", "k29": "<PERSON><PERSON>", "k30": "<PERSON><PERSON>", "k31": "String", "k32": "String", "k33": "<PERSON><PERSON>", "k34": "String", "k35": "<PERSON><PERSON>", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "String", "k48": "Number", "k49": "String", "k50": "<PERSON><PERSON>", "k51": "String", "k52": "String", "k53": "Number", "k54": "String", "k55": "String", "k56": "<PERSON><PERSON>", "k57": "<PERSON><PERSON>", "k58": "<PERSON><PERSON>", "k59": "<PERSON><PERSON>"}}, {"colName": "collection", "count": 9177, "size": 12170095, "storageSize": 1814528, "totalIndexSize": 94208, "indexSizes": {"_id_": 94208}, "avgObjSize": 1326, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "a0ddcbea-d492-4e05-8953-7ae80835801c"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "String", "k4": "String", "k5": "String", "k6": "String", "k7": "String", "k8": "String", "k9": "String", "k10": "String", "k11": "String", "k12": "String", "k13": "String", "k14": "String", "k15": "String", "k16": "String", "k17": "String", "k18": "String", "k19": "String", "k20": "String", "k21": "String", "k22": "String", "k23": "String", "k24": "String", "k25": "String", "k26": "String", "k27": "String", "k28": "String", "k29": "String", "k30": "String", "k31": "String", "k32": "String", "k33": "String", "k34": "String", "k35": "String", "k36": "String", "k37": "String", "k38": "String", "k39": "String", "k40": "String", "k41": "String", "k42": "String", "k43": "String", "k44": "String", "k45": "String", "k46": "String", "k47": "String", "k48": "String", "k49": "String", "k50": "String", "k51": "String", "k52": "String", "k53": "String", "k54": "String", "k55": "String", "k56": "String", "k57": "String", "k58": "String", "k59": "String", "k60": "String", "k61": "String", "k62": "String", "k63": "String", "k64": "String"}}, {"colName": "collection", "count": 3, "size": 194, "storageSize": 16384, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 64, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "26ec88f8-97ca-4bcc-bb4a-635b4127735c"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String"}}, {"colName": "collection", "count": 1, "size": 85, "storageSize": 16384, "totalIndexSize": 16384, "indexSizes": {"_id_": 16384}, "avgObjSize": 85, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "37e937f7-b76f-470b-a82b-ed05e042b6d2"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "Number", "k3": "Number", "k4": "String"}}, {"colName": "collection", "count": 11492, "size": 643552, "storageSize": 221184, "totalIndexSize": 114688, "indexSizes": {"_id_": 114688}, "avgObjSize": 56, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "bb1490b8-8e08-4070-b4e2-5033ec8f2269"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "<PERSON><PERSON>", "k2": "BSON"}}, {"colName": "collection", "count": 3, "size": 342, "storageSize": 36864, "totalIndexSize": 36864, "indexSizes": {"_id_": 36864}, "avgObjSize": 114, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "0967bb73-d4b9-43d5-b5d6-81e6ff656282"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "String", "k2": "String", "k3": "BSON"}}, {"colName": "collection", "count": 4, "size": 447, "storageSize": 36864, "totalIndexSize": 36864, "indexSizes": {"_id_": 36864}, "avgObjSize": 111, "colInfo": [{"name": "collection", "type": "collection", "options": {}, "info": {"readOnly": false, "uuid": "00b08e80-5c4c-4ca9-a354-2a33fddce028"}, "idIndex": {"v": 2, "key": {"_id": 1}, "name": "_id_", "ns": "ns"}}], "schema": {"k0": "ObjectId", "k1": "BSON", "k2": "String", "k3": "Number", "k4": "String", "k5": "String"}}]}