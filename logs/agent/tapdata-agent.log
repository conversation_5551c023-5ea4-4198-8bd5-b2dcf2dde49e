[INFO ] 2025-06-04 03:01:40.704  [main] Application - disabledAlgorithms [SSLv3, TLSv1, TLSv1.1, DTLSv1.0, RC4, DES, MD5withRSA, DH keySize < 1024, EC keySize < 224, 3DES_EDE_CBC, anon, NULL, ECDH]->[SSLv3, DTLSv1.0, RC4, DES, MD5withRSA, DH keySize < 1024, EC keySize < 224, 3DES_EDE_CBC, anon, NULL, ECDH]
[INFO ] 2025-06-04 03:01:40.770  [main] Application - Starting application, code version 2025-05-30T10:30:15Z
[INFO ] 2025-06-04 03:01:41.314  [background-preinit] Version - HV000001: Hibernate Validator 9.0.0.CR1
[INFO ] 2025-06-04 03:01:41.403  [main] Application - Starting Application using Java 17.0.14 with PID 89141 (/Users/<USER>/IdeaProjects/tapdata/tapdata-github/tapdata-v35-jdk17/tapdata/iengine/iengine-app/target/classes started by samuel in /Users/<USER>/IdeaProjects/tapdata/tapdata-github/tapdata-v35-jdk17)
[INFO ] 2025-06-04 03:01:41.407  [main] Application - No active profile set, falling back to 1 default profile: "default"
[INFO ] 2025-06-04 03:01:41.993  [main] RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
[INFO ] 2025-06-04 03:01:42.104  [main] RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 106 ms. Found 0 MongoDB repository interfaces.
[INFO ] 2025-06-04 03:01:42.253  [main] ConnectorManager - TAPDATA_MONGO_CONN env variable does not set, will use default 
[INFO ] 2025-06-04 03:01:42.253  [main] ConnectorManager - ssl env variable does not set, will use default false
[INFO ] 2025-06-04 03:01:42.253  [main] ConnectorManager - cloud_accessCode env variable does not set, will use default "".
[INFO ] 2025-06-04 03:01:42.253  [main] ConnectorManager - cloud_retryTime env variable does not set, will use default 3
[INFO ] 2025-06-04 03:01:42.254  [main] ConnectorManager - backend_url env variable does not set, will use default [http://127.0.0.1:3000/api/]
[INFO ] 2025-06-04 03:01:42.254  [main] ConnectorManager - mode env variable does not set, will use default cluster
[INFO ] 2025-06-04 03:01:42.269  [main] ConnectorManager - 
Initialed variable
 - mongoURI: mongodb://localhost/tapdata_v35_jdk17
 - ssl: false
 - sslCA: 
 - sslPEM: 
 - mongodbConnParams: 
 - baseURLs: [http://127.0.0.1:3000/api/]
 - accessCode: 
 - restRetryTime: 3
 - mode: cluster
 - app_type: DAAS
 - process id: sam_iengine
 - job tags: null
 - region: null
 - zone: null
 - worker dir: null
[INFO ] 2025-06-04 03:01:42.276  [main] CloudSignUtil - ak/sk needSign false, accessKey null, secretKey null
[INFO ] 2025-06-04 03:01:42.663  [main] ConnectorManager - Available processors number: 10
[INFO ] 2025-06-04 03:01:42.666  [main] ConnectorManager - Java class path: /Users/<USER>/IdeaProjects/tapdata/tapdata-github/tapdata-v35-jdk17/tapdata/iengine/iengine-app/target/classes:/Users/<USER>/IdeaProjects/tapdata/tapdata-github/tapdata-v35-jdk17/tapdata-enterprise/iengine-enterprise/target/classes:/Users/<USER>/IdeaProjects/tapdata/tapdata-github/tapdata-v35-jdk17/tapdata/iengine/api/target/classes:/Users/<USER>/.m2/tapdata_v3_jdk17/org/springframework/spring-test/6.2.6/spring-test-6.2.6.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/mongodb/mongodb-driver-sync/5.2.1/mongodb-driver-sync-5.2.1.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/mongodb/bson/5.2.1/bson-5.2.1.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/mongodb/mongodb-driver-core/5.2.1/mongodb-driver-core-5.2.1.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/mongodb/bson-record-codec/5.2.1/bson-record-codec-5.2.1.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/io/tapdata/tapdata-storage-module/2.0-SNAPSHOT/tapdata-storage-module-2.0-SNAPSHOT.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/io/tapdata/tapdata-pdk-runner/2.0-SNAPSHOT/tapdata-pdk-runner-2.0-SNAPSHOT.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/ehcache/ehcache/3.10.8/ehcache-3.10.8.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/javax/cache/cache-api/1.1.1/cache-api-1.1.1.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/io/jsonwebtoken/jjwt-api/0.12.6/jjwt-api-0.12.6.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/io/jsonwebtoken/jjwt-impl/0.12.6/jjwt-impl-0.12.6.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/glassfish/jaxb/jaxb-runtime/4.0.5/jaxb-runtime-4.0.5.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/glassfish/jaxb/jaxb-core/4.0.5/jaxb-core-4.0.5.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/eclipse/angus/angus-activation/2.0.2/angus-activation-2.0.2.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/glassfish/jaxb/txw2/4.0.5/txw2-4.0.5.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/com/sun/istack/istack-commons-runtime/4.1.2/istack-commons-runtime-4.1.2.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/io/jsonwebtoken/jjwt-jackson/0.12.6/jjwt-jackson-0.12.6.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/com/alibaba/fastjson/1.2.83/fastjson-1.2.83.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/cglib/cglib/3.3.0/cglib-3.3.0.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/commons-codec/commons-codec/1.17.2/commons-codec-1.17.2.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/com/github/luben/zstd-jni/1.5.2-5/zstd-jni-1.5.2-5.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/apache/commons/commons-compress/1.27.1/commons-compress-1.27.1.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/rocksdb/rocksdbjni/7.3.1/rocksdbjni-7.3.1.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/io/tapdata/async-tools-module/2.0-SNAPSHOT/async-tools-module-2.0-SNAPSHOT.jar:/Users/<USER>/IdeaProjects/tapdata/tapdata-github/tapdata-v35-jdk17/tapdata/iengine/modules/proxy-client-module/target/classes:/Users/<USER>/.m2/tapdata_v3_jdk17/io/tapdata/websocket-client-module/2.0-SNAPSHOT/websocket-client-module-2.0-SNAPSHOT.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/io/netty/netty-all/4.1.119.Final/netty-all-4.1.119.Final.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/io/netty/netty-buffer/4.1.119.Final/netty-buffer-4.1.119.Final.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/io/netty/netty-codec/4.1.119.Final/netty-codec-4.1.119.Final.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/io/netty/netty-codec-dns/4.1.119.Final/netty-codec-dns-4.1.119.Final.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/io/netty/netty-codec-haproxy/4.1.119.Final/netty-codec-haproxy-4.1.119.Final.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/io/netty/netty-codec-http/4.1.119.Final/netty-codec-http-4.1.119.Final.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/io/netty/netty-codec-http2/4.1.119.Final/netty-codec-http2-4.1.119.Final.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/io/netty/netty-codec-memcache/4.1.119.Final/netty-codec-memcache-4.1.119.Final.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/io/netty/netty-codec-mqtt/4.1.119.Final/netty-codec-mqtt-4.1.119.Final.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/io/netty/netty-codec-redis/4.1.119.Final/netty-codec-redis-4.1.119.Final.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/io/netty/netty-codec-smtp/4.1.119.Final/netty-codec-smtp-4.1.119.Final.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/io/netty/netty-codec-socks/4.1.119.Final/netty-codec-socks-4.1.119.Final.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/io/netty/netty-codec-stomp/4.1.119.Final/netty-codec-stomp-4.1.119.Final.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/io/netty/netty-codec-xml/4.1.119.Final/netty-codec-xml-4.1.119.Final.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/io/netty/netty-common/4.1.119.Final/netty-common-4.1.119.Final.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/io/netty/netty-handler/4.1.119.Final/netty-handler-4.1.119.Final.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/io/netty/netty-transport-native-unix-common/4.1.119.Final/netty-transport-native-unix-common-4.1.119.Final.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/io/netty/netty-handler-proxy/4.1.119.Final/netty-handler-proxy-4.1.119.Final.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/io/netty/netty-handler-ssl-ocsp/4.1.119.Final/netty-handler-ssl-ocsp-4.1.119.Final.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/io/netty/netty-resolver/4.1.119.Final/netty-resolver-4.1.119.Final.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/io/netty/netty-resolver-dns/4.1.119.Final/netty-resolver-dns-4.1.119.Final.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/io/netty/netty-transport/4.1.119.Final/netty-transport-4.1.119.Final.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/io/netty/netty-transport-rxtx/4.1.119.Final/netty-transport-rxtx-4.1.119.Final.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/io/netty/netty-transport-sctp/4.1.119.Final/netty-transport-sctp-4.1.119.Final.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/io/netty/netty-transport-udt/4.1.119.Final/netty-transport-udt-4.1.119.Final.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/io/netty/netty-transport-classes-epoll/4.1.119.Final/netty-transport-classes-epoll-4.1.119.Final.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/io/netty/netty-transport-classes-kqueue/4.1.119.Final/netty-transport-classes-kqueue-4.1.119.Final.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/io/netty/netty-resolver-dns-classes-macos/4.1.119.Final/netty-resolver-dns-classes-macos-4.1.119.Final.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/io/netty/netty-transport-native-epoll/4.1.119.Final/netty-transport-native-epoll-4.1.119.Final-linux-x86_64.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/io/netty/netty-transport-native-epoll/4.1.119.Final/netty-transport-native-epoll-4.1.119.Final-linux-aarch_64.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/io/netty/netty-transport-native-epoll/4.1.119.Final/netty-transport-native-epoll-4.1.119.Final-linux-riscv64.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/io/netty/netty-transport-native-kqueue/4.1.119.Final/netty-transport-native-kqueue-4.1.119.Final-osx-x86_64.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/io/netty/netty-transport-native-kqueue/4.1.119.Final/netty-transport-native-kqueue-4.1.119.Final-osx-aarch_64.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/io/netty/netty-resolver-dns-native-macos/4.1.119.Final/netty-resolver-dns-native-macos-4.1.119.Final-osx-x86_64.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/io/netty/netty-resolver-dns-native-macos/4.1.119.Final/netty-resolver-dns-native-macos-4.1.119.Final-osx-aarch_64.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/io/tapdata/modules-api/2.0-SNAPSHOT/modules-api-2.0-SNAPSHOT.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/io/tapdata/tapdata-api/2.0.0-SNAPSHOT/tapdata-api-2.0.0-SNAPSHOT.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/io/tapdata/tapdata-pdk-api/2.0.0-SNAPSHOT/tapdata-pdk-api-2.0.0-SNAPSHOT.jar:/Users/<USER>/IdeaProjects/tapdata/tapdata-github/tapdata-v35-jdk17/tapdata/iengine/modules/observable-module/target/classes:/Users/<USER>/.m2/tapdata_v3_jdk17/net/openhft/chronicle-queue/5.21.91/chronicle-queue-5.21.91.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/net/openhft/chronicle-core/2.21.91/chronicle-core-2.21.91.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/net/openhft/chronicle-analytics/2.21ea0/chronicle-analytics-2.21ea0.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/net/openhft/chronicle-bytes/2.21.89/chronicle-bytes-2.21.89.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/net/openhft/chronicle-wire/2.21.91/chronicle-wire-2.21.91.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/net/openhft/compiler/2.21ea80/compiler-2.21ea80.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/net/openhft/chronicle-threads/2.21.86/chronicle-threads-2.21.86.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/net/openhft/affinity/3.21ea5/affinity-3.21ea5.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/commons-cli/commons-cli/1.4/commons-cli-1.4.jar:/Users/<USER>/IdeaProjects/tapdata/tapdata-github/tapdata-v35-jdk17/tapdata/iengine/modules/milestone-module/target/classes:/Users/<USER>/IdeaProjects/tapdata/tapdata-github/tapdata-v35-jdk17/tapdata/iengine/modules/skip-error-event-module/target/classes:/Users/<USER>/IdeaProjects/tapdata/tapdata-github/tapdata-v35-jdk17/tapdata/iengine/modules/test-run-module/target/classes:/Users/<USER>/IdeaProjects/tapdata/tapdata-github/tapdata-v35-jdk17/tapdata/iengine/modules/deduction-module/target/classes:/Users/<USER>/IdeaProjects/tapdata/tapdata-github/tapdata-v35-jdk17/tapdata/iengine/modules/custom-sql-filter-module/target/classes:/Users/<USER>/.m2/tapdata_v3_jdk17/io/tapdata/service-skeleton-module/2.0-SNAPSHOT/service-skeleton-module-2.0-SNAPSHOT.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/io/tapdata/script-engine-module/2.0-SNAPSHOT/script-engine-module-2.0-SNAPSHOT.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/graalvm/js/js-language/24.1.2/js-language-24.1.2.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/graalvm/regex/regex/24.1.2/regex-24.1.2.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/graalvm/truffle/truffle-api/24.1.2/truffle-api-24.1.2.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/graalvm/shadowed/icu4j/24.1.2/icu4j-24.1.2.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/graalvm/truffle/truffle-runtime/24.1.2/truffle-runtime-24.1.2.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/graalvm/truffle/truffle-enterprise/24.1.2/truffle-enterprise-24.1.2.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/graalvm/truffle/truffle-compiler/24.1.2/truffle-compiler-24.1.2.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/graalvm/sdk/jniutils/24.1.2/jniutils-24.1.2.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/graalvm/sdk/nativebridge/24.1.2/nativebridge-24.1.2.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/graalvm/js/js-scriptengine/24.1.2/js-scriptengine-24.1.2.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/graalvm/polyglot/polyglot/24.1.2/polyglot-24.1.2.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/graalvm/sdk/collections/24.1.2/collections-24.1.2.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/graalvm/sdk/nativeimage/24.1.2/nativeimage-24.1.2.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/graalvm/sdk/word/24.1.2/word-24.1.2.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/com/google/guava/guava/33.4.0-jre/guava-33.4.0-jre.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/com/google/guava/failureaccess/1.0.2/failureaccess-1.0.2.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/checkerframework/checker-qual/3.43.0/checker-qual-3.43.0.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/com/google/errorprone/error_prone_annotations/2.36.0/error_prone_annotations-2.36.0.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/com/google/j2objc/j2objc-annotations/3.0.0/j2objc-annotations-3.0.0.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/commons-io/commons-io/2.18.0/commons-io-2.18.0.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/springframework/boot/spring-boot-starter-web/3.4.5/spring-boot-starter-web-3.4.5.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/springframework/boot/spring-boot-starter/3.4.5/spring-boot-starter-3.4.5.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/springframework/boot/spring-boot/3.4.5/spring-boot-3.4.5.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/springframework/boot/spring-boot-autoconfigure/3.4.5/spring-boot-autoconfigure-3.4.5.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/jakarta/annotation/jakarta.annotation-api/2.1.1/jakarta.annotation-api-2.1.1.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/springframework/boot/spring-boot-starter-json/3.4.5/spring-boot-starter-json-3.4.5.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.18.3/jackson-datatype-jdk8-2.18.3.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/com/fasterxml/jackson/module/jackson-module-parameter-names/2.18.3/jackson-module-parameter-names-2.18.3.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/springframework/boot/spring-boot-starter-tomcat/3.4.5/spring-boot-starter-tomcat-3.4.5.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/apache/tomcat/embed/tomcat-embed-core/10.1.40/tomcat-embed-core-10.1.40.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/apache/tomcat/embed/tomcat-embed-el/10.1.40/tomcat-embed-el-10.1.40.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/apache/tomcat/embed/tomcat-embed-websocket/10.1.40/tomcat-embed-websocket-10.1.40.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/springframework/spring-web/6.2.2/spring-web-6.2.2.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/io/micrometer/micrometer-observation/1.14.6/micrometer-observation-1.14.6.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/io/micrometer/micrometer-commons/1.14.6/micrometer-commons-1.14.6.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/springframework/spring-webmvc/6.2.6/spring-webmvc-6.2.6.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/springframework/spring-aop/6.2.6/spring-aop-6.2.6.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/springframework/spring-expression/6.2.6/spring-expression-6.2.6.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/hibernate/validator/hibernate-validator/9.0.0.CR1/hibernate-validator-9.0.0.CR1.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/jakarta/validation/jakarta.validation-api/3.0.2/jakarta.validation-api-3.0.2.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/jboss/logging/jboss-logging/3.6.1.Final/jboss-logging-3.6.1.Final.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/com/fasterxml/classmate/1.7.0/classmate-1.7.0.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/com/fasterxml/jackson/dataformat/jackson-dataformat-yaml/2.18.3/jackson-dataformat-yaml-2.18.3.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/com/fasterxml/jackson/core/jackson-databind/2.18.3/jackson-databind-2.18.3.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/com/fasterxml/jackson/core/jackson-annotations/2.18.3/jackson-annotations-2.18.3.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/yaml/snakeyaml/2.3/snakeyaml-2.3.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/com/fasterxml/jackson/core/jackson-core/2.18.3/jackson-core-2.18.3.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/springframework/spring-websocket/6.2.6/spring-websocket-6.2.6.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/springframework/spring-context/6.2.6/spring-context-6.2.6.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/springframework/spring-core/6.2.6/spring-core-6.2.6.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/springframework/spring-jcl/6.2.6/spring-jcl-6.2.6.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/springframework/spring-messaging/6.2.6/spring-messaging-6.2.6.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/springframework/spring-beans/6.2.6/spring-beans-6.2.6.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/apache/commons/commons-collections4/4.5.0-M3/commons-collections4-4.5.0-M3.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/apache/commons/commons-lang3/3.17.0/commons-lang3-3.17.0.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/slf4j/slf4j-api/2.0.17/slf4j-api-2.0.17.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/springframework/boot/spring-boot-starter-jdbc/3.4.5/spring-boot-starter-jdbc-3.4.5.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/com/zaxxer/HikariCP/5.1.0/HikariCP-5.1.0.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/springframework/spring-jdbc/6.2.6/spring-jdbc-6.2.6.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/springframework/spring-tx/6.2.6/spring-tx-6.2.6.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/com/github/jsqlparser/jsqlparser/4.3/jsqlparser-4.3.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/apache/logging/log4j/log4j-slf4j-impl/2.17.1/log4j-slf4j-impl-2.17.1.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/apache/logging/log4j/log4j-api/2.17.1/log4j-api-2.17.1.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/apache/logging/log4j/log4j-core/2.17.1/log4j-core-2.17.1.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/apache/logging/log4j/log4j-1.2-api/2.17.1/log4j-1.2-api-2.17.1.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/springframework/boot/spring-boot-starter-data-mongodb/3.4.5/spring-boot-starter-data-mongodb-3.4.5.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/springframework/data/spring-data-mongodb/4.4.5/spring-data-mongodb-4.4.5.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/springframework/data/spring-data-commons/3.4.5/spring-data-commons-3.4.5.jar:/Users/<USER>/IdeaProjects/tapdata/tapdata-github/tapdata-v35-jdk17/tapdata/iengine/iengine-common/target/classes:/Users/<USER>/.m2/tapdata_v3_jdk17/org/bouncycastle/bcprov-jdk18on/1.80/bcprov-jdk18on-1.80.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.18.3/jackson-datatype-jsr310-2.18.3.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/json/json/20250107/json-20250107.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/com/google/code/gson/gson/2.12.1/gson-2.12.1.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/commons-net/commons-net/3.11.1/commons-net-3.11.1.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/commons-beanutils/commons-beanutils/1.10.1/commons-beanutils-1.10.1.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/commons-logging/commons-logging/1.1.3/commons-logging-1.1.3.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/dom4j/dom4j/2.1.4/dom4j-2.1.4.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/com/github/albfernandez/juniversalchardet/2.3.0/juniversalchardet-2.3.0.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/apache/avro/avro/1.12.0/avro-1.12.0.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/apache/kafka/kafka_2.13/3.8.1/kafka_2.13-3.8.1.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/apache/kafka/kafka-server-common/3.8.1/kafka-server-common-3.8.1.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/pcollections/pcollections/4.0.1/pcollections-4.0.1.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/apache/kafka/kafka-group-coordinator-api/3.8.1/kafka-group-coordinator-api-3.8.1.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/apache/kafka/kafka-group-coordinator/3.8.1/kafka-group-coordinator-3.8.1.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/apache/kafka/kafka-transaction-coordinator/3.8.1/kafka-transaction-coordinator-3.8.1.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/apache/kafka/kafka-metadata/3.8.1/kafka-metadata-3.8.1.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/apache/kafka/kafka-storage-api/3.8.1/kafka-storage-api-3.8.1.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/apache/kafka/kafka-tools-api/3.8.1/kafka-tools-api-3.8.1.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/apache/kafka/kafka-raft/3.8.1/kafka-raft-3.8.1.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/apache/kafka/kafka-storage/3.8.1/kafka-storage-3.8.1.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/apache/kafka/kafka-server/3.8.1/kafka-server-3.8.1.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/net/sourceforge/argparse4j/argparse4j/0.7.0/argparse4j-0.7.0.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/commons-validator/commons-validator/1.7/commons-validator-1.7.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/commons-digester/commons-digester/2.1/commons-digester-2.1.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/com/fasterxml/jackson/module/jackson-module-scala_2.13/2.18.3/jackson-module-scala_2.13-2.18.3.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/com/thoughtworks/paranamer/paranamer/2.8/paranamer-2.8.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/com/fasterxml/jackson/dataformat/jackson-dataformat-csv/2.18.3/jackson-dataformat-csv-2.18.3.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/net/sf/jopt-simple/jopt-simple/5.0.4/jopt-simple-5.0.4.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/bitbucket/b_c/jose4j/0.9.4/jose4j-0.9.4.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/com/yammer/metrics/metrics-core/2.2.0/metrics-core-2.2.0.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/scala-lang/modules/scala-collection-compat_2.13/2.10.0/scala-collection-compat_2.13-2.10.0.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/scala-lang/modules/scala-java8-compat_2.13/1.0.2/scala-java8-compat_2.13-1.0.2.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/com/typesafe/scala-logging/scala-logging_2.13/3.9.4/scala-logging_2.13-3.9.4.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/io/dropwizard/metrics/metrics-core/4.1.12.1/metrics-core-4.1.12.1.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/apache/kafka/kafka-clients/3.7.2/kafka-clients-3.7.2.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/lz4/lz4-java/1.8.0/lz4-java-1.8.0.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/xerial/snappy/snappy-java/1.1.10.7/snappy-java-1.1.10.7.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/apache/kafka/kafka-streams/3.7.2/kafka-streams-3.7.2.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/quartz-scheduler/quartz/2.5.0/quartz-2.5.0.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/com/hankcs/hanlp/portable-1.5.3/hanlp-portable-1.5.3.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/com/hankcs/nlp/hanlp-lucene-plugin/1.1.2/hanlp-lucene-plugin-1.1.2.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/apache/lucene/lucene-analysis-common/9.12.1/lucene-analysis-common-9.12.1.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/apache/lucene/lucene-queryparser/9.12.1/lucene-queryparser-9.12.1.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/projectlombok/lombok/1.18.36/lombok-1.18.36.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/com/lmax/disruptor/3.4.2/disruptor-3.4.2.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/com/vividsolutions/jts/1.13/jts-1.13.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/com/github/ben-manes/caffeine/caffeine/3.1.8/caffeine-3.1.8.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/com/github/os72/protobuf-dynamic/1.0.1/protobuf-dynamic-1.0.1.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/com/google/protobuf/protobuf-java-util/4.29.3/protobuf-java-util-4.29.3.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/com/google/protobuf/protobuf-java/4.29.3/protobuf-java-4.29.3.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/voovan/voovan-framework/4.3.8/voovan-framework-4.3.8.jar:/Users/<USER>/IdeaProjects/tapdata/tapdata-github/tapdata-v35-jdk17/tapdata/manager/tm-sdk/target/classes:/Users/<USER>/.m2/tapdata_v3_jdk17/io/prometheus/simpleclient_httpserver/0.16.0/simpleclient_httpserver-0.16.0.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/io/prometheus/simpleclient/0.16.0/simpleclient-0.16.0.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/io/prometheus/simpleclient_tracer_otel/0.16.0/simpleclient_tracer_otel-0.16.0.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/io/prometheus/simpleclient_tracer_common/0.16.0/simpleclient_tracer_common-0.16.0.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/io/prometheus/simpleclient_tracer_otel_agent/0.16.0/simpleclient_tracer_otel_agent-0.16.0.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/io/prometheus/simpleclient_common/0.16.0/simpleclient_common-0.16.0.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/io/prometheus/simpleclient_hotspot/0.16.0/simpleclient_hotspot-0.16.0.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/com/github/oshi/oshi-core/5.8.3/oshi-core-5.8.3.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/net/java/dev/jna/jna/5.10.0/jna-5.10.0.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/net/java/dev/jna/jna-platform/5.10.0/jna-platform-5.10.0.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/io/tapdata/tapdata-common/2.0-SNAPSHOT/tapdata-common-2.0-SNAPSHOT.jar:/Users/<USER>/IdeaProjects/tapdata/tapdata-github/tapdata-v35-jdk17/tapdata/manager/tm-common/target/classes:/Users/<USER>/.m2/tapdata_v3_jdk17/com/tapdata/common/2.2.2/common-2.2.2.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/ognl/ognl/3.1.26/ognl-3.1.26.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/io/github/openlg/graphlib/1.1.0/graphlib-1.1.0.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/cn/hutool/hutool-crypto/5.8.36/hutool-crypto-5.8.36.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/cn/hutool/hutool-json/5.8.36/hutool-json-5.8.36.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/cn/hutool/hutool-extra/5.8.36/hutool-extra-5.8.36.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/cn/hutool/hutool-setting/5.8.36/hutool-setting-5.8.36.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/cn/hutool/hutool-log/5.8.36/hutool-log-5.8.36.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/apache/httpcomponents/httpclient/4.5.14/httpclient-4.5.14.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/cn/hutool/hutool-core/5.8.36/hutool-core-5.8.36.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/com/squareup/okhttp3/okhttp/4.12.0/okhttp-4.12.0.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/com/squareup/okio/okio/3.6.0/okio-3.6.0.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/com/squareup/okio/okio-jvm/3.6.0/okio-jvm-3.6.0.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/jetbrains/kotlin/kotlin-stdlib-common/1.9.25/kotlin-stdlib-common-1.9.25.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/jetbrains/kotlin/kotlin-stdlib-jdk8/1.9.25/kotlin-stdlib-jdk8-1.9.25.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/jetbrains/kotlin/kotlin-stdlib-jdk7/1.9.25/kotlin-stdlib-jdk7-1.9.25.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/jetbrains/kotlin/kotlin-stdlib/2.1.10/kotlin-stdlib-2.1.10.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/jetbrains/annotations/13.0/annotations-13.0.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/apache/commons/commons-text/1.13.0/commons-text-1.13.0.jar:/Users/<USER>/IdeaProjects/tapdata/tapdata-github/tapdata-v35-jdk17/hazelcast/hazelcast/target/classes:/Users/<USER>/.m2/tapdata_v3_jdk17/com/fasterxml/jackson/jr/jackson-jr-objects/2.18.3/jackson-jr-objects-2.18.3.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/com/fasterxml/jackson/jr/jackson-jr-annotation-support/2.18.3/jackson-jr-annotation-support-2.18.3.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/snakeyaml/snakeyaml-engine/2.3/snakeyaml-engine-2.3.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/io/github/classgraph/classgraph/4.8.149/classgraph-4.8.149.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/com/github/erosb/everit-json-schema/1.14.1/everit-json-schema-1.14.1.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/com/fasterxml/jackson/dataformat/jackson-dataformat-xml/2.18.3/jackson-dataformat-xml-2.18.3.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/codehaus/woodstox/stax2-api/4.2/stax2-api-4.2.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/com/fasterxml/woodstox/woodstox-core/7.1.0/woodstox-core-7.1.0.jar:/Users/<USER>/IdeaProjects/tapdata/tapdata-github/tapdata-v35-jdk17/hazelcast/hazelcast-persistence/target/classes:/Users/<USER>/.m2/tapdata_v3_jdk17/org/apache/httpcomponents/httpcore/4.4.16/httpcore-4.4.16.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/mapdb/mapdb/3.0.8/mapdb-3.0.8.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/eclipse/collections/eclipse-collections-api/12.0.0.M3/eclipse-collections-api-12.0.0.M3.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/eclipse/collections/eclipse-collections/12.0.0.M3/eclipse-collections-12.0.0.M3.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/eclipse/collections/eclipse-collections-forkjoin/12.0.0.M3/eclipse-collections-forkjoin-12.0.0.M3.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/net/jpountz/lz4/lz4/1.3.0/lz4-1.3.0.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/mapdb/elsa/3.0.0-M5/elsa-3.0.0-M5.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/it/unimi/dsi/fastutil/8.5.13/fastutil-8.5.13.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/openjdk/nashorn/nashorn-core/15.6/nashorn-core-15.6.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/ow2/asm/asm/7.1/asm-7.1.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/ow2/asm/asm-commons/7.3.1/asm-commons-7.3.1.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/ow2/asm/asm-analysis/7.3.1/asm-analysis-7.3.1.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/ow2/asm/asm-tree/7.1/asm-tree-7.1.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/ow2/asm/asm-util/7.3.1/asm-util-7.3.1.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/apache/httpcomponents/client5/httpclient5/5.4.4/httpclient5-5.4.4.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/apache/httpcomponents/core5/httpcore5/5.3.4/httpcore5-5.3.4.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/apache/httpcomponents/core5/httpcore5-h2/5.3.4/httpcore5-h2-5.3.4.jar:/Users/<USER>/IdeaProjects/tapdata/tapdata-github/tapdata-v35-jdk17/tapdata/iengine/validator/target/classes:/Users/<USER>/.m2/tapdata_v3_jdk17/org/apache/commons/commons-csv/1.5/commons-csv-1.5.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/simplejavamail/simple-java-mail/5.0.3/simple-java-mail-5.0.3.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/com/sun/mail/javax.mail/1.6.0/javax.mail-1.6.0.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/javax/activation/activation/1.1.1/activation-1.1.1.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/com/github/bbottema/emailaddress-rfc2822/1.0.1/emailaddress-rfc2822-1.0.1.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/com/google/code/findbugs/jsr305/3.0.1/jsr305-3.0.1.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/samba/jcifs/jcifs/1.3.17/jcifs-1.3.17.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/io/tapdata/error-code-core/2.0-SNAPSHOT/error-code-core-2.0-SNAPSHOT.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/io/tapdata/pdk-error-code/2.0-SNAPSHOT/pdk-error-code-2.0-SNAPSHOT.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/io/tapdata/error-code-scanner/2.0-SNAPSHOT/error-code-scanner-2.0-SNAPSHOT.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/reflections/reflections/0.10.2/reflections-0.10.2.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/javassist/javassist/3.28.0-GA/javassist-3.28.0-GA.jar:/Users/<USER>/IdeaProjects/tapdata/tapdata-github/tapdata-v35-jdk17/tapdata/iengine/modules/task-resource-supervisor-module/target/classes:/Users/<USER>/.m2/tapdata_v3_jdk17/org/apache/lucene/lucene-core/9.12.1/lucene-core-9.12.1.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/jakarta/xml/bind/jakarta.xml.bind-api/4.0.2/jakarta.xml.bind-api-4.0.2.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/jakarta/activation/jakarta.activation-api/2.1.3/jakarta.activation-api-2.1.3.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/org/objenesis/objenesis/3.3/objenesis-3.3.jar:/Users/<USER>/.m2/tapdata_v3_jdk17/net/bytebuddy/byte-buddy-agent/1.12.23/byte-buddy-agent-1.12.23.jar:/Users/<USER>/IdeaProjects/tapdata/tapdata-github/tapdata-v35-jdk17/hazelcast/hazelcast/target/hazelcast-5.5.0.jar:/Users/<USER>/Applications/IntelliJ IDEA Ultimate.app/Contents/lib/idea_rt.jar.
 Agent version: -
[INFO ] 2025-06-04 03:01:42.750  [main] ConnectorManager - Login params: accessCode=3324cfdf-7d3e-4792-bd32-571638d4562f, endpoint=[http://127.0.0.1:3000/api/]
[INFO ] 2025-06-04 03:01:42.805  [main] Version - flow engine version: 
[INFO ] 2025-06-04 03:01:43.469  [main] SettingService - [Setting] - Loading tapdata settings...
[java.base/java.lang.Thread.getStackTrace(Thread.java:1619), io.tapdata.common.SettingService.loadSettings(SettingService.java:40), io.tapdata.Schedule.ConnectorManager.init(ConnectorManager.java:216), java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method), java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77), java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43), java.base/java.lang.reflect.Method.invoke(Method.java:569), org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457), org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:401), org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:219), org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:429), org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1810), org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:607), org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529), org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339), org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371), org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337), org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202), org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1221), org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1187), org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1122), org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987), org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627), org.springframework.boot.SpringApplication.refresh(SpringApplication.java:753), org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439), org.springframework.boot.SpringApplication.run(SpringApplication.java:318), io.tapdata.Application.main(Application.java:133)]
[INFO ] 2025-06-04 03:01:44.724  [main] TransformerManager - Transformer init variables
 - process id: sam_iengine
 - job tags: 
 - region: 
 - zone: 
 - worker dir: null
[INFO ] 2025-06-04 03:01:44.813  [main] HazelcastUtil - -- listing properties --
hazelcast.operation.call.timeout.millis=300000
hazelcast.logging.type=log4j2

[INFO ] 2025-06-04 03:01:44.883  [main] AddressPicker - [LOCAL] [dev] [5.5.0] Interfaces is disabled, trying to pick one address from TCP-IP config addresses: []
[WARN ] 2025-06-04 03:01:44.884  [main] AddressPicker - [LOCAL] [dev] [5.5.0] Could not find a matching address to start with! Picking one of non-loopback addresses.
[INFO ] 2025-06-04 03:01:44.949  [main] logo - [************]:5701 [dev] [5.5.0] 
	+       +  o    o     o     o---o o----o o      o---o     o     o----o o--o--o
	+ +   + +  |    |    / \       /  |      |     /         / \    |         |   
	+ + + + +  o----o   o   o     o   o----o |    o         o   o   o----o    |   
	+ +   + +  |    |  /     \   /    |      |     \       /     \       |    |   
	+       +  o    o o       o o---o o----o o----o o---o o       o o----o    o   
[INFO ] 2025-06-04 03:01:44.949  [main] system - [************]:5701 [dev] [5.5.0] Copyright (c) 2008-2022, Hazelcast, Inc. All Rights Reserved.
[INFO ] 2025-06-04 03:01:44.949  [main] system - [************]:5701 [dev] [5.5.0] Hazelcast Platform 5.5.0 (20250513 - e932f53) starting at [************]:5701
[INFO ] 2025-06-04 03:01:44.949  [main] system - [************]:5701 [dev] [5.5.0] Cluster name: dev
[INFO ] 2025-06-04 03:01:44.949  [main] system - [************]:5701 [dev] [5.5.0] Integrity Checker is disabled. Fail-fast on corrupted executables will not be performed. For more information, see the documentation for Integrity Checker.
[INFO ] 2025-06-04 03:01:44.949  [main] system - [************]:5701 [dev] [5.5.0] Jet is enabled
[INFO ] 2025-06-04 03:01:45.450  [main] security - [************]:5701 [dev] [5.5.0] Enable DEBUG/FINE log level for log category com.hazelcast.system.security  or use -Dhazelcast.security.recommendations system property to see 🔒 security recommendations and the status of current config.
[INFO ] 2025-06-04 03:01:45.536  [main] Node - [************]:5701 [dev] [5.5.0] Using TCP/IP discovery
[WARN ] 2025-06-04 03:01:45.539  [main] CPSubsystem - [************]:5701 [dev] [5.5.0] CP Subsystem is not enabled. CP data structures will operate in UNSAFE mode! Please note that UNSAFE mode will not provide strong consistency guarantees.
[INFO ] 2025-06-04 03:01:45.865  [main] JetServiceBackend - [************]:5701 [dev] [5.5.0] Setting number of cooperative threads and default parallelism to 10
[INFO ] 2025-06-04 03:01:45.873  [main] Diagnostics - [************]:5701 [dev] [5.5.0] Diagnostics disabled. To enable add -Dhazelcast.diagnostics.enabled=true to the JVM arguments.
[INFO ] 2025-06-04 03:01:45.878  [main] LifecycleService - [************]:5701 [dev] [5.5.0] [************]:5701 is STARTING
[INFO ] 2025-06-04 03:01:45.900  [main] ClusterService - [************]:5701 [dev] [5.5.0] 

Members {size:1, ver:1} [
	Member [************]:5701 - cd7a4266-d9b7-4616-a921-c88ca4dbdecf this
]

[INFO ] 2025-06-04 03:01:45.910  [main] JobCoordinationService - [************]:5701 [dev] [5.5.0] Jet started scanning for jobs
[INFO ] 2025-06-04 03:01:45.912  [main] LifecycleService - [************]:5701 [dev] [5.5.0] [************]:5701 is STARTED
[INFO ] 2025-06-04 03:01:45.917  [main] TapdataTaskScheduler - [Task scheduler] instance no: sam_iengine
[INFO ] 2025-06-04 03:01:45.985  [Management Websocket Health Check] ManagementWebsocketHandler - Connect to web socket server success, url ws://127.0.0.1:3000/ws/agent?agentId=sam_iengine&access_token=a974a3460ff940afb68b8ed8d5d3af8625644b8d416b46a2af7d60dc1acf50ac&singletonTag=83c13ae6-32b1-43fb-8e79-973f52482776
[INFO ] 2025-06-04 03:01:46.118  [main] Application - Started Application in 5.243 seconds (process running for 6.597)
[INFO ] 2025-06-04 03:01:46.125  [main] PDK - Application [Looking for Aspect annotations...]
[INFO ] 2025-06-04 03:01:46.203  [main] PDK - Application [Looking for Aspect annotations takes 78]
[INFO ] 2025-06-04 03:01:46.206  [main] StartResultUtil - Write start result to file: .agentStartMsg
  {"msg":"","codeVersion":"2025-05-30T10:30:15Z","jvmZoneId":"GMT","osZoneId":"Asia/Shanghai","version":"-","status":"ok"}
[ERROR] 2025-06-04 03:01:47.033  [main] PDK - LOG [Get python jar path error]
[WARN ] 2025-06-04 03:01:47.033  [main] PDK - ApplicationStartAspectHandler [Can not load python engine, msg: Cannot invoke "javax.script.ScriptEngine.eval(String)" because "scriptEnginePy" is null]
[INFO ] 2025-06-04 03:01:47.033  [main] TapdataTaskScheduler - Stop task which agent id is sam_iengine and status is stopping
[INFO ] 2025-06-04 03:01:47.044  [main] ConnectorNodeService - Global connector thread pool started, interval ms: 300000, timeout ms: 1800000
[INFO ] 2025-06-04 03:07:43.336  [EMS-3-thread-4] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-06-04 03:07:43.337  [EMS-3-thread-4] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_sam_iengine]]
[INFO ] 2025-06-04 03:11:46.063  [hz.sam_iengine.HealthMonitor] HealthMonitor - [************]:5701 [dev] [5.5.0] processors=10, physical.memory.total=64.0G, physical.memory.free=277.4M, swap.space.total=0, swap.space.free=0, heap.memory.used=97.9M, heap.memory.free=142.1M, heap.memory.total=240.0M, heap.memory.max=16.0G, heap.memory.used/total=40.80%, heap.memory.used/max=0.60%, minor.gc.count=21, minor.gc.time=96ms, major.gc.count=0, major.gc.time=0ms, load.process=0.00%, load.system=NaN%, load.systemAverage=12.79, thread.count=215, thread.peakCount=224, cluster.timeDiff=0, event.q.size=0, executor.q.async.size=0, executor.q.client.size=0, executor.q.client.query.size=0, executor.q.client.blocking.size=0, executor.q.query.size=0, executor.q.scheduled.size=0, executor.q.io.size=0, executor.q.system.size=0, executor.q.operations.size=0, executor.q.priorityOperation.size=0, operations.completed.count=1, executor.q.mapLoad.size=0, executor.q.mapLoadAllKeys.size=0, executor.q.cluster.size=0, executor.q.response.size=0, operations.running.count=0, operations.pending.invocations.percentage=0.00%, operations.pending.invocations.count=0, proxy.count=0, clientEndpoint.count=0, connection.active.count=0, client.connection.count=0, connection.count=0
[INFO ] 2025-06-04 03:12:06.069  [hz.sam_iengine.HealthMonitor] HealthMonitor - [************]:5701 [dev] [5.5.0] processors=10, physical.memory.total=64.0G, physical.memory.free=175.8M, swap.space.total=0, swap.space.free=0, heap.memory.used=103.1M, heap.memory.free=136.9M, heap.memory.total=240.0M, heap.memory.max=16.0G, heap.memory.used/total=42.96%, heap.memory.used/max=0.63%, minor.gc.count=21, minor.gc.time=96ms, major.gc.count=0, major.gc.time=0ms, load.process=0.00%, load.system=NaN%, load.systemAverage=14.85, thread.count=215, thread.peakCount=224, cluster.timeDiff=0, event.q.size=0, executor.q.async.size=0, executor.q.client.size=0, executor.q.client.query.size=0, executor.q.client.blocking.size=0, executor.q.query.size=0, executor.q.scheduled.size=0, executor.q.io.size=0, executor.q.system.size=0, executor.q.operations.size=0, executor.q.priorityOperation.size=0, operations.completed.count=1, executor.q.mapLoad.size=0, executor.q.mapLoadAllKeys.size=0, executor.q.cluster.size=0, executor.q.response.size=0, operations.running.count=0, operations.pending.invocations.percentage=0.00%, operations.pending.invocations.count=0, proxy.count=0, clientEndpoint.count=0, connection.active.count=0, client.connection.count=0, connection.count=0
[INFO ] 2025-06-04 03:12:43.360  [EMS-3-thread-1] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_sam_iengine]]
[INFO ] 2025-06-04 03:12:43.360  [EMS-3-thread-2] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-06-04 03:17:43.377  [EMS-3-thread-4] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-06-04 03:17:43.377  [EMS-3-thread-3] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_sam_iengine]]
[INFO ] 2025-06-04 03:22:43.402  [EMS-3-thread-1] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-06-04 03:22:43.402  [EMS-3-thread-1] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_sam_iengine]]
[INFO ] 2025-06-04 03:23:06.198  [hz.sam_iengine.HealthMonitor] HealthMonitor - [************]:5701 [dev] [5.5.0] processors=10, physical.memory.total=64.0G, physical.memory.free=8.2G, swap.space.total=0, swap.space.free=0, heap.memory.used=143.8M, heap.memory.free=96.2M, heap.memory.total=240.0M, heap.memory.max=16.0G, heap.memory.used/total=59.93%, heap.memory.used/max=0.88%, minor.gc.count=22, minor.gc.time=103ms, major.gc.count=0, major.gc.time=0ms, load.process=0.00%, load.system=NaN%, load.systemAverage=15.59, thread.count=218, thread.peakCount=224, cluster.timeDiff=0, event.q.size=0, executor.q.async.size=0, executor.q.client.size=0, executor.q.client.query.size=0, executor.q.client.blocking.size=0, executor.q.query.size=0, executor.q.scheduled.size=0, executor.q.io.size=0, executor.q.system.size=0, executor.q.operations.size=0, executor.q.priorityOperation.size=0, operations.completed.count=1, executor.q.mapLoad.size=0, executor.q.mapLoadAllKeys.size=0, executor.q.cluster.size=0, executor.q.response.size=0, operations.running.count=0, operations.pending.invocations.percentage=0.00%, operations.pending.invocations.count=0, proxy.count=0, clientEndpoint.count=0, connection.active.count=0, client.connection.count=0, connection.count=0
[INFO ] 2025-06-04 03:23:26.203  [hz.sam_iengine.HealthMonitor] HealthMonitor - [************]:5701 [dev] [5.5.0] processors=10, physical.memory.total=64.0G, physical.memory.free=5.7G, swap.space.total=0, swap.space.free=0, heap.memory.used=147.3M, heap.memory.free=92.7M, heap.memory.total=240.0M, heap.memory.max=16.0G, heap.memory.used/total=61.38%, heap.memory.used/max=0.90%, minor.gc.count=22, minor.gc.time=103ms, major.gc.count=0, major.gc.time=0ms, load.process=0.00%, load.system=NaN%, load.systemAverage=17.26, thread.count=218, thread.peakCount=224, cluster.timeDiff=0, event.q.size=0, executor.q.async.size=0, executor.q.client.size=0, executor.q.client.query.size=0, executor.q.client.blocking.size=0, executor.q.query.size=0, executor.q.scheduled.size=0, executor.q.io.size=0, executor.q.system.size=0, executor.q.operations.size=0, executor.q.priorityOperation.size=0, operations.completed.count=1, executor.q.mapLoad.size=0, executor.q.mapLoadAllKeys.size=0, executor.q.cluster.size=0, executor.q.response.size=0, operations.running.count=0, operations.pending.invocations.percentage=0.00%, operations.pending.invocations.count=0, proxy.count=0, clientEndpoint.count=0, connection.active.count=0, client.connection.count=0, connection.count=0
[INFO ] 2025-06-04 03:27:43.417  [EMS-3-thread-1] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-06-04 03:27:43.418  [EMS-3-thread-1] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_sam_iengine]]
[INFO ] 2025-06-04 03:32:43.442  [EMS-3-thread-4] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-06-04 03:32:43.443  [EMS-3-thread-1] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_sam_iengine]]
[INFO ] 2025-06-04 03:34:15.071  [hz.ShutdownThread] Node - [************]:5701 [dev] [5.5.0] Running shutdown hook... Current node state: ACTIVE
[INFO ] 2025-06-04 03:34:15.082  [hz.ShutdownThread] LifecycleService - [************]:5701 [dev] [5.5.0] [************]:5701 is SHUTTING_DOWN
[WARN ] 2025-06-04 03:34:15.087  [hz.ShutdownThread] Node - [************]:5701 [dev] [5.5.0] Terminating forcefully...
[INFO ] 2025-06-04 03:34:15.087  [hz.ShutdownThread] Node - [************]:5701 [dev] [5.5.0] Shutting down connection manager...
[INFO ] 2025-06-04 03:34:15.088  [hz.ShutdownThread] Node - [************]:5701 [dev] [5.5.0] Shutting down node engine...
[INFO ] 2025-06-04 03:34:15.095  [hz.ShutdownThread] NodeExtension - [************]:5701 [dev] [5.5.0] Destroying node NodeExtension.
[INFO ] 2025-06-04 03:34:15.096  [hz.ShutdownThread] Node - [************]:5701 [dev] [5.5.0] Hazelcast Shutdown is completed in 9 ms.
[INFO ] 2025-06-04 03:34:15.096  [hz.ShutdownThread] LifecycleService - [************]:5701 [dev] [5.5.0] [************]:5701 is SHUTDOWN
