[TRACE] 2025-06-04 04:17:08.154 - [TAP-6799(100)][53f0606d-3528-4aa5-ad0b-e71efbe2c405] - Node 53f0606d-3528-4aa5-ad0b-e71efbe2c405[53f0606d-3528-4aa5-ad0b-e71efbe2c405] start preload schema,table counts: 0 
[TRACE] 2025-06-04 04:17:08.159 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:17:08.159 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:17:08.160 - [TAP-6799(100)][53f0606d-3528-4aa5-ad0b-e71efbe2c405] - Node 53f0606d-3528-4aa5-ad0b-e71efbe2c405[53f0606d-3528-4aa5-ad0b-e71efbe2c405] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:17:08.160 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:17:08.160 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:17:08.160 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:17:08.160 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:17:08.161 - [TAP-6799(100)][MDM_packages] - Node field_add_del_processor(MDM_packages: 3080ce1e-1a69-43e1-8283-6bfbdbad03d9) enable batch process 
[TRACE] 2025-06-04 04:17:08.161 - [TAP-6799(100)][MDM_packages] - Node js_processor(MDM_packages: 8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d) enable batch process 
[TRACE] 2025-06-04 04:17:08.566 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] running status set to false 
[TRACE] 2025-06-04 04:17:08.566 - [TAP-6799(100)][MDM_packages] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749010628447 
[TRACE] 2025-06-04 04:17:08.567 - [TAP-6799(100)][MDM_packages] - PDK connector node released: HazelcastSampleSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749010628447 
[TRACE] 2025-06-04 04:17:08.567 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] schema data cleaned 
[TRACE] 2025-06-04 04:17:08.569 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] monitor closed 
[TRACE] 2025-06-04 04:17:08.569 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] close complete, cost 5 ms 
[TRACE] 2025-06-04 04:17:08.579 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] running status set to false 
[TRACE] 2025-06-04 04:17:08.614 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] schema data cleaned 
[TRACE] 2025-06-04 04:17:08.615 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] monitor closed 
[TRACE] 2025-06-04 04:17:08.615 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] close complete, cost 45 ms 
[TRACE] 2025-06-04 04:17:08.833 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] running status set to false 
[INFO ] 2025-06-04 04:17:08.833 - [TAP-6799(100)][MDM_packages][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-03e42bc6-1b49-4100-9c47-8ed36cf9533f 
[INFO ] 2025-06-04 04:17:08.834 - [TAP-6799(100)][MDM_packages][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-03e42bc6-1b49-4100-9c47-8ed36cf9533f 
[INFO ] 2025-06-04 04:17:08.834 - [TAP-6799(100)][MDM_packages][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:17:08.838 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] schema data cleaned 
[TRACE] 2025-06-04 04:17:08.838 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] monitor closed 
[TRACE] 2025-06-04 04:17:08.838 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] close complete, cost 7 ms 
[TRACE] 2025-06-04 04:17:08.865 - [TAP-6799(100)][53f0606d-3528-4aa5-ad0b-e71efbe2c405] - Node 53f0606d-3528-4aa5-ad0b-e71efbe2c405[53f0606d-3528-4aa5-ad0b-e71efbe2c405] running status set to false 
[TRACE] 2025-06-04 04:17:08.865 - [TAP-6799(100)][53f0606d-3528-4aa5-ad0b-e71efbe2c405] - Node 53f0606d-3528-4aa5-ad0b-e71efbe2c405[53f0606d-3528-4aa5-ad0b-e71efbe2c405] schema data cleaned 
[TRACE] 2025-06-04 04:17:08.865 - [TAP-6799(100)][53f0606d-3528-4aa5-ad0b-e71efbe2c405] - Node 53f0606d-3528-4aa5-ad0b-e71efbe2c405[53f0606d-3528-4aa5-ad0b-e71efbe2c405] monitor closed 
[TRACE] 2025-06-04 04:17:08.866 - [TAP-6799(100)][53f0606d-3528-4aa5-ad0b-e71efbe2c405] - Node 53f0606d-3528-4aa5-ad0b-e71efbe2c405[53f0606d-3528-4aa5-ad0b-e71efbe2c405] close complete, cost 1 ms 
[TRACE] 2025-06-04 04:17:08.870 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 04:17:08.871 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 04:17:08.871 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 04:17:08.948 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 04:17:08.949 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:17:08.949 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:17:08.949 - [TAP-6799(100)][66e17c34-77dd-4135-a109-1b4524b89f07] - Node 66e17c34-77dd-4135-a109-1b4524b89f07[66e17c34-77dd-4135-a109-1b4524b89f07] start preload schema,table counts: 0 
[TRACE] 2025-06-04 04:17:08.949 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:17:08.949 - [TAP-6799(100)][66e17c34-77dd-4135-a109-1b4524b89f07] - Node 66e17c34-77dd-4135-a109-1b4524b89f07[66e17c34-77dd-4135-a109-1b4524b89f07] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:17:08.949 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:17:08.950 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 73fce465-47ee-46f4-abb7-173648d43889) enable batch process 
[TRACE] 2025-06-04 04:17:09.268 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] running status set to false 
[TRACE] 2025-06-04 04:17:09.271 - [TAP-6799(100)][FDM_product] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010629147 
[TRACE] 2025-06-04 04:17:09.271 - [TAP-6799(100)][FDM_product] - PDK connector node released: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010629147 
[TRACE] 2025-06-04 04:17:09.272 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] schema data cleaned 
[TRACE] 2025-06-04 04:17:09.272 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] monitor closed 
[TRACE] 2025-06-04 04:17:09.272 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] close complete, cost 4 ms 
[TRACE] 2025-06-04 04:17:09.473 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] running status set to false 
[TRACE] 2025-06-04 04:17:09.473 - [TAP-6799(100)][66e17c34-77dd-4135-a109-1b4524b89f07] - Node 66e17c34-77dd-4135-a109-1b4524b89f07[66e17c34-77dd-4135-a109-1b4524b89f07] running status set to false 
[INFO ] 2025-06-04 04:17:09.473 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-c324815b-4ef4-4b79-831d-fa9b0a233ebd 
[TRACE] 2025-06-04 04:17:09.473 - [TAP-6799(100)][66e17c34-77dd-4135-a109-1b4524b89f07] - Node 66e17c34-77dd-4135-a109-1b4524b89f07[66e17c34-77dd-4135-a109-1b4524b89f07] schema data cleaned 
[INFO ] 2025-06-04 04:17:09.474 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-c324815b-4ef4-4b79-831d-fa9b0a233ebd 
[TRACE] 2025-06-04 04:17:09.474 - [TAP-6799(100)][66e17c34-77dd-4135-a109-1b4524b89f07] - Node 66e17c34-77dd-4135-a109-1b4524b89f07[66e17c34-77dd-4135-a109-1b4524b89f07] monitor closed 
[INFO ] 2025-06-04 04:17:09.474 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:17:09.474 - [TAP-6799(100)][66e17c34-77dd-4135-a109-1b4524b89f07] - Node 66e17c34-77dd-4135-a109-1b4524b89f07[66e17c34-77dd-4135-a109-1b4524b89f07] close complete, cost 1 ms 
[TRACE] 2025-06-04 04:17:09.476 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] schema data cleaned 
[TRACE] 2025-06-04 04:17:09.476 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] monitor closed 
[TRACE] 2025-06-04 04:17:09.477 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] close complete, cost 8 ms 
[TRACE] 2025-06-04 04:17:09.477 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 04:17:09.477 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 04:17:09.478 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 04:17:09.478 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 04:17:09.566 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:17:09.566 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:17:09.566 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:17:09.566 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:17:09.567 - [TAP-6799(100)][1cc89333-71b2-4ffb-bea3-1bbc988a71ad] - Node 1cc89333-71b2-4ffb-bea3-1bbc988a71ad[1cc89333-71b2-4ffb-bea3-1bbc988a71ad] start preload schema,table counts: 0 
[TRACE] 2025-06-04 04:17:09.567 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:17:09.567 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:17:09.567 - [TAP-6799(100)][1cc89333-71b2-4ffb-bea3-1bbc988a71ad] - Node 1cc89333-71b2-4ffb-bea3-1bbc988a71ad[1cc89333-71b2-4ffb-bea3-1bbc988a71ad] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:17:09.567 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 1674c3ea-16fc-4674-9a27-0cd5951e0f48) enable batch process 
[TRACE] 2025-06-04 04:17:09.567 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 73fce465-47ee-46f4-abb7-173648d43889) enable batch process 
[TRACE] 2025-06-04 04:17:09.701 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] running status set to false 
[TRACE] 2025-06-04 04:17:09.703 - [TAP-6799(100)][FDM_product] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010629608 
[TRACE] 2025-06-04 04:17:09.703 - [TAP-6799(100)][FDM_product] - PDK connector node released: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010629608 
[TRACE] 2025-06-04 04:17:09.703 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] schema data cleaned 
[TRACE] 2025-06-04 04:17:09.703 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] monitor closed 
[TRACE] 2025-06-04 04:17:09.703 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] close complete, cost 2 ms 
[TRACE] 2025-06-04 04:17:09.904 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] running status set to false 
[INFO ] 2025-06-04 04:17:09.905 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-218b0196-b807-4dd3-8d5d-bc4dd8db5f1a 
[INFO ] 2025-06-04 04:17:09.905 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-218b0196-b807-4dd3-8d5d-bc4dd8db5f1a 
[INFO ] 2025-06-04 04:17:09.905 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:17:09.906 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] schema data cleaned 
[TRACE] 2025-06-04 04:17:09.906 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] monitor closed 
[TRACE] 2025-06-04 04:17:09.906 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] close complete, cost 9 ms 
[TRACE] 2025-06-04 04:17:10.101 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] running status set to false 
[INFO ] 2025-06-04 04:17:10.103 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-e6165862-8df8-43b9-b0fa-6dfbc4d1a1c3 
[INFO ] 2025-06-04 04:17:10.104 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-e6165862-8df8-43b9-b0fa-6dfbc4d1a1c3 
[INFO ] 2025-06-04 04:17:10.104 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-1674c3ea-16fc-4674-9a27-0cd5951e0f48-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:17:10.105 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] schema data cleaned 
[TRACE] 2025-06-04 04:17:10.105 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] monitor closed 
[TRACE] 2025-06-04 04:17:10.105 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] close complete, cost 3 ms 
[TRACE] 2025-06-04 04:17:10.107 - [TAP-6799(100)][1cc89333-71b2-4ffb-bea3-1bbc988a71ad] - Node 1cc89333-71b2-4ffb-bea3-1bbc988a71ad[1cc89333-71b2-4ffb-bea3-1bbc988a71ad] running status set to false 
[TRACE] 2025-06-04 04:17:10.108 - [TAP-6799(100)][1cc89333-71b2-4ffb-bea3-1bbc988a71ad] - Node 1cc89333-71b2-4ffb-bea3-1bbc988a71ad[1cc89333-71b2-4ffb-bea3-1bbc988a71ad] schema data cleaned 
[TRACE] 2025-06-04 04:17:10.108 - [TAP-6799(100)][1cc89333-71b2-4ffb-bea3-1bbc988a71ad] - Node 1cc89333-71b2-4ffb-bea3-1bbc988a71ad[1cc89333-71b2-4ffb-bea3-1bbc988a71ad] monitor closed 
[TRACE] 2025-06-04 04:17:10.110 - [TAP-6799(100)][1cc89333-71b2-4ffb-bea3-1bbc988a71ad] - Node 1cc89333-71b2-4ffb-bea3-1bbc988a71ad[1cc89333-71b2-4ffb-bea3-1bbc988a71ad] close complete, cost 1 ms 
[TRACE] 2025-06-04 04:17:10.110 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 04:17:10.110 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 04:17:10.111 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 04:17:10.111 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 04:19:50.494 - [TAP-6799(100)][08366e4c-72c0-4dc8-a43e-d112a297f6be] - Node 08366e4c-72c0-4dc8-a43e-d112a297f6be[08366e4c-72c0-4dc8-a43e-d112a297f6be] start preload schema,table counts: 0 
[TRACE] 2025-06-04 04:19:50.494 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:19:50.494 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:19:50.495 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:19:50.495 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:50.495 - [TAP-6799(100)][08366e4c-72c0-4dc8-a43e-d112a297f6be] - Node 08366e4c-72c0-4dc8-a43e-d112a297f6be[08366e4c-72c0-4dc8-a43e-d112a297f6be] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:50.495 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:50.495 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:50.495 - [TAP-6799(100)][MDM_packages] - Node js_processor(MDM_packages: 8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d) enable batch process 
[TRACE] 2025-06-04 04:19:50.696 - [TAP-6799(100)][MDM_packages] - Node field_add_del_processor(MDM_packages: 3080ce1e-1a69-43e1-8283-6bfbdbad03d9) enable batch process 
[TRACE] 2025-06-04 04:19:50.701 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:19:50.701 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:19:50.701 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:50.701 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:19:50.701 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:50.701 - [TAP-6799(100)][b7931ff8-dd16-4f92-b3b4-0ba306e310ab] - Node b7931ff8-dd16-4f92-b3b4-0ba306e310ab[b7931ff8-dd16-4f92-b3b4-0ba306e310ab] start preload schema,table counts: 0 
[TRACE] 2025-06-04 04:19:50.701 - [TAP-6799(100)][b7931ff8-dd16-4f92-b3b4-0ba306e310ab] - Node b7931ff8-dd16-4f92-b3b4-0ba306e310ab[b7931ff8-dd16-4f92-b3b4-0ba306e310ab] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:50.701 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:50.701 - [TAP-6799(100)][MDM_packages] - Node field_add_del_processor(MDM_packages: 3080ce1e-1a69-43e1-8283-6bfbdbad03d9) enable batch process 
[TRACE] 2025-06-04 04:19:50.822 - [TAP-6799(100)][MDM_packages] - Node js_processor(MDM_packages: 8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d) enable batch process 
[TRACE] 2025-06-04 04:19:50.822 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] running status set to false 
[TRACE] 2025-06-04 04:19:50.823 - [TAP-6799(100)][MDM_packages] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749010790714 
[TRACE] 2025-06-04 04:19:50.823 - [TAP-6799(100)][MDM_packages] - PDK connector node released: HazelcastSampleSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749010790714 
[TRACE] 2025-06-04 04:19:50.823 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] schema data cleaned 
[TRACE] 2025-06-04 04:19:50.823 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] monitor closed 
[TRACE] 2025-06-04 04:19:50.823 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] close complete, cost 1 ms 
[TRACE] 2025-06-04 04:19:50.823 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] running status set to false 
[TRACE] 2025-06-04 04:19:50.842 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] schema data cleaned 
[TRACE] 2025-06-04 04:19:50.842 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] monitor closed 
[TRACE] 2025-06-04 04:19:50.842 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] close complete, cost 19 ms 
[TRACE] 2025-06-04 04:19:50.883 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] running status set to false 
[TRACE] 2025-06-04 04:19:50.883 - [TAP-6799(100)][MDM_packages] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749010790727 
[TRACE] 2025-06-04 04:19:50.883 - [TAP-6799(100)][MDM_packages] - PDK connector node released: HazelcastSampleSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749010790727 
[TRACE] 2025-06-04 04:19:50.884 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] schema data cleaned 
[TRACE] 2025-06-04 04:19:50.884 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] monitor closed 
[TRACE] 2025-06-04 04:19:50.888 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] close complete, cost 1 ms 
[TRACE] 2025-06-04 04:19:50.888 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] running status set to false 
[TRACE] 2025-06-04 04:19:50.907 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] schema data cleaned 
[TRACE] 2025-06-04 04:19:50.907 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] monitor closed 
[TRACE] 2025-06-04 04:19:50.907 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] close complete, cost 19 ms 
[TRACE] 2025-06-04 04:19:50.984 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] running status set to false 
[INFO ] 2025-06-04 04:19:50.984 - [TAP-6799(100)][MDM_packages][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-15197d25-1d29-4395-8d82-e04152c17881 
[INFO ] 2025-06-04 04:19:50.984 - [TAP-6799(100)][MDM_packages][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-15197d25-1d29-4395-8d82-e04152c17881 
[INFO ] 2025-06-04 04:19:50.984 - [TAP-6799(100)][MDM_packages][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:19:50.984 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] schema data cleaned 
[TRACE] 2025-06-04 04:19:50.985 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] monitor closed 
[TRACE] 2025-06-04 04:19:50.985 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] close complete, cost 2 ms 
[TRACE] 2025-06-04 04:19:50.997 - [TAP-6799(100)][08366e4c-72c0-4dc8-a43e-d112a297f6be] - Node 08366e4c-72c0-4dc8-a43e-d112a297f6be[08366e4c-72c0-4dc8-a43e-d112a297f6be] running status set to false 
[TRACE] 2025-06-04 04:19:50.997 - [TAP-6799(100)][08366e4c-72c0-4dc8-a43e-d112a297f6be] - Node 08366e4c-72c0-4dc8-a43e-d112a297f6be[08366e4c-72c0-4dc8-a43e-d112a297f6be] schema data cleaned 
[TRACE] 2025-06-04 04:19:50.997 - [TAP-6799(100)][08366e4c-72c0-4dc8-a43e-d112a297f6be] - Node 08366e4c-72c0-4dc8-a43e-d112a297f6be[08366e4c-72c0-4dc8-a43e-d112a297f6be] monitor closed 
[TRACE] 2025-06-04 04:19:50.998 - [TAP-6799(100)][08366e4c-72c0-4dc8-a43e-d112a297f6be] - Node 08366e4c-72c0-4dc8-a43e-d112a297f6be[08366e4c-72c0-4dc8-a43e-d112a297f6be] close complete, cost 1 ms 
[TRACE] 2025-06-04 04:19:50.999 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 04:19:50.999 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 04:19:50.999 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 04:19:50.999 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 04:19:51.043 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] running status set to false 
[INFO ] 2025-06-04 04:19:51.043 - [TAP-6799(100)][MDM_packages][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-f000af50-49a4-4161-bcef-23633817323d 
[INFO ] 2025-06-04 04:19:51.044 - [TAP-6799(100)][MDM_packages][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-f000af50-49a4-4161-bcef-23633817323d 
[INFO ] 2025-06-04 04:19:51.044 - [TAP-6799(100)][MDM_packages][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:19:51.044 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] schema data cleaned 
[TRACE] 2025-06-04 04:19:51.044 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] monitor closed 
[TRACE] 2025-06-04 04:19:51.055 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] close complete, cost 1 ms 
[TRACE] 2025-06-04 04:19:51.055 - [TAP-6799(100)][b7931ff8-dd16-4f92-b3b4-0ba306e310ab] - Node b7931ff8-dd16-4f92-b3b4-0ba306e310ab[b7931ff8-dd16-4f92-b3b4-0ba306e310ab] running status set to false 
[TRACE] 2025-06-04 04:19:51.055 - [TAP-6799(100)][b7931ff8-dd16-4f92-b3b4-0ba306e310ab] - Node b7931ff8-dd16-4f92-b3b4-0ba306e310ab[b7931ff8-dd16-4f92-b3b4-0ba306e310ab] schema data cleaned 
[TRACE] 2025-06-04 04:19:51.055 - [TAP-6799(100)][b7931ff8-dd16-4f92-b3b4-0ba306e310ab] - Node b7931ff8-dd16-4f92-b3b4-0ba306e310ab[b7931ff8-dd16-4f92-b3b4-0ba306e310ab] monitor closed 
[TRACE] 2025-06-04 04:19:51.056 - [TAP-6799(100)][b7931ff8-dd16-4f92-b3b4-0ba306e310ab] - Node b7931ff8-dd16-4f92-b3b4-0ba306e310ab[b7931ff8-dd16-4f92-b3b4-0ba306e310ab] close complete, cost 0 ms 
[TRACE] 2025-06-04 04:19:51.056 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 04:19:51.057 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 04:19:51.057 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 04:19:51.057 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 04:19:51.078 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:19:51.078 - [TAP-6799(100)][19a102d5-e751-4d63-b4cd-a2c2100b6f54] - Node 19a102d5-e751-4d63-b4cd-a2c2100b6f54[19a102d5-e751-4d63-b4cd-a2c2100b6f54] start preload schema,table counts: 0 
[TRACE] 2025-06-04 04:19:51.079 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:19:51.079 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:51.079 - [TAP-6799(100)][19a102d5-e751-4d63-b4cd-a2c2100b6f54] - Node 19a102d5-e751-4d63-b4cd-a2c2100b6f54[19a102d5-e751-4d63-b4cd-a2c2100b6f54] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:51.079 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:51.139 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 73fce465-47ee-46f4-abb7-173648d43889) enable batch process 
[TRACE] 2025-06-04 04:19:51.140 - [TAP-6799(100)][3736b996-751c-4d21-850a-7e51c94d1f6e] - Node 3736b996-751c-4d21-850a-7e51c94d1f6e[3736b996-751c-4d21-850a-7e51c94d1f6e] start preload schema,table counts: 0 
[TRACE] 2025-06-04 04:19:51.141 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:19:51.141 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:19:51.141 - [TAP-6799(100)][3736b996-751c-4d21-850a-7e51c94d1f6e] - Node 3736b996-751c-4d21-850a-7e51c94d1f6e[3736b996-751c-4d21-850a-7e51c94d1f6e] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:51.142 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:51.142 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:51.347 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 73fce465-47ee-46f4-abb7-173648d43889) enable batch process 
[TRACE] 2025-06-04 04:19:51.425 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] running status set to false 
[TRACE] 2025-06-04 04:19:51.425 - [TAP-6799(100)][FDM_product] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010791323 
[TRACE] 2025-06-04 04:19:51.425 - [TAP-6799(100)][FDM_product] - PDK connector node released: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010791323 
[TRACE] 2025-06-04 04:19:51.425 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] schema data cleaned 
[TRACE] 2025-06-04 04:19:51.425 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] monitor closed 
[TRACE] 2025-06-04 04:19:51.481 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] close complete, cost 5 ms 
[TRACE] 2025-06-04 04:19:51.485 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] running status set to false 
[TRACE] 2025-06-04 04:19:51.485 - [TAP-6799(100)][FDM_product] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010791331 
[TRACE] 2025-06-04 04:19:51.485 - [TAP-6799(100)][FDM_product] - PDK connector node released: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010791331 
[TRACE] 2025-06-04 04:19:51.485 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] schema data cleaned 
[TRACE] 2025-06-04 04:19:51.485 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] monitor closed 
[TRACE] 2025-06-04 04:19:51.485 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] close complete, cost 0 ms 
[TRACE] 2025-06-04 04:19:51.585 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] running status set to false 
[TRACE] 2025-06-04 04:19:51.589 - [TAP-6799(100)][3736b996-751c-4d21-850a-7e51c94d1f6e] - Node 3736b996-751c-4d21-850a-7e51c94d1f6e[3736b996-751c-4d21-850a-7e51c94d1f6e] running status set to false 
[TRACE] 2025-06-04 04:19:51.593 - [TAP-6799(100)][3736b996-751c-4d21-850a-7e51c94d1f6e] - Node 3736b996-751c-4d21-850a-7e51c94d1f6e[3736b996-751c-4d21-850a-7e51c94d1f6e] schema data cleaned 
[TRACE] 2025-06-04 04:19:51.593 - [TAP-6799(100)][3736b996-751c-4d21-850a-7e51c94d1f6e] - Node 3736b996-751c-4d21-850a-7e51c94d1f6e[3736b996-751c-4d21-850a-7e51c94d1f6e] monitor closed 
[TRACE] 2025-06-04 04:19:51.593 - [TAP-6799(100)][3736b996-751c-4d21-850a-7e51c94d1f6e] - Node 3736b996-751c-4d21-850a-7e51c94d1f6e[3736b996-751c-4d21-850a-7e51c94d1f6e] close complete, cost 0 ms 
[INFO ] 2025-06-04 04:19:51.593 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-8a94daa5-6483-4278-9ef7-194e0af07300 
[INFO ] 2025-06-04 04:19:51.593 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-8a94daa5-6483-4278-9ef7-194e0af07300 
[INFO ] 2025-06-04 04:19:51.593 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:19:51.593 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] schema data cleaned 
[TRACE] 2025-06-04 04:19:51.593 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] monitor closed 
[TRACE] 2025-06-04 04:19:51.593 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] close complete, cost 4 ms 
[TRACE] 2025-06-04 04:19:51.593 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 04:19:51.593 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 04:19:51.593 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 04:19:51.593 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 04:19:51.656 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] running status set to false 
[INFO ] 2025-06-04 04:19:51.656 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-3fbe314a-c513-47bc-8ebb-5fc56c39f865 
[INFO ] 2025-06-04 04:19:51.657 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-3fbe314a-c513-47bc-8ebb-5fc56c39f865 
[INFO ] 2025-06-04 04:19:51.657 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:19:51.657 - [TAP-6799(100)][19a102d5-e751-4d63-b4cd-a2c2100b6f54] - Node 19a102d5-e751-4d63-b4cd-a2c2100b6f54[19a102d5-e751-4d63-b4cd-a2c2100b6f54] running status set to false 
[TRACE] 2025-06-04 04:19:51.657 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] schema data cleaned 
[TRACE] 2025-06-04 04:19:51.657 - [TAP-6799(100)][19a102d5-e751-4d63-b4cd-a2c2100b6f54] - Node 19a102d5-e751-4d63-b4cd-a2c2100b6f54[19a102d5-e751-4d63-b4cd-a2c2100b6f54] schema data cleaned 
[TRACE] 2025-06-04 04:19:51.657 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] monitor closed 
[TRACE] 2025-06-04 04:19:51.657 - [TAP-6799(100)][19a102d5-e751-4d63-b4cd-a2c2100b6f54] - Node 19a102d5-e751-4d63-b4cd-a2c2100b6f54[19a102d5-e751-4d63-b4cd-a2c2100b6f54] monitor closed 
[TRACE] 2025-06-04 04:19:51.657 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] close complete, cost 2 ms 
[TRACE] 2025-06-04 04:19:51.657 - [TAP-6799(100)][19a102d5-e751-4d63-b4cd-a2c2100b6f54] - Node 19a102d5-e751-4d63-b4cd-a2c2100b6f54[19a102d5-e751-4d63-b4cd-a2c2100b6f54] close complete, cost 0 ms 
[TRACE] 2025-06-04 04:19:51.657 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 04:19:51.657 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 04:19:51.657 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 04:19:51.660 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 04:19:51.660 - [TAP-6799(100)][fe166732-edf5-4461-8454-e981de0be14c] - Node fe166732-edf5-4461-8454-e981de0be14c[fe166732-edf5-4461-8454-e981de0be14c] start preload schema,table counts: 0 
[TRACE] 2025-06-04 04:19:51.660 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:19:51.660 - [TAP-6799(100)][fe166732-edf5-4461-8454-e981de0be14c] - Node fe166732-edf5-4461-8454-e981de0be14c[fe166732-edf5-4461-8454-e981de0be14c] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:51.660 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:19:51.660 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:19:51.660 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:51.660 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:51.660 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:51.740 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 1674c3ea-16fc-4674-9a27-0cd5951e0f48) enable batch process 
[TRACE] 2025-06-04 04:19:51.740 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 73fce465-47ee-46f4-abb7-173648d43889) enable batch process 
[TRACE] 2025-06-04 04:19:51.740 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:19:51.740 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:19:51.740 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:19:51.740 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:51.740 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:51.740 - [TAP-6799(100)][af63537f-21ba-433d-9fc0-eb901b29e142] - Node af63537f-21ba-433d-9fc0-eb901b29e142[af63537f-21ba-433d-9fc0-eb901b29e142] start preload schema,table counts: 0 
[TRACE] 2025-06-04 04:19:51.740 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:51.740 - [TAP-6799(100)][af63537f-21ba-433d-9fc0-eb901b29e142] - Node af63537f-21ba-433d-9fc0-eb901b29e142[af63537f-21ba-433d-9fc0-eb901b29e142] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:51.742 - [TAP-6799(100)][MDM_packages] - Node js_processor(MDM_packages: 8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d) enable batch process 
[TRACE] 2025-06-04 04:19:51.742 - [TAP-6799(100)][MDM_packages] - Node field_add_del_processor(MDM_packages: 3080ce1e-1a69-43e1-8283-6bfbdbad03d9) enable batch process 
[TRACE] 2025-06-04 04:19:51.757 - [TAP-6799(100)][084b95c7-32e9-43a8-aba5-723d2b518bb0] - Node 084b95c7-32e9-43a8-aba5-723d2b518bb0[084b95c7-32e9-43a8-aba5-723d2b518bb0] start preload schema,table counts: 0 
[TRACE] 2025-06-04 04:19:51.757 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:19:51.757 - [TAP-6799(100)][084b95c7-32e9-43a8-aba5-723d2b518bb0] - Node 084b95c7-32e9-43a8-aba5-723d2b518bb0[084b95c7-32e9-43a8-aba5-723d2b518bb0] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:51.757 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:51.757 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:19:51.757 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:51.757 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:19:51.757 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:51.757 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 1674c3ea-16fc-4674-9a27-0cd5951e0f48) enable batch process 
[TRACE] 2025-06-04 04:19:51.785 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 73fce465-47ee-46f4-abb7-173648d43889) enable batch process 
[TRACE] 2025-06-04 04:19:51.786 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] running status set to false 
[TRACE] 2025-06-04 04:19:51.787 - [TAP-6799(100)][FDM_product] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010791695 
[TRACE] 2025-06-04 04:19:51.787 - [TAP-6799(100)][FDM_product] - PDK connector node released: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010791695 
[TRACE] 2025-06-04 04:19:51.787 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] schema data cleaned 
[TRACE] 2025-06-04 04:19:51.787 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] monitor closed 
[TRACE] 2025-06-04 04:19:51.787 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] close complete, cost 1 ms 
[TRACE] 2025-06-04 04:19:51.874 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] running status set to false 
[TRACE] 2025-06-04 04:19:51.874 - [TAP-6799(100)][MDM_packages] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749010791775 
[TRACE] 2025-06-04 04:19:51.874 - [TAP-6799(100)][MDM_packages] - PDK connector node released: HazelcastSampleSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749010791775 
[TRACE] 2025-06-04 04:19:51.874 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] schema data cleaned 
[TRACE] 2025-06-04 04:19:51.874 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] monitor closed 
[TRACE] 2025-06-04 04:19:51.874 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] close complete, cost 1 ms 
[TRACE] 2025-06-04 04:19:51.896 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] running status set to false 
[TRACE] 2025-06-04 04:19:51.896 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] schema data cleaned 
[TRACE] 2025-06-04 04:19:51.896 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] monitor closed 
[TRACE] 2025-06-04 04:19:51.896 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] close complete, cost 18 ms 
[TRACE] 2025-06-04 04:19:51.966 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] running status set to false 
[TRACE] 2025-06-04 04:19:51.967 - [TAP-6799(100)][FDM_product] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010791775 
[TRACE] 2025-06-04 04:19:51.967 - [TAP-6799(100)][FDM_product] - PDK connector node released: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010791775 
[TRACE] 2025-06-04 04:19:51.967 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] schema data cleaned 
[TRACE] 2025-06-04 04:19:51.967 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] monitor closed 
[TRACE] 2025-06-04 04:19:51.967 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] close complete, cost 1 ms 
[TRACE] 2025-06-04 04:19:51.974 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] running status set to false 
[INFO ] 2025-06-04 04:19:51.974 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-4d5c5c94-82d9-4b36-85ff-7f53c35739f7 
[INFO ] 2025-06-04 04:19:51.974 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-4d5c5c94-82d9-4b36-85ff-7f53c35739f7 
[INFO ] 2025-06-04 04:19:51.974 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:19:51.975 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] schema data cleaned 
[TRACE] 2025-06-04 04:19:51.975 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] monitor closed 
[TRACE] 2025-06-04 04:19:52.126 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] close complete, cost 3 ms 
[TRACE] 2025-06-04 04:19:52.127 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] running status set to false 
[INFO ] 2025-06-04 04:19:52.132 - [TAP-6799(100)][MDM_packages][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-b50cfb89-4d4d-4136-86a3-7ad38670c24a 
[INFO ] 2025-06-04 04:19:52.132 - [TAP-6799(100)][MDM_packages][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-b50cfb89-4d4d-4136-86a3-7ad38670c24a 
[INFO ] 2025-06-04 04:19:52.141 - [TAP-6799(100)][MDM_packages][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:19:52.142 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] schema data cleaned 
[TRACE] 2025-06-04 04:19:52.142 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] monitor closed 
[TRACE] 2025-06-04 04:19:52.143 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] close complete, cost 18 ms 
[TRACE] 2025-06-04 04:19:52.190 - [TAP-6799(100)][af63537f-21ba-433d-9fc0-eb901b29e142] - Node af63537f-21ba-433d-9fc0-eb901b29e142[af63537f-21ba-433d-9fc0-eb901b29e142] running status set to false 
[TRACE] 2025-06-04 04:19:52.190 - [TAP-6799(100)][af63537f-21ba-433d-9fc0-eb901b29e142] - Node af63537f-21ba-433d-9fc0-eb901b29e142[af63537f-21ba-433d-9fc0-eb901b29e142] schema data cleaned 
[TRACE] 2025-06-04 04:19:52.193 - [TAP-6799(100)][af63537f-21ba-433d-9fc0-eb901b29e142] - Node af63537f-21ba-433d-9fc0-eb901b29e142[af63537f-21ba-433d-9fc0-eb901b29e142] monitor closed 
[TRACE] 2025-06-04 04:19:52.194 - [TAP-6799(100)][af63537f-21ba-433d-9fc0-eb901b29e142] - Node af63537f-21ba-433d-9fc0-eb901b29e142[af63537f-21ba-433d-9fc0-eb901b29e142] close complete, cost 5 ms 
[TRACE] 2025-06-04 04:19:52.196 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 04:19:52.196 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 04:19:52.197 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 04:19:52.245 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 04:19:52.246 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] running status set to false 
[INFO ] 2025-06-04 04:19:52.251 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-a102bdda-7350-403f-87e9-0a1e6fcc467a 
[INFO ] 2025-06-04 04:19:52.252 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-a102bdda-7350-403f-87e9-0a1e6fcc467a 
[INFO ] 2025-06-04 04:19:52.252 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:19:52.253 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] schema data cleaned 
[TRACE] 2025-06-04 04:19:52.253 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] monitor closed 
[TRACE] 2025-06-04 04:19:52.254 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] close complete, cost 9 ms 
[TRACE] 2025-06-04 04:19:52.336 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] running status set to false 
[TRACE] 2025-06-04 04:19:52.339 - [TAP-6799(100)][fe166732-edf5-4461-8454-e981de0be14c] - Node fe166732-edf5-4461-8454-e981de0be14c[fe166732-edf5-4461-8454-e981de0be14c] running status set to false 
[TRACE] 2025-06-04 04:19:52.339 - [TAP-6799(100)][fe166732-edf5-4461-8454-e981de0be14c] - Node fe166732-edf5-4461-8454-e981de0be14c[fe166732-edf5-4461-8454-e981de0be14c] schema data cleaned 
[TRACE] 2025-06-04 04:19:52.339 - [TAP-6799(100)][fe166732-edf5-4461-8454-e981de0be14c] - Node fe166732-edf5-4461-8454-e981de0be14c[fe166732-edf5-4461-8454-e981de0be14c] monitor closed 
[TRACE] 2025-06-04 04:19:52.339 - [TAP-6799(100)][fe166732-edf5-4461-8454-e981de0be14c] - Node fe166732-edf5-4461-8454-e981de0be14c[fe166732-edf5-4461-8454-e981de0be14c] close complete, cost 1 ms 
[INFO ] 2025-06-04 04:19:52.340 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-0c4928f1-16eb-4096-b178-fe85e89981a4 
[INFO ] 2025-06-04 04:19:52.341 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-0c4928f1-16eb-4096-b178-fe85e89981a4 
[INFO ] 2025-06-04 04:19:52.341 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-1674c3ea-16fc-4674-9a27-0cd5951e0f48-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:19:52.344 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] schema data cleaned 
[TRACE] 2025-06-04 04:19:52.344 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] monitor closed 
[TRACE] 2025-06-04 04:19:52.345 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] close complete, cost 8 ms 
[TRACE] 2025-06-04 04:19:52.347 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 04:19:52.348 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 04:19:52.348 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 04:19:52.348 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 04:19:52.384 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:19:52.385 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:19:52.385 - [TAP-6799(100)][049010a0-e7ed-445d-87de-100a17d0dd41] - Node 049010a0-e7ed-445d-87de-100a17d0dd41[049010a0-e7ed-445d-87de-100a17d0dd41] start preload schema,table counts: 0 
[TRACE] 2025-06-04 04:19:52.385 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:52.385 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:52.385 - [TAP-6799(100)][049010a0-e7ed-445d-87de-100a17d0dd41] - Node 049010a0-e7ed-445d-87de-100a17d0dd41[049010a0-e7ed-445d-87de-100a17d0dd41] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:52.386 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 73fce465-47ee-46f4-abb7-173648d43889) enable batch process 
[TRACE] 2025-06-04 04:19:52.445 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] running status set to false 
[INFO ] 2025-06-04 04:19:52.445 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-49f81d3a-d2e1-4a92-8e81-74964df8de96 
[INFO ] 2025-06-04 04:19:52.447 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-49f81d3a-d2e1-4a92-8e81-74964df8de96 
[INFO ] 2025-06-04 04:19:52.447 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-1674c3ea-16fc-4674-9a27-0cd5951e0f48-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:19:52.450 - [TAP-6799(100)][084b95c7-32e9-43a8-aba5-723d2b518bb0] - Node 084b95c7-32e9-43a8-aba5-723d2b518bb0[084b95c7-32e9-43a8-aba5-723d2b518bb0] running status set to false 
[TRACE] 2025-06-04 04:19:52.450 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] schema data cleaned 
[TRACE] 2025-06-04 04:19:52.451 - [TAP-6799(100)][084b95c7-32e9-43a8-aba5-723d2b518bb0] - Node 084b95c7-32e9-43a8-aba5-723d2b518bb0[084b95c7-32e9-43a8-aba5-723d2b518bb0] schema data cleaned 
[TRACE] 2025-06-04 04:19:52.451 - [TAP-6799(100)][084b95c7-32e9-43a8-aba5-723d2b518bb0] - Node 084b95c7-32e9-43a8-aba5-723d2b518bb0[084b95c7-32e9-43a8-aba5-723d2b518bb0] monitor closed 
[TRACE] 2025-06-04 04:19:52.451 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] monitor closed 
[TRACE] 2025-06-04 04:19:52.452 - [TAP-6799(100)][084b95c7-32e9-43a8-aba5-723d2b518bb0] - Node 084b95c7-32e9-43a8-aba5-723d2b518bb0[084b95c7-32e9-43a8-aba5-723d2b518bb0] close complete, cost 2 ms 
[TRACE] 2025-06-04 04:19:52.452 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] close complete, cost 7 ms 
[TRACE] 2025-06-04 04:19:52.454 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 04:19:52.455 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 04:19:52.455 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 04:19:52.525 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 04:19:52.526 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] running status set to false 
[TRACE] 2025-06-04 04:19:52.529 - [TAP-6799(100)][FDM_product] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010792423 
[TRACE] 2025-06-04 04:19:52.529 - [TAP-6799(100)][FDM_product] - PDK connector node released: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010792423 
[TRACE] 2025-06-04 04:19:52.529 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] schema data cleaned 
[TRACE] 2025-06-04 04:19:52.529 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] monitor closed 
[TRACE] 2025-06-04 04:19:52.530 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] close complete, cost 5 ms 
[TRACE] 2025-06-04 04:19:52.896 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] running status set to false 
[TRACE] 2025-06-04 04:19:52.898 - [TAP-6799(100)][049010a0-e7ed-445d-87de-100a17d0dd41] - Node 049010a0-e7ed-445d-87de-100a17d0dd41[049010a0-e7ed-445d-87de-100a17d0dd41] running status set to false 
[TRACE] 2025-06-04 04:19:52.899 - [TAP-6799(100)][049010a0-e7ed-445d-87de-100a17d0dd41] - Node 049010a0-e7ed-445d-87de-100a17d0dd41[049010a0-e7ed-445d-87de-100a17d0dd41] schema data cleaned 
[TRACE] 2025-06-04 04:19:52.899 - [TAP-6799(100)][049010a0-e7ed-445d-87de-100a17d0dd41] - Node 049010a0-e7ed-445d-87de-100a17d0dd41[049010a0-e7ed-445d-87de-100a17d0dd41] monitor closed 
[INFO ] 2025-06-04 04:19:52.899 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-3305b937-73e7-46a5-a013-d5b2d27b8687 
[TRACE] 2025-06-04 04:19:52.899 - [TAP-6799(100)][049010a0-e7ed-445d-87de-100a17d0dd41] - Node 049010a0-e7ed-445d-87de-100a17d0dd41[049010a0-e7ed-445d-87de-100a17d0dd41] close complete, cost 1 ms 
[INFO ] 2025-06-04 04:19:52.900 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-3305b937-73e7-46a5-a013-d5b2d27b8687 
[INFO ] 2025-06-04 04:19:52.900 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:19:52.901 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] schema data cleaned 
[TRACE] 2025-06-04 04:19:52.901 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] monitor closed 
[TRACE] 2025-06-04 04:19:52.901 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] close complete, cost 5 ms 
[TRACE] 2025-06-04 04:19:52.902 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 04:19:52.902 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 04:19:52.903 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 04:19:52.991 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 04:19:52.992 - [TAP-6799(100)][f486eda8-a2bb-47af-9b31-06c95bd3d62a] - Node f486eda8-a2bb-47af-9b31-06c95bd3d62a[f486eda8-a2bb-47af-9b31-06c95bd3d62a] start preload schema,table counts: 0 
[TRACE] 2025-06-04 04:19:52.992 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:19:52.992 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:19:52.992 - [TAP-6799(100)][f486eda8-a2bb-47af-9b31-06c95bd3d62a] - Node f486eda8-a2bb-47af-9b31-06c95bd3d62a[f486eda8-a2bb-47af-9b31-06c95bd3d62a] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:52.992 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:52.992 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:52.992 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:19:52.993 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 1674c3ea-16fc-4674-9a27-0cd5951e0f48) enable batch process 
[TRACE] 2025-06-04 04:19:52.993 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:52.993 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 73fce465-47ee-46f4-abb7-173648d43889) enable batch process 
[TRACE] 2025-06-04 04:19:53.142 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] running status set to false 
[TRACE] 2025-06-04 04:19:53.145 - [TAP-6799(100)][FDM_product] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010793048 
[TRACE] 2025-06-04 04:19:53.145 - [TAP-6799(100)][FDM_product] - PDK connector node released: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010793048 
[TRACE] 2025-06-04 04:19:53.145 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] schema data cleaned 
[TRACE] 2025-06-04 04:19:53.146 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] monitor closed 
[TRACE] 2025-06-04 04:19:53.146 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] close complete, cost 4 ms 
[TRACE] 2025-06-04 04:19:53.351 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] running status set to false 
[INFO ] 2025-06-04 04:19:53.354 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-16b89efb-8b87-452e-b6b8-746e84859c65 
[INFO ] 2025-06-04 04:19:53.354 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-16b89efb-8b87-452e-b6b8-746e84859c65 
[INFO ] 2025-06-04 04:19:53.355 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:19:53.356 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] schema data cleaned 
[TRACE] 2025-06-04 04:19:53.356 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] monitor closed 
[TRACE] 2025-06-04 04:19:53.356 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] close complete, cost 5 ms 
[TRACE] 2025-06-04 04:19:53.549 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] running status set to false 
[INFO ] 2025-06-04 04:19:53.551 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-75da6c48-e2c0-41cc-b7e9-0a3f6ab88104 
[INFO ] 2025-06-04 04:19:53.551 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-75da6c48-e2c0-41cc-b7e9-0a3f6ab88104 
[INFO ] 2025-06-04 04:19:53.551 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-1674c3ea-16fc-4674-9a27-0cd5951e0f48-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:19:53.552 - [TAP-6799(100)][f486eda8-a2bb-47af-9b31-06c95bd3d62a] - Node f486eda8-a2bb-47af-9b31-06c95bd3d62a[f486eda8-a2bb-47af-9b31-06c95bd3d62a] running status set to false 
[TRACE] 2025-06-04 04:19:53.552 - [TAP-6799(100)][f486eda8-a2bb-47af-9b31-06c95bd3d62a] - Node f486eda8-a2bb-47af-9b31-06c95bd3d62a[f486eda8-a2bb-47af-9b31-06c95bd3d62a] schema data cleaned 
[TRACE] 2025-06-04 04:19:53.552 - [TAP-6799(100)][f486eda8-a2bb-47af-9b31-06c95bd3d62a] - Node f486eda8-a2bb-47af-9b31-06c95bd3d62a[f486eda8-a2bb-47af-9b31-06c95bd3d62a] monitor closed 
[TRACE] 2025-06-04 04:19:53.552 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] schema data cleaned 
[TRACE] 2025-06-04 04:19:53.552 - [TAP-6799(100)][f486eda8-a2bb-47af-9b31-06c95bd3d62a] - Node f486eda8-a2bb-47af-9b31-06c95bd3d62a[f486eda8-a2bb-47af-9b31-06c95bd3d62a] close complete, cost 0 ms 
[TRACE] 2025-06-04 04:19:53.553 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] monitor closed 
[TRACE] 2025-06-04 04:19:53.553 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] close complete, cost 4 ms 
[TRACE] 2025-06-04 04:19:53.554 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 04:19:53.554 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 04:19:53.554 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 04:19:53.759 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 04:19:56.075 - [TAP-6799(100)][07339ff6-6769-465e-934c-5b93541b5b04] - Node 07339ff6-6769-465e-934c-5b93541b5b04[07339ff6-6769-465e-934c-5b93541b5b04] start preload schema,table counts: 0 
[TRACE] 2025-06-04 04:19:56.077 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:19:56.077 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:19:56.078 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:56.078 - [TAP-6799(100)][07339ff6-6769-465e-934c-5b93541b5b04] - Node 07339ff6-6769-465e-934c-5b93541b5b04[07339ff6-6769-465e-934c-5b93541b5b04] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:56.078 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:19:56.078 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:56.078 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:56.078 - [TAP-6799(100)][MDM_packages] - Node field_add_del_processor(MDM_packages: 3080ce1e-1a69-43e1-8283-6bfbdbad03d9) enable batch process 
[TRACE] 2025-06-04 04:19:56.079 - [TAP-6799(100)][MDM_packages] - Node js_processor(MDM_packages: 8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d) enable batch process 
[TRACE] 2025-06-04 04:19:56.210 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] running status set to false 
[TRACE] 2025-06-04 04:19:56.210 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] running status set to false 
[TRACE] 2025-06-04 04:19:56.211 - [TAP-6799(100)][MDM_packages] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749010796106 
[TRACE] 2025-06-04 04:19:56.211 - [TAP-6799(100)][MDM_packages] - PDK connector node released: HazelcastSampleSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749010796106 
[TRACE] 2025-06-04 04:19:56.211 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] schema data cleaned 
[TRACE] 2025-06-04 04:19:56.212 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] monitor closed 
[TRACE] 2025-06-04 04:19:56.244 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] close complete, cost 2 ms 
[TRACE] 2025-06-04 04:19:56.244 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] schema data cleaned 
[TRACE] 2025-06-04 04:19:56.244 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] monitor closed 
[TRACE] 2025-06-04 04:19:56.244 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] close complete, cost 34 ms 
[TRACE] 2025-06-04 04:19:56.378 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] running status set to false 
[INFO ] 2025-06-04 04:19:56.378 - [TAP-6799(100)][MDM_packages][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-ecb32f97-c6a5-40aa-9eef-ae5b14a28b36 
[INFO ] 2025-06-04 04:19:56.378 - [TAP-6799(100)][MDM_packages][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-ecb32f97-c6a5-40aa-9eef-ae5b14a28b36 
[INFO ] 2025-06-04 04:19:56.378 - [TAP-6799(100)][MDM_packages][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:19:56.380 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] schema data cleaned 
[TRACE] 2025-06-04 04:19:56.380 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] monitor closed 
[TRACE] 2025-06-04 04:19:56.381 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] close complete, cost 3 ms 
[TRACE] 2025-06-04 04:19:56.392 - [TAP-6799(100)][07339ff6-6769-465e-934c-5b93541b5b04] - Node 07339ff6-6769-465e-934c-5b93541b5b04[07339ff6-6769-465e-934c-5b93541b5b04] running status set to false 
[TRACE] 2025-06-04 04:19:56.392 - [TAP-6799(100)][07339ff6-6769-465e-934c-5b93541b5b04] - Node 07339ff6-6769-465e-934c-5b93541b5b04[07339ff6-6769-465e-934c-5b93541b5b04] schema data cleaned 
[TRACE] 2025-06-04 04:19:56.392 - [TAP-6799(100)][07339ff6-6769-465e-934c-5b93541b5b04] - Node 07339ff6-6769-465e-934c-5b93541b5b04[07339ff6-6769-465e-934c-5b93541b5b04] monitor closed 
[TRACE] 2025-06-04 04:19:56.392 - [TAP-6799(100)][07339ff6-6769-465e-934c-5b93541b5b04] - Node 07339ff6-6769-465e-934c-5b93541b5b04[07339ff6-6769-465e-934c-5b93541b5b04] close complete, cost 0 ms 
[TRACE] 2025-06-04 04:19:56.393 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 04:19:56.393 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 04:19:56.393 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 04:19:56.458 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 04:19:56.458 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:19:56.459 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:19:56.459 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:56.459 - [TAP-6799(100)][ac798dea-55f6-443d-a7ed-1b4e779441b2] - Node ac798dea-55f6-443d-a7ed-1b4e779441b2[ac798dea-55f6-443d-a7ed-1b4e779441b2] start preload schema,table counts: 0 
[TRACE] 2025-06-04 04:19:56.459 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:56.459 - [TAP-6799(100)][ac798dea-55f6-443d-a7ed-1b4e779441b2] - Node ac798dea-55f6-443d-a7ed-1b4e779441b2[ac798dea-55f6-443d-a7ed-1b4e779441b2] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:56.613 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 73fce465-47ee-46f4-abb7-173648d43889) enable batch process 
[TRACE] 2025-06-04 04:19:56.613 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] running status set to false 
[TRACE] 2025-06-04 04:19:56.616 - [TAP-6799(100)][FDM_product] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010796500 
[TRACE] 2025-06-04 04:19:56.616 - [TAP-6799(100)][FDM_product] - PDK connector node released: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010796500 
[TRACE] 2025-06-04 04:19:56.617 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] schema data cleaned 
[TRACE] 2025-06-04 04:19:56.617 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] monitor closed 
[TRACE] 2025-06-04 04:19:56.822 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] close complete, cost 4 ms 
[TRACE] 2025-06-04 04:19:56.868 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] running status set to false 
[INFO ] 2025-06-04 04:19:56.869 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-9e981c57-cf48-4e04-b31c-a81ac1bc4c75 
[INFO ] 2025-06-04 04:19:56.869 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-9e981c57-cf48-4e04-b31c-a81ac1bc4c75 
[INFO ] 2025-06-04 04:19:56.869 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:19:56.870 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] schema data cleaned 
[TRACE] 2025-06-04 04:19:56.870 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] monitor closed 
[TRACE] 2025-06-04 04:19:56.870 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] close complete, cost 3 ms 
[TRACE] 2025-06-04 04:19:56.871 - [TAP-6799(100)][ac798dea-55f6-443d-a7ed-1b4e779441b2] - Node ac798dea-55f6-443d-a7ed-1b4e779441b2[ac798dea-55f6-443d-a7ed-1b4e779441b2] running status set to false 
[TRACE] 2025-06-04 04:19:56.871 - [TAP-6799(100)][ac798dea-55f6-443d-a7ed-1b4e779441b2] - Node ac798dea-55f6-443d-a7ed-1b4e779441b2[ac798dea-55f6-443d-a7ed-1b4e779441b2] schema data cleaned 
[TRACE] 2025-06-04 04:19:56.871 - [TAP-6799(100)][ac798dea-55f6-443d-a7ed-1b4e779441b2] - Node ac798dea-55f6-443d-a7ed-1b4e779441b2[ac798dea-55f6-443d-a7ed-1b4e779441b2] monitor closed 
[TRACE] 2025-06-04 04:19:56.871 - [TAP-6799(100)][ac798dea-55f6-443d-a7ed-1b4e779441b2] - Node ac798dea-55f6-443d-a7ed-1b4e779441b2[ac798dea-55f6-443d-a7ed-1b4e779441b2] close complete, cost 0 ms 
[TRACE] 2025-06-04 04:19:56.872 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 04:19:56.872 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 04:19:56.872 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 04:19:56.872 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 04:19:56.950 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:19:56.950 - [TAP-6799(100)][1cb76505-90ee-4802-b7fc-709d97a6a5ba] - Node 1cb76505-90ee-4802-b7fc-709d97a6a5ba[1cb76505-90ee-4802-b7fc-709d97a6a5ba] start preload schema,table counts: 0 
[TRACE] 2025-06-04 04:19:56.950 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:19:56.950 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:56.950 - [TAP-6799(100)][1cb76505-90ee-4802-b7fc-709d97a6a5ba] - Node 1cb76505-90ee-4802-b7fc-709d97a6a5ba[1cb76505-90ee-4802-b7fc-709d97a6a5ba] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:56.950 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:19:56.950 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:56.951 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 1674c3ea-16fc-4674-9a27-0cd5951e0f48) enable batch process 
[TRACE] 2025-06-04 04:19:56.951 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:57.119 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 73fce465-47ee-46f4-abb7-173648d43889) enable batch process 
[TRACE] 2025-06-04 04:19:57.119 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] running status set to false 
[TRACE] 2025-06-04 04:19:57.120 - [TAP-6799(100)][FDM_product] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010796999 
[TRACE] 2025-06-04 04:19:57.121 - [TAP-6799(100)][FDM_product] - PDK connector node released: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010796999 
[TRACE] 2025-06-04 04:19:57.121 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] schema data cleaned 
[TRACE] 2025-06-04 04:19:57.121 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] monitor closed 
[TRACE] 2025-06-04 04:19:57.307 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] close complete, cost 2 ms 
[TRACE] 2025-06-04 04:19:57.307 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] running status set to false 
[INFO ] 2025-06-04 04:19:57.309 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-27f921f2-5cce-4b53-bedc-f22f9e4fd736 
[INFO ] 2025-06-04 04:19:57.309 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-27f921f2-5cce-4b53-bedc-f22f9e4fd736 
[INFO ] 2025-06-04 04:19:57.309 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:19:57.310 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] schema data cleaned 
[TRACE] 2025-06-04 04:19:57.310 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] monitor closed 
[TRACE] 2025-06-04 04:19:57.310 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] close complete, cost 3 ms 
[TRACE] 2025-06-04 04:19:57.473 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] running status set to false 
[INFO ] 2025-06-04 04:19:57.474 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-c27b75f8-4ab1-403f-89ea-6a972073eda6 
[INFO ] 2025-06-04 04:19:57.475 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-c27b75f8-4ab1-403f-89ea-6a972073eda6 
[INFO ] 2025-06-04 04:19:57.475 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-1674c3ea-16fc-4674-9a27-0cd5951e0f48-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:19:57.475 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] schema data cleaned 
[TRACE] 2025-06-04 04:19:57.476 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] monitor closed 
[TRACE] 2025-06-04 04:19:57.476 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] close complete, cost 2 ms 
[TRACE] 2025-06-04 04:19:57.477 - [TAP-6799(100)][1cb76505-90ee-4802-b7fc-709d97a6a5ba] - Node 1cb76505-90ee-4802-b7fc-709d97a6a5ba[1cb76505-90ee-4802-b7fc-709d97a6a5ba] running status set to false 
[TRACE] 2025-06-04 04:19:57.477 - [TAP-6799(100)][1cb76505-90ee-4802-b7fc-709d97a6a5ba] - Node 1cb76505-90ee-4802-b7fc-709d97a6a5ba[1cb76505-90ee-4802-b7fc-709d97a6a5ba] schema data cleaned 
[TRACE] 2025-06-04 04:19:57.477 - [TAP-6799(100)][1cb76505-90ee-4802-b7fc-709d97a6a5ba] - Node 1cb76505-90ee-4802-b7fc-709d97a6a5ba[1cb76505-90ee-4802-b7fc-709d97a6a5ba] monitor closed 
[TRACE] 2025-06-04 04:19:57.477 - [TAP-6799(100)][1cb76505-90ee-4802-b7fc-709d97a6a5ba] - Node 1cb76505-90ee-4802-b7fc-709d97a6a5ba[1cb76505-90ee-4802-b7fc-709d97a6a5ba] close complete, cost 0 ms 
[TRACE] 2025-06-04 04:19:57.478 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 04:19:57.478 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 04:19:57.478 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 04:19:57.478 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 04:20:00.509 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:00.509 - [TAP-6799(100)][8b95dcde-ac55-4ecf-b9eb-2f561c8ec177] - Node 8b95dcde-ac55-4ecf-b9eb-2f561c8ec177[8b95dcde-ac55-4ecf-b9eb-2f561c8ec177] start preload schema,table counts: 0 
[TRACE] 2025-06-04 04:20:00.509 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:00.509 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:00.509 - [TAP-6799(100)][8b95dcde-ac55-4ecf-b9eb-2f561c8ec177] - Node 8b95dcde-ac55-4ecf-b9eb-2f561c8ec177[8b95dcde-ac55-4ecf-b9eb-2f561c8ec177] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:00.509 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:00.510 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:00.510 - [TAP-6799(100)][MDM_packages] - Node js_processor(MDM_packages: 8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d) enable batch process 
[TRACE] 2025-06-04 04:20:00.510 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:00.642 - [TAP-6799(100)][MDM_packages] - Node field_add_del_processor(MDM_packages: 3080ce1e-1a69-43e1-8283-6bfbdbad03d9) enable batch process 
[TRACE] 2025-06-04 04:20:00.642 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] running status set to false 
[TRACE] 2025-06-04 04:20:00.643 - [TAP-6799(100)][MDM_packages] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749010800552 
[TRACE] 2025-06-04 04:20:00.643 - [TAP-6799(100)][MDM_packages] - PDK connector node released: HazelcastSampleSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749010800552 
[TRACE] 2025-06-04 04:20:00.643 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] schema data cleaned 
[TRACE] 2025-06-04 04:20:00.643 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] monitor closed 
[TRACE] 2025-06-04 04:20:00.648 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] close complete, cost 1 ms 
[TRACE] 2025-06-04 04:20:00.648 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] running status set to false 
[TRACE] 2025-06-04 04:20:00.670 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] schema data cleaned 
[TRACE] 2025-06-04 04:20:00.670 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] monitor closed 
[TRACE] 2025-06-04 04:20:00.670 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] close complete, cost 22 ms 
[TRACE] 2025-06-04 04:20:00.820 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] running status set to false 
[INFO ] 2025-06-04 04:20:00.821 - [TAP-6799(100)][MDM_packages][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-0596476a-edab-44fb-a7a4-fda0fc3f770e 
[INFO ] 2025-06-04 04:20:00.821 - [TAP-6799(100)][MDM_packages][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-0596476a-edab-44fb-a7a4-fda0fc3f770e 
[INFO ] 2025-06-04 04:20:00.821 - [TAP-6799(100)][MDM_packages][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:20:00.822 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] schema data cleaned 
[TRACE] 2025-06-04 04:20:00.822 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] monitor closed 
[TRACE] 2025-06-04 04:20:00.830 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] close complete, cost 2 ms 
[TRACE] 2025-06-04 04:20:00.830 - [TAP-6799(100)][8b95dcde-ac55-4ecf-b9eb-2f561c8ec177] - Node 8b95dcde-ac55-4ecf-b9eb-2f561c8ec177[8b95dcde-ac55-4ecf-b9eb-2f561c8ec177] running status set to false 
[TRACE] 2025-06-04 04:20:00.831 - [TAP-6799(100)][8b95dcde-ac55-4ecf-b9eb-2f561c8ec177] - Node 8b95dcde-ac55-4ecf-b9eb-2f561c8ec177[8b95dcde-ac55-4ecf-b9eb-2f561c8ec177] schema data cleaned 
[TRACE] 2025-06-04 04:20:00.831 - [TAP-6799(100)][8b95dcde-ac55-4ecf-b9eb-2f561c8ec177] - Node 8b95dcde-ac55-4ecf-b9eb-2f561c8ec177[8b95dcde-ac55-4ecf-b9eb-2f561c8ec177] monitor closed 
[TRACE] 2025-06-04 04:20:00.831 - [TAP-6799(100)][8b95dcde-ac55-4ecf-b9eb-2f561c8ec177] - Node 8b95dcde-ac55-4ecf-b9eb-2f561c8ec177[8b95dcde-ac55-4ecf-b9eb-2f561c8ec177] close complete, cost 1 ms 
[TRACE] 2025-06-04 04:20:00.832 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 04:20:00.832 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 04:20:00.832 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 04:20:00.832 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 04:20:00.932 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:00.932 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:00.932 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:00.932 - [TAP-6799(100)][eb0b59af-1c21-4e1b-bb71-6b8201e4416f] - Node eb0b59af-1c21-4e1b-bb71-6b8201e4416f[eb0b59af-1c21-4e1b-bb71-6b8201e4416f] start preload schema,table counts: 0 
[TRACE] 2025-06-04 04:20:00.932 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:00.932 - [TAP-6799(100)][eb0b59af-1c21-4e1b-bb71-6b8201e4416f] - Node eb0b59af-1c21-4e1b-bb71-6b8201e4416f[eb0b59af-1c21-4e1b-bb71-6b8201e4416f] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:00.932 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 73fce465-47ee-46f4-abb7-173648d43889) enable batch process 
[TRACE] 2025-06-04 04:20:01.116 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] running status set to false 
[TRACE] 2025-06-04 04:20:01.116 - [TAP-6799(100)][FDM_product] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010800993 
[TRACE] 2025-06-04 04:20:01.116 - [TAP-6799(100)][FDM_product] - PDK connector node released: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010800993 
[TRACE] 2025-06-04 04:20:01.116 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] schema data cleaned 
[TRACE] 2025-06-04 04:20:01.117 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] monitor closed 
[TRACE] 2025-06-04 04:20:01.117 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] close complete, cost 2 ms 
[TRACE] 2025-06-04 04:20:01.324 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] running status set to false 
[TRACE] 2025-06-04 04:20:01.325 - [TAP-6799(100)][eb0b59af-1c21-4e1b-bb71-6b8201e4416f] - Node eb0b59af-1c21-4e1b-bb71-6b8201e4416f[eb0b59af-1c21-4e1b-bb71-6b8201e4416f] running status set to false 
[TRACE] 2025-06-04 04:20:01.325 - [TAP-6799(100)][eb0b59af-1c21-4e1b-bb71-6b8201e4416f] - Node eb0b59af-1c21-4e1b-bb71-6b8201e4416f[eb0b59af-1c21-4e1b-bb71-6b8201e4416f] schema data cleaned 
[TRACE] 2025-06-04 04:20:01.325 - [TAP-6799(100)][eb0b59af-1c21-4e1b-bb71-6b8201e4416f] - Node eb0b59af-1c21-4e1b-bb71-6b8201e4416f[eb0b59af-1c21-4e1b-bb71-6b8201e4416f] monitor closed 
[TRACE] 2025-06-04 04:20:01.325 - [TAP-6799(100)][eb0b59af-1c21-4e1b-bb71-6b8201e4416f] - Node eb0b59af-1c21-4e1b-bb71-6b8201e4416f[eb0b59af-1c21-4e1b-bb71-6b8201e4416f] close complete, cost 0 ms 
[INFO ] 2025-06-04 04:20:01.326 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-6f7a744f-75d6-4816-a0ce-3f515369e7f7 
[INFO ] 2025-06-04 04:20:01.326 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-6f7a744f-75d6-4816-a0ce-3f515369e7f7 
[INFO ] 2025-06-04 04:20:01.326 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:20:01.327 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] schema data cleaned 
[TRACE] 2025-06-04 04:20:01.327 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] monitor closed 
[TRACE] 2025-06-04 04:20:01.327 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] close complete, cost 2 ms 
[TRACE] 2025-06-04 04:20:01.328 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 04:20:01.328 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 04:20:01.328 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 04:20:01.398 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 04:20:01.399 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:01.399 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:01.399 - [TAP-6799(100)][9a581254-b0b8-4da9-9411-416c8b6becce] - Node 9a581254-b0b8-4da9-9411-416c8b6becce[9a581254-b0b8-4da9-9411-416c8b6becce] start preload schema,table counts: 0 
[TRACE] 2025-06-04 04:20:01.399 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:01.399 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:01.399 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:01.399 - [TAP-6799(100)][9a581254-b0b8-4da9-9411-416c8b6becce] - Node 9a581254-b0b8-4da9-9411-416c8b6becce[9a581254-b0b8-4da9-9411-416c8b6becce] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:01.399 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 1674c3ea-16fc-4674-9a27-0cd5951e0f48) enable batch process 
[TRACE] 2025-06-04 04:20:01.399 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:01.563 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 73fce465-47ee-46f4-abb7-173648d43889) enable batch process 
[TRACE] 2025-06-04 04:20:01.563 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] running status set to false 
[TRACE] 2025-06-04 04:20:01.565 - [TAP-6799(100)][FDM_product] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010801454 
[TRACE] 2025-06-04 04:20:01.565 - [TAP-6799(100)][FDM_product] - PDK connector node released: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010801454 
[TRACE] 2025-06-04 04:20:01.565 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] schema data cleaned 
[TRACE] 2025-06-04 04:20:01.565 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] monitor closed 
[TRACE] 2025-06-04 04:20:01.740 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] close complete, cost 2 ms 
[TRACE] 2025-06-04 04:20:01.741 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] running status set to false 
[INFO ] 2025-06-04 04:20:01.744 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-cd9232eb-8fcb-4857-8cd3-1c5aa23d127e 
[INFO ] 2025-06-04 04:20:01.744 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-cd9232eb-8fcb-4857-8cd3-1c5aa23d127e 
[INFO ] 2025-06-04 04:20:01.744 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:20:01.745 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] schema data cleaned 
[TRACE] 2025-06-04 04:20:01.745 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] monitor closed 
[TRACE] 2025-06-04 04:20:01.745 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] close complete, cost 4 ms 
[TRACE] 2025-06-04 04:20:01.907 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] running status set to false 
[INFO ] 2025-06-04 04:20:01.910 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-3bc5e319-b6c3-4133-b63a-d2b2fa8734b1 
[INFO ] 2025-06-04 04:20:01.910 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-3bc5e319-b6c3-4133-b63a-d2b2fa8734b1 
[INFO ] 2025-06-04 04:20:01.910 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-1674c3ea-16fc-4674-9a27-0cd5951e0f48-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:20:01.910 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] schema data cleaned 
[TRACE] 2025-06-04 04:20:01.910 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] monitor closed 
[TRACE] 2025-06-04 04:20:01.910 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] close complete, cost 3 ms 
[TRACE] 2025-06-04 04:20:01.911 - [TAP-6799(100)][9a581254-b0b8-4da9-9411-416c8b6becce] - Node 9a581254-b0b8-4da9-9411-416c8b6becce[9a581254-b0b8-4da9-9411-416c8b6becce] running status set to false 
[TRACE] 2025-06-04 04:20:01.912 - [TAP-6799(100)][9a581254-b0b8-4da9-9411-416c8b6becce] - Node 9a581254-b0b8-4da9-9411-416c8b6becce[9a581254-b0b8-4da9-9411-416c8b6becce] schema data cleaned 
[TRACE] 2025-06-04 04:20:01.912 - [TAP-6799(100)][9a581254-b0b8-4da9-9411-416c8b6becce] - Node 9a581254-b0b8-4da9-9411-416c8b6becce[9a581254-b0b8-4da9-9411-416c8b6becce] monitor closed 
[TRACE] 2025-06-04 04:20:01.912 - [TAP-6799(100)][9a581254-b0b8-4da9-9411-416c8b6becce] - Node 9a581254-b0b8-4da9-9411-416c8b6becce[9a581254-b0b8-4da9-9411-416c8b6becce] close complete, cost 0 ms 
[TRACE] 2025-06-04 04:20:01.912 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 04:20:01.912 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 04:20:01.912 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 04:20:01.912 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 04:20:01.916 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:01.916 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:01.916 - [TAP-6799(100)][8e968e4c-55e0-4f87-8b2d-6deca21d56a7] - Node 8e968e4c-55e0-4f87-8b2d-6deca21d56a7[8e968e4c-55e0-4f87-8b2d-6deca21d56a7] start preload schema,table counts: 0 
[TRACE] 2025-06-04 04:20:01.916 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:01.916 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:01.916 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:01.916 - [TAP-6799(100)][8e968e4c-55e0-4f87-8b2d-6deca21d56a7] - Node 8e968e4c-55e0-4f87-8b2d-6deca21d56a7[8e968e4c-55e0-4f87-8b2d-6deca21d56a7] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:01.916 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:01.916 - [TAP-6799(100)][MDM_packages] - Node js_processor(MDM_packages: 8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d) enable batch process 
[TRACE] 2025-06-04 04:20:01.916 - [TAP-6799(100)][MDM_packages] - Node field_add_del_processor(MDM_packages: 3080ce1e-1a69-43e1-8283-6bfbdbad03d9) enable batch process 
[TRACE] 2025-06-04 04:20:02.025 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] running status set to false 
[TRACE] 2025-06-04 04:20:02.025 - [TAP-6799(100)][MDM_packages] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749010801935 
[TRACE] 2025-06-04 04:20:02.025 - [TAP-6799(100)][MDM_packages] - PDK connector node released: HazelcastSampleSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749010801935 
[TRACE] 2025-06-04 04:20:02.025 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] schema data cleaned 
[TRACE] 2025-06-04 04:20:02.025 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] monitor closed 
[TRACE] 2025-06-04 04:20:02.025 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] close complete, cost 1 ms 
[TRACE] 2025-06-04 04:20:02.047 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] running status set to false 
[TRACE] 2025-06-04 04:20:02.047 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] schema data cleaned 
[TRACE] 2025-06-04 04:20:02.047 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] monitor closed 
[TRACE] 2025-06-04 04:20:02.047 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] close complete, cost 20 ms 
[TRACE] 2025-06-04 04:20:02.204 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] running status set to false 
[INFO ] 2025-06-04 04:20:02.204 - [TAP-6799(100)][MDM_packages][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-d44626a6-d9c0-4a2f-a9a4-487b49ed24ef 
[INFO ] 2025-06-04 04:20:02.204 - [TAP-6799(100)][MDM_packages][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-d44626a6-d9c0-4a2f-a9a4-487b49ed24ef 
[INFO ] 2025-06-04 04:20:02.204 - [TAP-6799(100)][MDM_packages][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:20:02.205 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] schema data cleaned 
[TRACE] 2025-06-04 04:20:02.205 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] monitor closed 
[TRACE] 2025-06-04 04:20:02.205 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] close complete, cost 2 ms 
[TRACE] 2025-06-04 04:20:02.210 - [TAP-6799(100)][8e968e4c-55e0-4f87-8b2d-6deca21d56a7] - Node 8e968e4c-55e0-4f87-8b2d-6deca21d56a7[8e968e4c-55e0-4f87-8b2d-6deca21d56a7] running status set to false 
[TRACE] 2025-06-04 04:20:02.211 - [TAP-6799(100)][8e968e4c-55e0-4f87-8b2d-6deca21d56a7] - Node 8e968e4c-55e0-4f87-8b2d-6deca21d56a7[8e968e4c-55e0-4f87-8b2d-6deca21d56a7] schema data cleaned 
[TRACE] 2025-06-04 04:20:02.211 - [TAP-6799(100)][8e968e4c-55e0-4f87-8b2d-6deca21d56a7] - Node 8e968e4c-55e0-4f87-8b2d-6deca21d56a7[8e968e4c-55e0-4f87-8b2d-6deca21d56a7] monitor closed 
[TRACE] 2025-06-04 04:20:02.211 - [TAP-6799(100)][8e968e4c-55e0-4f87-8b2d-6deca21d56a7] - Node 8e968e4c-55e0-4f87-8b2d-6deca21d56a7[8e968e4c-55e0-4f87-8b2d-6deca21d56a7] close complete, cost 0 ms 
[TRACE] 2025-06-04 04:20:02.211 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 04:20:02.211 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 04:20:02.211 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 04:20:02.271 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 04:20:02.271 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:02.271 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:02.271 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:02.271 - [TAP-6799(100)][fa4ce548-9479-4767-b9d2-76961702ff13] - Node fa4ce548-9479-4767-b9d2-76961702ff13[fa4ce548-9479-4767-b9d2-76961702ff13] start preload schema,table counts: 0 
[TRACE] 2025-06-04 04:20:02.271 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:02.272 - [TAP-6799(100)][fa4ce548-9479-4767-b9d2-76961702ff13] - Node fa4ce548-9479-4767-b9d2-76961702ff13[fa4ce548-9479-4767-b9d2-76961702ff13] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:02.272 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 73fce465-47ee-46f4-abb7-173648d43889) enable batch process 
[TRACE] 2025-06-04 04:20:02.416 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] running status set to false 
[TRACE] 2025-06-04 04:20:02.416 - [TAP-6799(100)][FDM_product] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010802310 
[TRACE] 2025-06-04 04:20:02.416 - [TAP-6799(100)][FDM_product] - PDK connector node released: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010802310 
[TRACE] 2025-06-04 04:20:02.416 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] schema data cleaned 
[TRACE] 2025-06-04 04:20:02.416 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] monitor closed 
[TRACE] 2025-06-04 04:20:02.596 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] close complete, cost 2 ms 
[TRACE] 2025-06-04 04:20:02.596 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] running status set to false 
[INFO ] 2025-06-04 04:20:02.598 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-5aab8944-1a0d-440f-8600-b50171c0ba5a 
[INFO ] 2025-06-04 04:20:02.598 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-5aab8944-1a0d-440f-8600-b50171c0ba5a 
[INFO ] 2025-06-04 04:20:02.598 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:20:02.599 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] schema data cleaned 
[TRACE] 2025-06-04 04:20:02.599 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] monitor closed 
[TRACE] 2025-06-04 04:20:02.601 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] close complete, cost 2 ms 
[TRACE] 2025-06-04 04:20:02.601 - [TAP-6799(100)][fa4ce548-9479-4767-b9d2-76961702ff13] - Node fa4ce548-9479-4767-b9d2-76961702ff13[fa4ce548-9479-4767-b9d2-76961702ff13] running status set to false 
[TRACE] 2025-06-04 04:20:02.601 - [TAP-6799(100)][fa4ce548-9479-4767-b9d2-76961702ff13] - Node fa4ce548-9479-4767-b9d2-76961702ff13[fa4ce548-9479-4767-b9d2-76961702ff13] schema data cleaned 
[TRACE] 2025-06-04 04:20:02.601 - [TAP-6799(100)][fa4ce548-9479-4767-b9d2-76961702ff13] - Node fa4ce548-9479-4767-b9d2-76961702ff13[fa4ce548-9479-4767-b9d2-76961702ff13] monitor closed 
[TRACE] 2025-06-04 04:20:02.602 - [TAP-6799(100)][fa4ce548-9479-4767-b9d2-76961702ff13] - Node fa4ce548-9479-4767-b9d2-76961702ff13[fa4ce548-9479-4767-b9d2-76961702ff13] close complete, cost 0 ms 
[TRACE] 2025-06-04 04:20:02.602 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 04:20:02.602 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 04:20:02.602 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 04:20:02.602 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 04:20:02.699 - [TAP-6799(100)][d8afe0fe-56a0-4b1e-88bf-e894bac43bb4] - Node d8afe0fe-56a0-4b1e-88bf-e894bac43bb4[d8afe0fe-56a0-4b1e-88bf-e894bac43bb4] start preload schema,table counts: 0 
[TRACE] 2025-06-04 04:20:02.700 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:02.700 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:02.700 - [TAP-6799(100)][d8afe0fe-56a0-4b1e-88bf-e894bac43bb4] - Node d8afe0fe-56a0-4b1e-88bf-e894bac43bb4[d8afe0fe-56a0-4b1e-88bf-e894bac43bb4] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:02.700 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:02.700 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:02.700 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:02.700 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 73fce465-47ee-46f4-abb7-173648d43889) enable batch process 
[TRACE] 2025-06-04 04:20:02.700 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:02.700 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 1674c3ea-16fc-4674-9a27-0cd5951e0f48) enable batch process 
[TRACE] 2025-06-04 04:20:02.851 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] running status set to false 
[TRACE] 2025-06-04 04:20:02.851 - [TAP-6799(100)][FDM_product] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010802735 
[TRACE] 2025-06-04 04:20:02.851 - [TAP-6799(100)][FDM_product] - PDK connector node released: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010802735 
[TRACE] 2025-06-04 04:20:02.852 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] schema data cleaned 
[TRACE] 2025-06-04 04:20:02.852 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] monitor closed 
[TRACE] 2025-06-04 04:20:02.852 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] close complete, cost 2 ms 
[TRACE] 2025-06-04 04:20:03.062 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] running status set to false 
[INFO ] 2025-06-04 04:20:03.065 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-1675312b-3833-4d8e-ba2a-2e618b4a7371 
[INFO ] 2025-06-04 04:20:03.066 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-1675312b-3833-4d8e-ba2a-2e618b4a7371 
[INFO ] 2025-06-04 04:20:03.066 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:20:03.066 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] schema data cleaned 
[TRACE] 2025-06-04 04:20:03.066 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] monitor closed 
[TRACE] 2025-06-04 04:20:03.066 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] close complete, cost 5 ms 
[TRACE] 2025-06-04 04:20:03.167 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:03.167 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:03.167 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:03.167 - [TAP-6799(100)][06604b9f-8f89-4ff5-a851-d06044e0ea4a] - Node 06604b9f-8f89-4ff5-a851-d06044e0ea4a[06604b9f-8f89-4ff5-a851-d06044e0ea4a] start preload schema,table counts: 0 
[TRACE] 2025-06-04 04:20:03.167 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:03.167 - [TAP-6799(100)][MDM_packages] - Node field_add_del_processor(MDM_packages: 3080ce1e-1a69-43e1-8283-6bfbdbad03d9) enable batch process 
[TRACE] 2025-06-04 04:20:03.168 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:03.168 - [TAP-6799(100)][06604b9f-8f89-4ff5-a851-d06044e0ea4a] - Node 06604b9f-8f89-4ff5-a851-d06044e0ea4a[06604b9f-8f89-4ff5-a851-d06044e0ea4a] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:03.168 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:03.168 - [TAP-6799(100)][MDM_packages] - Node js_processor(MDM_packages: 8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d) enable batch process 
[TRACE] 2025-06-04 04:20:03.256 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] running status set to false 
[TRACE] 2025-06-04 04:20:03.256 - [TAP-6799(100)][d8afe0fe-56a0-4b1e-88bf-e894bac43bb4] - Node d8afe0fe-56a0-4b1e-88bf-e894bac43bb4[d8afe0fe-56a0-4b1e-88bf-e894bac43bb4] running status set to false 
[TRACE] 2025-06-04 04:20:03.257 - [TAP-6799(100)][d8afe0fe-56a0-4b1e-88bf-e894bac43bb4] - Node d8afe0fe-56a0-4b1e-88bf-e894bac43bb4[d8afe0fe-56a0-4b1e-88bf-e894bac43bb4] schema data cleaned 
[TRACE] 2025-06-04 04:20:03.257 - [TAP-6799(100)][d8afe0fe-56a0-4b1e-88bf-e894bac43bb4] - Node d8afe0fe-56a0-4b1e-88bf-e894bac43bb4[d8afe0fe-56a0-4b1e-88bf-e894bac43bb4] monitor closed 
[TRACE] 2025-06-04 04:20:03.257 - [TAP-6799(100)][d8afe0fe-56a0-4b1e-88bf-e894bac43bb4] - Node d8afe0fe-56a0-4b1e-88bf-e894bac43bb4[d8afe0fe-56a0-4b1e-88bf-e894bac43bb4] close complete, cost 0 ms 
[INFO ] 2025-06-04 04:20:03.257 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-65f6e354-1e9c-40a1-b913-c5d76911ee17 
[INFO ] 2025-06-04 04:20:03.257 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-65f6e354-1e9c-40a1-b913-c5d76911ee17 
[INFO ] 2025-06-04 04:20:03.258 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-1674c3ea-16fc-4674-9a27-0cd5951e0f48-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:20:03.258 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] schema data cleaned 
[TRACE] 2025-06-04 04:20:03.258 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] monitor closed 
[TRACE] 2025-06-04 04:20:03.258 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] close complete, cost 2 ms 
[TRACE] 2025-06-04 04:20:03.259 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 04:20:03.259 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 04:20:03.259 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 04:20:03.259 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 04:20:03.319 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] running status set to false 
[TRACE] 2025-06-04 04:20:03.319 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] running status set to false 
[TRACE] 2025-06-04 04:20:03.319 - [TAP-6799(100)][MDM_packages] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749010803213 
[TRACE] 2025-06-04 04:20:03.319 - [TAP-6799(100)][MDM_packages] - PDK connector node released: HazelcastSampleSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749010803213 
[TRACE] 2025-06-04 04:20:03.319 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] schema data cleaned 
[TRACE] 2025-06-04 04:20:03.319 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] monitor closed 
[TRACE] 2025-06-04 04:20:03.338 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] close complete, cost 1 ms 
[TRACE] 2025-06-04 04:20:03.338 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] schema data cleaned 
[TRACE] 2025-06-04 04:20:03.338 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] monitor closed 
[TRACE] 2025-06-04 04:20:03.338 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] close complete, cost 19 ms 
[TRACE] 2025-06-04 04:20:03.524 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] running status set to false 
[INFO ] 2025-06-04 04:20:03.524 - [TAP-6799(100)][MDM_packages][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-6d7c9992-7e81-456c-8cc0-7fe30df3c09b 
[INFO ] 2025-06-04 04:20:03.525 - [TAP-6799(100)][MDM_packages][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-6d7c9992-7e81-456c-8cc0-7fe30df3c09b 
[INFO ] 2025-06-04 04:20:03.525 - [TAP-6799(100)][MDM_packages][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:20:03.525 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] schema data cleaned 
[TRACE] 2025-06-04 04:20:03.525 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] monitor closed 
[TRACE] 2025-06-04 04:20:03.525 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] close complete, cost 1 ms 
[TRACE] 2025-06-04 04:20:03.534 - [TAP-6799(100)][06604b9f-8f89-4ff5-a851-d06044e0ea4a] - Node 06604b9f-8f89-4ff5-a851-d06044e0ea4a[06604b9f-8f89-4ff5-a851-d06044e0ea4a] running status set to false 
[TRACE] 2025-06-04 04:20:03.534 - [TAP-6799(100)][06604b9f-8f89-4ff5-a851-d06044e0ea4a] - Node 06604b9f-8f89-4ff5-a851-d06044e0ea4a[06604b9f-8f89-4ff5-a851-d06044e0ea4a] schema data cleaned 
[TRACE] 2025-06-04 04:20:03.534 - [TAP-6799(100)][06604b9f-8f89-4ff5-a851-d06044e0ea4a] - Node 06604b9f-8f89-4ff5-a851-d06044e0ea4a[06604b9f-8f89-4ff5-a851-d06044e0ea4a] monitor closed 
[TRACE] 2025-06-04 04:20:03.534 - [TAP-6799(100)][06604b9f-8f89-4ff5-a851-d06044e0ea4a] - Node 06604b9f-8f89-4ff5-a851-d06044e0ea4a[06604b9f-8f89-4ff5-a851-d06044e0ea4a] close complete, cost 0 ms 
[TRACE] 2025-06-04 04:20:03.534 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 04:20:03.534 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 04:20:03.534 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 04:20:03.534 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 04:20:03.599 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:03.599 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:03.599 - [TAP-6799(100)][0bb8aa78-bb23-45c4-a498-ba6ea37a7e30] - Node 0bb8aa78-bb23-45c4-a498-ba6ea37a7e30[0bb8aa78-bb23-45c4-a498-ba6ea37a7e30] start preload schema,table counts: 0 
[TRACE] 2025-06-04 04:20:03.599 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:03.599 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:03.599 - [TAP-6799(100)][0bb8aa78-bb23-45c4-a498-ba6ea37a7e30] - Node 0bb8aa78-bb23-45c4-a498-ba6ea37a7e30[0bb8aa78-bb23-45c4-a498-ba6ea37a7e30] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:03.731 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 73fce465-47ee-46f4-abb7-173648d43889) enable batch process 
[TRACE] 2025-06-04 04:20:03.731 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] running status set to false 
[TRACE] 2025-06-04 04:20:03.732 - [TAP-6799(100)][FDM_product] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010803638 
[TRACE] 2025-06-04 04:20:03.732 - [TAP-6799(100)][FDM_product] - PDK connector node released: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010803638 
[TRACE] 2025-06-04 04:20:03.732 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] schema data cleaned 
[TRACE] 2025-06-04 04:20:03.733 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] monitor closed 
[TRACE] 2025-06-04 04:20:03.923 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] close complete, cost 2 ms 
[TRACE] 2025-06-04 04:20:03.923 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] running status set to false 
[INFO ] 2025-06-04 04:20:03.924 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-af4a7f86-3500-4d9f-b4b3-8593eccb34a8 
[INFO ] 2025-06-04 04:20:03.924 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-af4a7f86-3500-4d9f-b4b3-8593eccb34a8 
[INFO ] 2025-06-04 04:20:03.924 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:20:03.925 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] schema data cleaned 
[TRACE] 2025-06-04 04:20:03.925 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] monitor closed 
[TRACE] 2025-06-04 04:20:03.927 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] close complete, cost 2 ms 
[TRACE] 2025-06-04 04:20:03.927 - [TAP-6799(100)][0bb8aa78-bb23-45c4-a498-ba6ea37a7e30] - Node 0bb8aa78-bb23-45c4-a498-ba6ea37a7e30[0bb8aa78-bb23-45c4-a498-ba6ea37a7e30] running status set to false 
[TRACE] 2025-06-04 04:20:03.927 - [TAP-6799(100)][0bb8aa78-bb23-45c4-a498-ba6ea37a7e30] - Node 0bb8aa78-bb23-45c4-a498-ba6ea37a7e30[0bb8aa78-bb23-45c4-a498-ba6ea37a7e30] schema data cleaned 
[TRACE] 2025-06-04 04:20:03.927 - [TAP-6799(100)][0bb8aa78-bb23-45c4-a498-ba6ea37a7e30] - Node 0bb8aa78-bb23-45c4-a498-ba6ea37a7e30[0bb8aa78-bb23-45c4-a498-ba6ea37a7e30] monitor closed 
[TRACE] 2025-06-04 04:20:03.927 - [TAP-6799(100)][0bb8aa78-bb23-45c4-a498-ba6ea37a7e30] - Node 0bb8aa78-bb23-45c4-a498-ba6ea37a7e30[0bb8aa78-bb23-45c4-a498-ba6ea37a7e30] close complete, cost 0 ms 
[TRACE] 2025-06-04 04:20:03.928 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 04:20:03.928 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 04:20:03.928 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 04:20:03.928 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 04:20:03.988 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:03.988 - [TAP-6799(100)][7fd705e8-7727-4e6e-9f96-3cf3f27cdc8c] - Node 7fd705e8-7727-4e6e-9f96-3cf3f27cdc8c[7fd705e8-7727-4e6e-9f96-3cf3f27cdc8c] start preload schema,table counts: 0 
[TRACE] 2025-06-04 04:20:03.988 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:03.988 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:03.988 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:03.989 - [TAP-6799(100)][7fd705e8-7727-4e6e-9f96-3cf3f27cdc8c] - Node 7fd705e8-7727-4e6e-9f96-3cf3f27cdc8c[7fd705e8-7727-4e6e-9f96-3cf3f27cdc8c] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:03.989 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:03.989 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:03.989 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 73fce465-47ee-46f4-abb7-173648d43889) enable batch process 
[TRACE] 2025-06-04 04:20:03.989 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 1674c3ea-16fc-4674-9a27-0cd5951e0f48) enable batch process 
[TRACE] 2025-06-04 04:20:04.137 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] running status set to false 
[TRACE] 2025-06-04 04:20:04.138 - [TAP-6799(100)][FDM_product] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010804029 
[TRACE] 2025-06-04 04:20:04.138 - [TAP-6799(100)][FDM_product] - PDK connector node released: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010804029 
[TRACE] 2025-06-04 04:20:04.138 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] schema data cleaned 
[TRACE] 2025-06-04 04:20:04.138 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] monitor closed 
[TRACE] 2025-06-04 04:20:04.138 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] close complete, cost 2 ms 
[TRACE] 2025-06-04 04:20:04.329 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] running status set to false 
[INFO ] 2025-06-04 04:20:04.332 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-f75f8bc9-8714-41b9-81bd-2e6746a8020c 
[INFO ] 2025-06-04 04:20:04.332 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-f75f8bc9-8714-41b9-81bd-2e6746a8020c 
[INFO ] 2025-06-04 04:20:04.332 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:20:04.333 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] schema data cleaned 
[TRACE] 2025-06-04 04:20:04.333 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] monitor closed 
[TRACE] 2025-06-04 04:20:04.333 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] close complete, cost 3 ms 
[TRACE] 2025-06-04 04:20:04.494 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] running status set to false 
[INFO ] 2025-06-04 04:20:04.494 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-c861afdf-ddc8-4dab-b23d-7bbdda83799b 
[INFO ] 2025-06-04 04:20:04.494 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-c861afdf-ddc8-4dab-b23d-7bbdda83799b 
[INFO ] 2025-06-04 04:20:04.494 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-1674c3ea-16fc-4674-9a27-0cd5951e0f48-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:20:04.494 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] schema data cleaned 
[TRACE] 2025-06-04 04:20:04.495 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] monitor closed 
[TRACE] 2025-06-04 04:20:04.495 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] close complete, cost 1 ms 
[TRACE] 2025-06-04 04:20:04.497 - [TAP-6799(100)][7fd705e8-7727-4e6e-9f96-3cf3f27cdc8c] - Node 7fd705e8-7727-4e6e-9f96-3cf3f27cdc8c[7fd705e8-7727-4e6e-9f96-3cf3f27cdc8c] running status set to false 
[TRACE] 2025-06-04 04:20:04.498 - [TAP-6799(100)][7fd705e8-7727-4e6e-9f96-3cf3f27cdc8c] - Node 7fd705e8-7727-4e6e-9f96-3cf3f27cdc8c[7fd705e8-7727-4e6e-9f96-3cf3f27cdc8c] schema data cleaned 
[TRACE] 2025-06-04 04:20:04.498 - [TAP-6799(100)][7fd705e8-7727-4e6e-9f96-3cf3f27cdc8c] - Node 7fd705e8-7727-4e6e-9f96-3cf3f27cdc8c[7fd705e8-7727-4e6e-9f96-3cf3f27cdc8c] monitor closed 
[TRACE] 2025-06-04 04:20:04.498 - [TAP-6799(100)][7fd705e8-7727-4e6e-9f96-3cf3f27cdc8c] - Node 7fd705e8-7727-4e6e-9f96-3cf3f27cdc8c[7fd705e8-7727-4e6e-9f96-3cf3f27cdc8c] close complete, cost 0 ms 
[TRACE] 2025-06-04 04:20:04.498 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 04:20:04.498 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 04:20:04.499 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 04:20:04.499 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 04:20:06.027 - [TAP-6799(100)][291b55b5-b5a2-459b-a16b-89fe684276a3] - Node 291b55b5-b5a2-459b-a16b-89fe684276a3[291b55b5-b5a2-459b-a16b-89fe684276a3] start preload schema,table counts: 0 
[TRACE] 2025-06-04 04:20:06.027 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:06.027 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:06.027 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:06.027 - [TAP-6799(100)][291b55b5-b5a2-459b-a16b-89fe684276a3] - Node 291b55b5-b5a2-459b-a16b-89fe684276a3[291b55b5-b5a2-459b-a16b-89fe684276a3] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:06.027 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:06.027 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:06.027 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:06.027 - [TAP-6799(100)][MDM_packages] - Node field_add_del_processor(MDM_packages: 3080ce1e-1a69-43e1-8283-6bfbdbad03d9) enable batch process 
[TRACE] 2025-06-04 04:20:06.153 - [TAP-6799(100)][MDM_packages] - Node js_processor(MDM_packages: 8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d) enable batch process 
[TRACE] 2025-06-04 04:20:06.153 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] running status set to false 
[TRACE] 2025-06-04 04:20:06.155 - [TAP-6799(100)][MDM_packages] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749010806056 
[TRACE] 2025-06-04 04:20:06.155 - [TAP-6799(100)][MDM_packages] - PDK connector node released: HazelcastSampleSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749010806056 
[TRACE] 2025-06-04 04:20:06.155 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] schema data cleaned 
[TRACE] 2025-06-04 04:20:06.155 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] monitor closed 
[TRACE] 2025-06-04 04:20:06.156 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] close complete, cost 2 ms 
[TRACE] 2025-06-04 04:20:06.157 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] running status set to false 
[TRACE] 2025-06-04 04:20:06.175 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] schema data cleaned 
[TRACE] 2025-06-04 04:20:06.175 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] monitor closed 
[TRACE] 2025-06-04 04:20:06.175 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] close complete, cost 18 ms 
[TRACE] 2025-06-04 04:20:06.337 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] running status set to false 
[INFO ] 2025-06-04 04:20:06.337 - [TAP-6799(100)][MDM_packages][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-a55c8915-edde-4b13-8e06-e03955572b21 
[INFO ] 2025-06-04 04:20:06.337 - [TAP-6799(100)][MDM_packages][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-a55c8915-edde-4b13-8e06-e03955572b21 
[INFO ] 2025-06-04 04:20:06.337 - [TAP-6799(100)][MDM_packages][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:20:06.337 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] schema data cleaned 
[TRACE] 2025-06-04 04:20:06.337 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] monitor closed 
[TRACE] 2025-06-04 04:20:06.338 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] close complete, cost 2 ms 
[TRACE] 2025-06-04 04:20:06.341 - [TAP-6799(100)][291b55b5-b5a2-459b-a16b-89fe684276a3] - Node 291b55b5-b5a2-459b-a16b-89fe684276a3[291b55b5-b5a2-459b-a16b-89fe684276a3] running status set to false 
[TRACE] 2025-06-04 04:20:06.342 - [TAP-6799(100)][291b55b5-b5a2-459b-a16b-89fe684276a3] - Node 291b55b5-b5a2-459b-a16b-89fe684276a3[291b55b5-b5a2-459b-a16b-89fe684276a3] schema data cleaned 
[TRACE] 2025-06-04 04:20:06.342 - [TAP-6799(100)][291b55b5-b5a2-459b-a16b-89fe684276a3] - Node 291b55b5-b5a2-459b-a16b-89fe684276a3[291b55b5-b5a2-459b-a16b-89fe684276a3] monitor closed 
[TRACE] 2025-06-04 04:20:06.342 - [TAP-6799(100)][291b55b5-b5a2-459b-a16b-89fe684276a3] - Node 291b55b5-b5a2-459b-a16b-89fe684276a3[291b55b5-b5a2-459b-a16b-89fe684276a3] close complete, cost 0 ms 
[TRACE] 2025-06-04 04:20:06.342 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 04:20:06.342 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 04:20:06.342 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 04:20:06.422 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 04:20:06.422 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:06.422 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:06.422 - [TAP-6799(100)][0d6162f0-aa0c-4e1f-8d56-5f81eb31e6e1] - Node 0d6162f0-aa0c-4e1f-8d56-5f81eb31e6e1[0d6162f0-aa0c-4e1f-8d56-5f81eb31e6e1] start preload schema,table counts: 0 
[TRACE] 2025-06-04 04:20:06.422 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:06.422 - [TAP-6799(100)][0d6162f0-aa0c-4e1f-8d56-5f81eb31e6e1] - Node 0d6162f0-aa0c-4e1f-8d56-5f81eb31e6e1[0d6162f0-aa0c-4e1f-8d56-5f81eb31e6e1] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:06.422 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:06.568 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 73fce465-47ee-46f4-abb7-173648d43889) enable batch process 
[TRACE] 2025-06-04 04:20:06.568 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] running status set to false 
[TRACE] 2025-06-04 04:20:06.576 - [TAP-6799(100)][FDM_product] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010806460 
[TRACE] 2025-06-04 04:20:06.576 - [TAP-6799(100)][FDM_product] - PDK connector node released: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010806460 
[TRACE] 2025-06-04 04:20:06.576 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] schema data cleaned 
[TRACE] 2025-06-04 04:20:06.576 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] monitor closed 
[TRACE] 2025-06-04 04:20:06.576 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] close complete, cost 8 ms 
[TRACE] 2025-06-04 04:20:06.836 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] running status set to false 
[INFO ] 2025-06-04 04:20:06.838 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-09e2a6a4-f966-4983-9e00-118d8beaa9cb 
[INFO ] 2025-06-04 04:20:06.838 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-09e2a6a4-f966-4983-9e00-118d8beaa9cb 
[INFO ] 2025-06-04 04:20:06.838 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:20:06.839 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] schema data cleaned 
[TRACE] 2025-06-04 04:20:06.839 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] monitor closed 
[TRACE] 2025-06-04 04:20:06.840 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] close complete, cost 3 ms 
[TRACE] 2025-06-04 04:20:06.840 - [TAP-6799(100)][0d6162f0-aa0c-4e1f-8d56-5f81eb31e6e1] - Node 0d6162f0-aa0c-4e1f-8d56-5f81eb31e6e1[0d6162f0-aa0c-4e1f-8d56-5f81eb31e6e1] running status set to false 
[TRACE] 2025-06-04 04:20:06.840 - [TAP-6799(100)][0d6162f0-aa0c-4e1f-8d56-5f81eb31e6e1] - Node 0d6162f0-aa0c-4e1f-8d56-5f81eb31e6e1[0d6162f0-aa0c-4e1f-8d56-5f81eb31e6e1] schema data cleaned 
[TRACE] 2025-06-04 04:20:06.840 - [TAP-6799(100)][0d6162f0-aa0c-4e1f-8d56-5f81eb31e6e1] - Node 0d6162f0-aa0c-4e1f-8d56-5f81eb31e6e1[0d6162f0-aa0c-4e1f-8d56-5f81eb31e6e1] monitor closed 
[TRACE] 2025-06-04 04:20:06.840 - [TAP-6799(100)][0d6162f0-aa0c-4e1f-8d56-5f81eb31e6e1] - Node 0d6162f0-aa0c-4e1f-8d56-5f81eb31e6e1[0d6162f0-aa0c-4e1f-8d56-5f81eb31e6e1] close complete, cost 0 ms 
[TRACE] 2025-06-04 04:20:06.840 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 04:20:06.840 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 04:20:06.840 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 04:20:06.840 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 04:20:06.921 - [TAP-6799(100)][09028c31-8bb5-4e5e-a049-cf7048c80e5d] - Node 09028c31-8bb5-4e5e-a049-cf7048c80e5d[09028c31-8bb5-4e5e-a049-cf7048c80e5d] start preload schema,table counts: 0 
[TRACE] 2025-06-04 04:20:06.921 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:06.921 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:06.921 - [TAP-6799(100)][09028c31-8bb5-4e5e-a049-cf7048c80e5d] - Node 09028c31-8bb5-4e5e-a049-cf7048c80e5d[09028c31-8bb5-4e5e-a049-cf7048c80e5d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:06.922 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:06.922 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:06.922 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:06.922 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:06.922 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 1674c3ea-16fc-4674-9a27-0cd5951e0f48) enable batch process 
[TRACE] 2025-06-04 04:20:07.059 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 73fce465-47ee-46f4-abb7-173648d43889) enable batch process 
[TRACE] 2025-06-04 04:20:07.059 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] running status set to false 
[TRACE] 2025-06-04 04:20:07.062 - [TAP-6799(100)][FDM_product] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010806966 
[TRACE] 2025-06-04 04:20:07.062 - [TAP-6799(100)][FDM_product] - PDK connector node released: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010806966 
[TRACE] 2025-06-04 04:20:07.062 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] schema data cleaned 
[TRACE] 2025-06-04 04:20:07.062 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] monitor closed 
[TRACE] 2025-06-04 04:20:07.264 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] close complete, cost 3 ms 
[TRACE] 2025-06-04 04:20:07.267 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] running status set to false 
[INFO ] 2025-06-04 04:20:07.267 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-f4847135-3f17-4c83-8586-7d95a6a1b6c2 
[INFO ] 2025-06-04 04:20:07.267 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-f4847135-3f17-4c83-8586-7d95a6a1b6c2 
[INFO ] 2025-06-04 04:20:07.267 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:20:07.267 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] schema data cleaned 
[TRACE] 2025-06-04 04:20:07.268 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] monitor closed 
[TRACE] 2025-06-04 04:20:07.268 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] close complete, cost 3 ms 
[TRACE] 2025-06-04 04:20:07.443 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] running status set to false 
[TRACE] 2025-06-04 04:20:07.443 - [TAP-6799(100)][09028c31-8bb5-4e5e-a049-cf7048c80e5d] - Node 09028c31-8bb5-4e5e-a049-cf7048c80e5d[09028c31-8bb5-4e5e-a049-cf7048c80e5d] running status set to false 
[TRACE] 2025-06-04 04:20:07.443 - [TAP-6799(100)][09028c31-8bb5-4e5e-a049-cf7048c80e5d] - Node 09028c31-8bb5-4e5e-a049-cf7048c80e5d[09028c31-8bb5-4e5e-a049-cf7048c80e5d] schema data cleaned 
[TRACE] 2025-06-04 04:20:07.444 - [TAP-6799(100)][09028c31-8bb5-4e5e-a049-cf7048c80e5d] - Node 09028c31-8bb5-4e5e-a049-cf7048c80e5d[09028c31-8bb5-4e5e-a049-cf7048c80e5d] monitor closed 
[TRACE] 2025-06-04 04:20:07.444 - [TAP-6799(100)][09028c31-8bb5-4e5e-a049-cf7048c80e5d] - Node 09028c31-8bb5-4e5e-a049-cf7048c80e5d[09028c31-8bb5-4e5e-a049-cf7048c80e5d] close complete, cost 0 ms 
[INFO ] 2025-06-04 04:20:07.445 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-edde82d0-db8e-42cf-86c1-68decf24a1e1 
[INFO ] 2025-06-04 04:20:07.445 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-edde82d0-db8e-42cf-86c1-68decf24a1e1 
[INFO ] 2025-06-04 04:20:07.445 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-1674c3ea-16fc-4674-9a27-0cd5951e0f48-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:20:07.445 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] schema data cleaned 
[TRACE] 2025-06-04 04:20:07.445 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] monitor closed 
[TRACE] 2025-06-04 04:20:07.445 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] close complete, cost 2 ms 
[TRACE] 2025-06-04 04:20:07.446 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 04:20:07.446 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 04:20:07.446 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 04:20:07.446 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 04:20:09.558 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:09.558 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:09.558 - [TAP-6799(100)][5b63de86-64ce-407f-a045-9c70ab5d8fbd] - Node 5b63de86-64ce-407f-a045-9c70ab5d8fbd[5b63de86-64ce-407f-a045-9c70ab5d8fbd] start preload schema,table counts: 0 
[TRACE] 2025-06-04 04:20:09.558 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:09.558 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:09.558 - [TAP-6799(100)][5b63de86-64ce-407f-a045-9c70ab5d8fbd] - Node 5b63de86-64ce-407f-a045-9c70ab5d8fbd[5b63de86-64ce-407f-a045-9c70ab5d8fbd] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:09.558 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:09.559 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:09.559 - [TAP-6799(100)][MDM_packages] - Node field_add_del_processor(MDM_packages: 3080ce1e-1a69-43e1-8283-6bfbdbad03d9) enable batch process 
[TRACE] 2025-06-04 04:20:09.559 - [TAP-6799(100)][MDM_packages] - Node js_processor(MDM_packages: 8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d) enable batch process 
[TRACE] 2025-06-04 04:20:09.673 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] running status set to false 
[TRACE] 2025-06-04 04:20:09.674 - [TAP-6799(100)][MDM_packages] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749010809580 
[TRACE] 2025-06-04 04:20:09.674 - [TAP-6799(100)][MDM_packages] - PDK connector node released: HazelcastSampleSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749010809580 
[TRACE] 2025-06-04 04:20:09.674 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] schema data cleaned 
[TRACE] 2025-06-04 04:20:09.675 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] monitor closed 
[TRACE] 2025-06-04 04:20:09.679 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] close complete, cost 1 ms 
[TRACE] 2025-06-04 04:20:09.679 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] running status set to false 
[TRACE] 2025-06-04 04:20:09.700 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] schema data cleaned 
[TRACE] 2025-06-04 04:20:09.700 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] monitor closed 
[TRACE] 2025-06-04 04:20:09.700 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] close complete, cost 21 ms 
[TRACE] 2025-06-04 04:20:09.848 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] running status set to false 
[INFO ] 2025-06-04 04:20:09.848 - [TAP-6799(100)][MDM_packages][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-64d62842-4df9-4fc9-a60c-f410a21769e2 
[INFO ] 2025-06-04 04:20:09.848 - [TAP-6799(100)][MDM_packages][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-64d62842-4df9-4fc9-a60c-f410a21769e2 
[INFO ] 2025-06-04 04:20:09.848 - [TAP-6799(100)][MDM_packages][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:20:09.849 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] schema data cleaned 
[TRACE] 2025-06-04 04:20:09.849 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] monitor closed 
[TRACE] 2025-06-04 04:20:09.849 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] close complete, cost 2 ms 
[TRACE] 2025-06-04 04:20:09.859 - [TAP-6799(100)][5b63de86-64ce-407f-a045-9c70ab5d8fbd] - Node 5b63de86-64ce-407f-a045-9c70ab5d8fbd[5b63de86-64ce-407f-a045-9c70ab5d8fbd] running status set to false 
[TRACE] 2025-06-04 04:20:09.859 - [TAP-6799(100)][5b63de86-64ce-407f-a045-9c70ab5d8fbd] - Node 5b63de86-64ce-407f-a045-9c70ab5d8fbd[5b63de86-64ce-407f-a045-9c70ab5d8fbd] schema data cleaned 
[TRACE] 2025-06-04 04:20:09.859 - [TAP-6799(100)][5b63de86-64ce-407f-a045-9c70ab5d8fbd] - Node 5b63de86-64ce-407f-a045-9c70ab5d8fbd[5b63de86-64ce-407f-a045-9c70ab5d8fbd] monitor closed 
[TRACE] 2025-06-04 04:20:09.859 - [TAP-6799(100)][5b63de86-64ce-407f-a045-9c70ab5d8fbd] - Node 5b63de86-64ce-407f-a045-9c70ab5d8fbd[5b63de86-64ce-407f-a045-9c70ab5d8fbd] close complete, cost 0 ms 
[TRACE] 2025-06-04 04:20:09.860 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 04:20:09.860 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 04:20:09.860 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 04:20:09.860 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 04:20:09.927 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:09.927 - [TAP-6799(100)][21433e6b-d312-4a39-a72e-8728ed7bdf4e] - Node 21433e6b-d312-4a39-a72e-8728ed7bdf4e[21433e6b-d312-4a39-a72e-8728ed7bdf4e] start preload schema,table counts: 0 
[TRACE] 2025-06-04 04:20:09.927 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:09.927 - [TAP-6799(100)][21433e6b-d312-4a39-a72e-8728ed7bdf4e] - Node 21433e6b-d312-4a39-a72e-8728ed7bdf4e[21433e6b-d312-4a39-a72e-8728ed7bdf4e] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:09.927 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:09.927 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:09.927 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 73fce465-47ee-46f4-abb7-173648d43889) enable batch process 
[TRACE] 2025-06-04 04:20:10.062 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] running status set to false 
[TRACE] 2025-06-04 04:20:10.062 - [TAP-6799(100)][FDM_product] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010809964 
[TRACE] 2025-06-04 04:20:10.062 - [TAP-6799(100)][FDM_product] - PDK connector node released: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010809964 
[TRACE] 2025-06-04 04:20:10.062 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] schema data cleaned 
[TRACE] 2025-06-04 04:20:10.062 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] monitor closed 
[TRACE] 2025-06-04 04:20:10.062 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] close complete, cost 2 ms 
[TRACE] 2025-06-04 04:20:10.222 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] running status set to false 
[TRACE] 2025-06-04 04:20:10.222 - [TAP-6799(100)][21433e6b-d312-4a39-a72e-8728ed7bdf4e] - Node 21433e6b-d312-4a39-a72e-8728ed7bdf4e[21433e6b-d312-4a39-a72e-8728ed7bdf4e] running status set to false 
[TRACE] 2025-06-04 04:20:10.222 - [TAP-6799(100)][21433e6b-d312-4a39-a72e-8728ed7bdf4e] - Node 21433e6b-d312-4a39-a72e-8728ed7bdf4e[21433e6b-d312-4a39-a72e-8728ed7bdf4e] schema data cleaned 
[TRACE] 2025-06-04 04:20:10.222 - [TAP-6799(100)][21433e6b-d312-4a39-a72e-8728ed7bdf4e] - Node 21433e6b-d312-4a39-a72e-8728ed7bdf4e[21433e6b-d312-4a39-a72e-8728ed7bdf4e] monitor closed 
[TRACE] 2025-06-04 04:20:10.223 - [TAP-6799(100)][21433e6b-d312-4a39-a72e-8728ed7bdf4e] - Node 21433e6b-d312-4a39-a72e-8728ed7bdf4e[21433e6b-d312-4a39-a72e-8728ed7bdf4e] close complete, cost 0 ms 
[INFO ] 2025-06-04 04:20:10.223 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-44c5e9b2-fb77-4d24-9530-1631fa8fbe3d 
[INFO ] 2025-06-04 04:20:10.223 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-44c5e9b2-fb77-4d24-9530-1631fa8fbe3d 
[INFO ] 2025-06-04 04:20:10.223 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:20:10.224 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] schema data cleaned 
[TRACE] 2025-06-04 04:20:10.224 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] monitor closed 
[TRACE] 2025-06-04 04:20:10.224 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] close complete, cost 2 ms 
[TRACE] 2025-06-04 04:20:10.224 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 04:20:10.224 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 04:20:10.225 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 04:20:10.225 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 04:20:10.298 - [TAP-6799(100)][a5ede849-34da-4369-b0d1-7d96b9bbccc7] - Node a5ede849-34da-4369-b0d1-7d96b9bbccc7[a5ede849-34da-4369-b0d1-7d96b9bbccc7] start preload schema,table counts: 0 
[TRACE] 2025-06-04 04:20:10.298 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:10.298 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:10.298 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:10.298 - [TAP-6799(100)][a5ede849-34da-4369-b0d1-7d96b9bbccc7] - Node a5ede849-34da-4369-b0d1-7d96b9bbccc7[a5ede849-34da-4369-b0d1-7d96b9bbccc7] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:10.298 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:10.298 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:10.299 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:10.299 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 1674c3ea-16fc-4674-9a27-0cd5951e0f48) enable batch process 
[TRACE] 2025-06-04 04:20:10.432 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 73fce465-47ee-46f4-abb7-173648d43889) enable batch process 
[TRACE] 2025-06-04 04:20:10.433 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] running status set to false 
[TRACE] 2025-06-04 04:20:10.435 - [TAP-6799(100)][FDM_product] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010810337 
[TRACE] 2025-06-04 04:20:10.435 - [TAP-6799(100)][FDM_product] - PDK connector node released: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010810337 
[TRACE] 2025-06-04 04:20:10.435 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] schema data cleaned 
[TRACE] 2025-06-04 04:20:10.435 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] monitor closed 
[TRACE] 2025-06-04 04:20:10.435 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] close complete, cost 3 ms 
[TRACE] 2025-06-04 04:20:10.604 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] running status set to false 
[INFO ] 2025-06-04 04:20:10.605 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-a28adca9-3cfe-43be-9933-dab247958936 
[INFO ] 2025-06-04 04:20:10.605 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-a28adca9-3cfe-43be-9933-dab247958936 
[INFO ] 2025-06-04 04:20:10.605 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:20:10.605 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] schema data cleaned 
[TRACE] 2025-06-04 04:20:10.606 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] monitor closed 
[TRACE] 2025-06-04 04:20:10.606 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] close complete, cost 4 ms 
[TRACE] 2025-06-04 04:20:10.747 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] running status set to false 
[INFO ] 2025-06-04 04:20:10.748 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-43817581-d946-4ba3-a8fc-fb7cdeec1175 
[INFO ] 2025-06-04 04:20:10.748 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-43817581-d946-4ba3-a8fc-fb7cdeec1175 
[INFO ] 2025-06-04 04:20:10.748 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-1674c3ea-16fc-4674-9a27-0cd5951e0f48-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:20:10.749 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] schema data cleaned 
[TRACE] 2025-06-04 04:20:10.749 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] monitor closed 
[TRACE] 2025-06-04 04:20:10.750 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] close complete, cost 1 ms 
[TRACE] 2025-06-04 04:20:10.750 - [TAP-6799(100)][a5ede849-34da-4369-b0d1-7d96b9bbccc7] - Node a5ede849-34da-4369-b0d1-7d96b9bbccc7[a5ede849-34da-4369-b0d1-7d96b9bbccc7] running status set to false 
[TRACE] 2025-06-04 04:20:10.750 - [TAP-6799(100)][a5ede849-34da-4369-b0d1-7d96b9bbccc7] - Node a5ede849-34da-4369-b0d1-7d96b9bbccc7[a5ede849-34da-4369-b0d1-7d96b9bbccc7] schema data cleaned 
[TRACE] 2025-06-04 04:20:10.750 - [TAP-6799(100)][a5ede849-34da-4369-b0d1-7d96b9bbccc7] - Node a5ede849-34da-4369-b0d1-7d96b9bbccc7[a5ede849-34da-4369-b0d1-7d96b9bbccc7] monitor closed 
[TRACE] 2025-06-04 04:20:10.751 - [TAP-6799(100)][a5ede849-34da-4369-b0d1-7d96b9bbccc7] - Node a5ede849-34da-4369-b0d1-7d96b9bbccc7[a5ede849-34da-4369-b0d1-7d96b9bbccc7] close complete, cost 0 ms 
[TRACE] 2025-06-04 04:20:10.751 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 04:20:10.751 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 04:20:10.751 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 04:20:10.751 - [TAP-6799(100)] - Stopped task aspect(s) 
