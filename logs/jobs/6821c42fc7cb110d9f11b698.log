[TRACE] 2025-05-12 09:49:48.886 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353] - Task initialization... 
[TRACE] 2025-05-12 09:49:49.046 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353] - Start task milestones: 6821c42fc7cb110d9f11b698(t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353) 
[INFO ] 2025-05-12 09:49:49.046 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353] - Loading table structure completed 
[TRACE] 2025-05-12 09:49:49.197 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353] - Node performs snapshot read asynchronously 
[TRACE] 2025-05-12 09:49:49.313 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353] - The engine receives t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-05-12 09:49:49.313 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353] - Task started 
[TRACE] 2025-05-12 09:49:49.330 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[9105de9f-1dbf-43aa-9b72-ed5d2d57a7a0] start preload schema,table counts: 1 
[TRACE] 2025-05-12 09:49:49.331 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - Node qa_mongodb_repl_42240_share_1717403468657_3537[140865b5-1f96-48bc-b160-dfcecd47d93d] start preload schema,table counts: 1 
[TRACE] 2025-05-12 09:49:49.331 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[9105de9f-1dbf-43aa-9b72-ed5d2d57a7a0] preload schema finished, cost 0 ms 
[TRACE] 2025-05-12 09:49:49.331 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - Node qa_mongodb_repl_42240_share_1717403468657_3537[140865b5-1f96-48bc-b160-dfcecd47d93d] preload schema finished, cost 0 ms 
[INFO ] 2025-05-12 09:49:50.134 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_6040_1717403468657_3537] - Sink connector(qa_mongodb_repl_6040_1717403468657_3537) initialization completed 
[TRACE] 2025-05-12 09:49:50.134 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_6040_1717403468657_3537] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-05-12 09:49:50.182 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_6040_1717403468657_3537] - Apply table structure to target database 
[INFO ] 2025-05-12 09:49:50.182 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - Source connector(qa_mongodb_repl_42240_share_1717403468657_3537) initialization completed 
[TRACE] 2025-05-12 09:49:50.182 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - Source node "qa_mongodb_repl_42240_share_1717403468657_3537" read batch size: 500 
[TRACE] 2025-05-12 09:49:50.182 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - Source node "qa_mongodb_repl_42240_share_1717403468657_3537" event queue capacity: 1000 
[TRACE] 2025-05-12 09:49:50.182 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-05-12 09:49:50.346 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - Use existing stream offset: {"cdcOffset":1747043390,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2025-05-12 09:49:50.347 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - Starting batch read from 1 tables 
[TRACE] 2025-05-12 09:49:50.350 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - Initial sync started 
[INFO ] 2025-05-12 09:49:50.350 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - Starting batch read from table: t1 
[TRACE] 2025-05-12 09:49:50.350 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - Table t1 is going to be initial synced 
[TRACE] 2025-05-12 09:49:50.369 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - Query snapshot row size completed: qa_mongodb_repl_42240_share_1717403468657_3537(140865b5-1f96-48bc-b160-dfcecd47d93d) 
[INFO ] 2025-05-12 09:49:50.369 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - Table t1 has been completed batch read 
[TRACE] 2025-05-12 09:49:50.369 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - Initial sync completed 
[INFO ] 2025-05-12 09:49:50.370 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - Batch read completed. 
[TRACE] 2025-05-12 09:49:50.371 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - Incremental sync starting... 
[TRACE] 2025-05-12 09:49:50.371 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - Initial sync completed 
[INFO ] 2025-05-12 09:49:50.463 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2025-05-12 09:49:50.463 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - [Share CDC Task HZ Reader] - Step 1 - Check connection qa_mongodb_repl_42240_share_1717403468657_3537 enable share cdc: true 
[INFO ] 2025-05-12 09:49:50.463 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - [Share CDC Task HZ Reader] - Step 2 - Check task t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353 enable share cdc: true 
[INFO ] 2025-05-12 09:49:50.599 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537 
[INFO ] 2025-05-12 09:49:50.605 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost/tapdata_taptest_ci_jdk17', table='null', ttlDay=0] 
[INFO ] 2025-05-12 09:49:50.605 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - [Share CDC Task HZ Reader] - Step 6 - Init read thread pool completed 
[INFO ] 2025-05-12 09:49:50.605 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - Init share cdc reader completed 
[INFO ] 2025-05-12 09:49:50.606 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - Starting incremental sync using share log storage mode 
[INFO ] 2025-05-12 09:49:50.606 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2025-05-12 09:49:50.606 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - [Share CDC Task HZ Reader] - Read table count: 1, partition size: 1, read thread number: 1 
[INFO ] 2025-05-12 09:49:50.805 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6821c4393a705b6e41793e4a, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6821bcc1c7cb110d9f11b68a_t1, version=v2, tableName=t1, externalStorageTableName=ExternalStorage_SHARE_CDC_88906171, shareCdcTaskId=6821c439c7cb110d9f11b6ad, connectionId=6821bcc1c7cb110d9f11b68a) 
[INFO ] 2025-05-12 09:49:50.805 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537_t1_t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353, external storage name: ExternalStorage_SHARE_CDC_88906171 
[INFO ] 2025-05-12 09:49:50.807 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [t1] 
[INFO ] 2025-05-12 09:49:50.807 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - [Share CDC Task HZ Reader] - Find sequence in construct(t1) by timestamp(2025-05-12T09:49:50.182Z): 0 
[TRACE] 2025-05-12 09:49:50.807 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - Connector MongoDB incremental start succeed, tables: [t1], data change syncing 
[INFO ] 2025-05-12 09:49:50.807 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - [Share CDC Task HZ Reader] - Starting read 't1' log, sequence: 0 
[INFO ] 2025-05-12 09:49:51.008 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - [Share CDC Task HZ Reader] - Find by t1 filter: {sequence=0} 
[TRACE] 2025-05-12 09:49:56.196 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_6040_1717403468657_3537] - Process after table "t1" initial sync finished, cost: 0 ms 
[INFO ] 2025-05-12 09:49:56.196 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_6040_1717403468657_3537] - Process after all table(s) initial sync are finished，table number: 1 
[TRACE] 2025-05-12 09:59:31.157 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - Node qa_mongodb_repl_42240_share_1717403468657_3537[140865b5-1f96-48bc-b160-dfcecd47d93d] running status set to false 
[TRACE] 2025-05-12 09:59:31.158 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - Incremental sync completed 
[TRACE] 2025-05-12 09:59:31.161 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - PDK connector node stopped: HazelcastSourcePdkDataNode_140865b5-1f96-48bc-b160-dfcecd47d93d_1747043390059 
[TRACE] 2025-05-12 09:59:31.161 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - PDK connector node released: HazelcastSourcePdkDataNode_140865b5-1f96-48bc-b160-dfcecd47d93d_1747043390059 
[TRACE] 2025-05-12 09:59:31.161 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - Node qa_mongodb_repl_42240_share_1717403468657_3537[140865b5-1f96-48bc-b160-dfcecd47d93d] schema data cleaned 
[TRACE] 2025-05-12 09:59:31.162 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - Node qa_mongodb_repl_42240_share_1717403468657_3537[140865b5-1f96-48bc-b160-dfcecd47d93d] monitor closed 
[TRACE] 2025-05-12 09:59:31.162 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - Node qa_mongodb_repl_42240_share_1717403468657_3537[140865b5-1f96-48bc-b160-dfcecd47d93d] close complete, cost 5 ms 
[TRACE] 2025-05-12 09:59:31.162 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[9105de9f-1dbf-43aa-9b72-ed5d2d57a7a0] running status set to false 
[TRACE] 2025-05-12 09:59:31.176 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_6040_1717403468657_3537] - PDK connector node stopped: HazelcastTargetPdkDataNode_9105de9f-1dbf-43aa-9b72-ed5d2d57a7a0_1747043390005 
[TRACE] 2025-05-12 09:59:31.176 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_6040_1717403468657_3537] - PDK connector node released: HazelcastTargetPdkDataNode_9105de9f-1dbf-43aa-9b72-ed5d2d57a7a0_1747043390005 
[TRACE] 2025-05-12 09:59:31.176 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[9105de9f-1dbf-43aa-9b72-ed5d2d57a7a0] schema data cleaned 
[TRACE] 2025-05-12 09:59:31.176 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[9105de9f-1dbf-43aa-9b72-ed5d2d57a7a0] monitor closed 
[TRACE] 2025-05-12 09:59:31.379 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[9105de9f-1dbf-43aa-9b72-ed5d2d57a7a0] close complete, cost 32 ms 
[TRACE] 2025-05-12 09:59:33.528 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-05-12 09:59:33.530 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@6d00f43a 
[TRACE] 2025-05-12 09:59:33.530 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353] - Stop task milestones: 6821c42fc7cb110d9f11b698(t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353)  
[TRACE] 2025-05-12 09:59:33.646 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353] - Stopped task aspect(s) 
[TRACE] 2025-05-12 09:59:33.646 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353] - Snapshot order controller have been removed 
[INFO ] 2025-05-12 09:59:33.646 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353] - Task stopped. 
[TRACE] 2025-05-12 09:59:33.701 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353] - Remove memory task client succeed, task: t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353[6821c42fc7cb110d9f11b698] 
[TRACE] 2025-05-12 09:59:33.701 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353] - Destroy memory task client cache succeed, task: t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353[6821c42fc7cb110d9f11b698] 
[TRACE] 2025-05-12 09:59:37.383 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353] - Task initialization... 
[TRACE] 2025-05-12 09:59:37.445 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353] - Start task milestones: 6821c42fc7cb110d9f11b698(t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353) 
[INFO ] 2025-05-12 09:59:37.445 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353] - Loading table structure completed 
[TRACE] 2025-05-12 09:59:37.578 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353] - Node performs snapshot read asynchronously 
[TRACE] 2025-05-12 09:59:37.578 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353] - The engine receives t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-05-12 09:59:37.612 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353] - Task started 
[TRACE] 2025-05-12 09:59:37.612 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[9105de9f-1dbf-43aa-9b72-ed5d2d57a7a0] start preload schema,table counts: 1 
[TRACE] 2025-05-12 09:59:37.613 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[9105de9f-1dbf-43aa-9b72-ed5d2d57a7a0] preload schema finished, cost 0 ms 
[TRACE] 2025-05-12 09:59:37.615 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - Node qa_mongodb_repl_42240_share_1717403468657_3537[140865b5-1f96-48bc-b160-dfcecd47d93d] start preload schema,table counts: 1 
[TRACE] 2025-05-12 09:59:37.615 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - Node qa_mongodb_repl_42240_share_1717403468657_3537[140865b5-1f96-48bc-b160-dfcecd47d93d] preload schema finished, cost 0 ms 
[INFO ] 2025-05-12 09:59:37.760 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_6040_1717403468657_3537] - Sink connector(qa_mongodb_repl_6040_1717403468657_3537) initialization completed 
[TRACE] 2025-05-12 09:59:37.760 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_6040_1717403468657_3537] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-05-12 09:59:37.828 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_6040_1717403468657_3537] - Apply table structure to target database 
[INFO ] 2025-05-12 09:59:37.828 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - Source connector(qa_mongodb_repl_42240_share_1717403468657_3537) initialization completed 
[TRACE] 2025-05-12 09:59:37.829 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - Source node "qa_mongodb_repl_42240_share_1717403468657_3537" read batch size: 500 
[TRACE] 2025-05-12 09:59:37.829 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - Source node "qa_mongodb_repl_42240_share_1717403468657_3537" event queue capacity: 1000 
[INFO ] 2025-05-12 09:59:37.829 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2025-05-12 09:59:37.829 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - Use existing batch read offset: {"t1":{"batch_read_connector_status":"OVER"}}, Use existing stream offset: {"cdcOffset":1747043390,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2025-05-12 09:59:37.900 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - Batch read completed. 
[TRACE] 2025-05-12 09:59:37.900 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - Incremental sync starting... 
[TRACE] 2025-05-12 09:59:37.900 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - Initial sync completed 
[INFO ] 2025-05-12 09:59:37.929 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2025-05-12 09:59:37.929 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - [Share CDC Task HZ Reader] - Step 1 - Check connection qa_mongodb_repl_42240_share_1717403468657_3537 enable share cdc: true 
[INFO ] 2025-05-12 09:59:37.929 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - [Share CDC Task HZ Reader] - Step 2 - Check task t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353 enable share cdc: true 
[INFO ] 2025-05-12 09:59:37.951 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537 
[INFO ] 2025-05-12 09:59:37.951 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost/tapdata_taptest_ci_jdk17', table='null', ttlDay=0] 
[INFO ] 2025-05-12 09:59:37.951 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - [Share CDC Task HZ Reader] - Step 6 - Init read thread pool completed 
[INFO ] 2025-05-12 09:59:37.952 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - Init share cdc reader completed 
[INFO ] 2025-05-12 09:59:37.952 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - Starting incremental sync using share log storage mode 
[INFO ] 2025-05-12 09:59:37.952 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2025-05-12 09:59:37.952 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - [Share CDC Task HZ Reader] - Read table count: 1, partition size: 1, read thread number: 1 
[INFO ] 2025-05-12 10:00:22.769 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6821c4393a705b6e41793e4a, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6821bcc1c7cb110d9f11b68a_t1, version=v2, tableName=t1, externalStorageTableName=ExternalStorage_SHARE_CDC_88906171, shareCdcTaskId=6821c439c7cb110d9f11b6ad, connectionId=6821bcc1c7cb110d9f11b68a) 
[INFO ] 2025-05-12 10:00:22.769 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537_t1_t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353, external storage name: ExternalStorage_SHARE_CDC_88906171 
[INFO ] 2025-05-12 10:00:22.769 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [t1] 
[INFO ] 2025-05-12 10:00:22.771 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - [Share CDC Task HZ Reader] - Find sequence in construct(t1) by timestamp(2025-05-12T09:49:50.182Z): 0 
[TRACE] 2025-05-12 10:00:22.775 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - Connector MongoDB incremental start succeed, tables: [t1], data change syncing 
[INFO ] 2025-05-12 10:00:22.775 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - [Share CDC Task HZ Reader] - Starting read 't1' log, sequence: 0 
[INFO ] 2025-05-12 10:00:22.775 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - [Share CDC Task HZ Reader] - Find by t1 filter: {sequence=0} 
[TRACE] 2025-05-12 10:11:16.114 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - Node qa_mongodb_repl_42240_share_1717403468657_3537[140865b5-1f96-48bc-b160-dfcecd47d93d] running status set to false 
[TRACE] 2025-05-12 10:11:16.115 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - Incremental sync completed 
[TRACE] 2025-05-12 10:11:16.117 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - PDK connector node stopped: HazelcastSourcePdkDataNode_140865b5-1f96-48bc-b160-dfcecd47d93d_1747043977656 
[TRACE] 2025-05-12 10:11:16.117 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - PDK connector node released: HazelcastSourcePdkDataNode_140865b5-1f96-48bc-b160-dfcecd47d93d_1747043977656 
[TRACE] 2025-05-12 10:11:16.117 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - Node qa_mongodb_repl_42240_share_1717403468657_3537[140865b5-1f96-48bc-b160-dfcecd47d93d] schema data cleaned 
[TRACE] 2025-05-12 10:11:16.118 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - Node qa_mongodb_repl_42240_share_1717403468657_3537[140865b5-1f96-48bc-b160-dfcecd47d93d] monitor closed 
[TRACE] 2025-05-12 10:11:16.118 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_42240_share_1717403468657_3537] - Node qa_mongodb_repl_42240_share_1717403468657_3537[140865b5-1f96-48bc-b160-dfcecd47d93d] close complete, cost 4 ms 
[TRACE] 2025-05-12 10:11:16.118 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[9105de9f-1dbf-43aa-9b72-ed5d2d57a7a0] running status set to false 
[TRACE] 2025-05-12 10:11:16.119 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_6040_1717403468657_3537] - PDK connector node stopped: HazelcastTargetPdkDataNode_9105de9f-1dbf-43aa-9b72-ed5d2d57a7a0_1747043977633 
[TRACE] 2025-05-12 10:11:16.119 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_6040_1717403468657_3537] - PDK connector node released: HazelcastTargetPdkDataNode_9105de9f-1dbf-43aa-9b72-ed5d2d57a7a0_1747043977633 
[TRACE] 2025-05-12 10:11:16.119 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[9105de9f-1dbf-43aa-9b72-ed5d2d57a7a0] schema data cleaned 
[TRACE] 2025-05-12 10:11:16.120 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[9105de9f-1dbf-43aa-9b72-ed5d2d57a7a0] monitor closed 
[TRACE] 2025-05-12 10:11:16.320 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747043353][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[9105de9f-1dbf-43aa-9b72-ed5d2d57a7a0] close complete, cost 1 ms 
