[TRACE] 2025-05-13 02:45:52.435 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319] - Task initialization... 
[TRACE] 2025-05-13 02:45:52.435 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319] - Start task milestones: 6822b2554c4e56217558ef6a(t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319) 
[INFO ] 2025-05-13 02:45:52.638 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319] - Loading table structure completed 
[TRACE] 2025-05-13 02:45:52.715 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319] - Node performs snapshot read asynchronously 
[TRACE] 2025-05-13 02:45:52.715 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319] - The engine receives t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-05-13 02:45:52.738 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319] - Task started 
[TRACE] 2025-05-13 02:45:52.738 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319][qa_mongodb_repl_42240_share_1717403468657_3537] - Node qa_mongodb_repl_42240_share_1717403468657_3537[f6f5ed02-b6fd-439d-b1da-6c407c34eb81] start preload schema,table counts: 1 
[TRACE] 2025-05-13 02:45:52.738 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319][qa_mongodb_repl_42240_share_1717403468657_3537] - Node qa_mongodb_repl_42240_share_1717403468657_3537[f6f5ed02-b6fd-439d-b1da-6c407c34eb81] preload schema finished, cost 0 ms 
[TRACE] 2025-05-13 02:45:52.738 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[654f3e68-5681-47b0-b9c1-8590b436ecf0] start preload schema,table counts: 1 
[TRACE] 2025-05-13 02:45:52.738 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[654f3e68-5681-47b0-b9c1-8590b436ecf0] preload schema finished, cost 0 ms 
[INFO ] 2025-05-13 02:45:53.252 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319][qa_mongodb_repl_6040_1717403468657_3537] - Sink connector(qa_mongodb_repl_6040_1717403468657_3537) initialization completed 
[TRACE] 2025-05-13 02:45:53.252 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319][qa_mongodb_repl_6040_1717403468657_3537] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-05-13 02:45:53.317 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319][qa_mongodb_repl_6040_1717403468657_3537] - Apply table structure to target database 
[INFO ] 2025-05-13 02:45:53.317 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319][qa_mongodb_repl_42240_share_1717403468657_3537] - Source connector(qa_mongodb_repl_42240_share_1717403468657_3537) initialization completed 
[TRACE] 2025-05-13 02:45:53.317 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319][qa_mongodb_repl_42240_share_1717403468657_3537] - Source node "qa_mongodb_repl_42240_share_1717403468657_3537" read batch size: 500 
[TRACE] 2025-05-13 02:45:53.317 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319][qa_mongodb_repl_42240_share_1717403468657_3537] - Source node "qa_mongodb_repl_42240_share_1717403468657_3537" event queue capacity: 1000 
[TRACE] 2025-05-13 02:45:53.317 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319][qa_mongodb_repl_42240_share_1717403468657_3537] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-05-13 02:45:53.399 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319][qa_mongodb_repl_42240_share_1717403468657_3537] - Use existing stream offset: {"cdcOffset":1747104352,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2025-05-13 02:45:53.399 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319][qa_mongodb_repl_42240_share_1717403468657_3537] - Starting batch read from 1 tables 
[TRACE] 2025-05-13 02:45:53.404 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319][qa_mongodb_repl_42240_share_1717403468657_3537] - Initial sync started 
[INFO ] 2025-05-13 02:45:53.404 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319][qa_mongodb_repl_42240_share_1717403468657_3537] - Starting batch read from table: t1 
[TRACE] 2025-05-13 02:45:53.404 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319][qa_mongodb_repl_42240_share_1717403468657_3537] - Table t1 is going to be initial synced 
[TRACE] 2025-05-13 02:45:53.408 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319][qa_mongodb_repl_42240_share_1717403468657_3537] - Query snapshot row size completed: qa_mongodb_repl_42240_share_1717403468657_3537(f6f5ed02-b6fd-439d-b1da-6c407c34eb81) 
[INFO ] 2025-05-13 02:45:53.408 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319][qa_mongodb_repl_42240_share_1717403468657_3537] - Table t1 has been completed batch read 
[TRACE] 2025-05-13 02:45:53.408 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319][qa_mongodb_repl_42240_share_1717403468657_3537] - Initial sync completed 
[INFO ] 2025-05-13 02:45:53.408 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319][qa_mongodb_repl_42240_share_1717403468657_3537] - Batch read completed. 
[TRACE] 2025-05-13 02:45:53.408 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319][qa_mongodb_repl_42240_share_1717403468657_3537] - Incremental sync starting... 
[TRACE] 2025-05-13 02:45:53.408 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319][qa_mongodb_repl_42240_share_1717403468657_3537] - Initial sync completed 
[INFO ] 2025-05-13 02:45:53.450 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319][qa_mongodb_repl_42240_share_1717403468657_3537] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2025-05-13 02:45:53.450 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319][qa_mongodb_repl_42240_share_1717403468657_3537] - [Share CDC Task HZ Reader] - Step 1 - Check connection qa_mongodb_repl_42240_share_1717403468657_3537 enable share cdc: true 
[INFO ] 2025-05-13 02:45:53.450 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319][qa_mongodb_repl_42240_share_1717403468657_3537] - [Share CDC Task HZ Reader] - Step 2 - Check task t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319 enable share cdc: true 
[INFO ] 2025-05-13 02:45:53.470 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319][qa_mongodb_repl_42240_share_1717403468657_3537] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537 
[INFO ] 2025-05-13 02:45:53.470 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319][qa_mongodb_repl_42240_share_1717403468657_3537] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost/tapdata_taptest_ci_jdk17', table='null', ttlDay=0] 
[INFO ] 2025-05-13 02:45:53.471 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319][qa_mongodb_repl_42240_share_1717403468657_3537] - [Share CDC Task HZ Reader] - Step 6 - Init read thread pool completed 
[INFO ] 2025-05-13 02:45:53.471 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319][qa_mongodb_repl_42240_share_1717403468657_3537] - Init share cdc reader completed 
[INFO ] 2025-05-13 02:45:53.471 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319][qa_mongodb_repl_42240_share_1717403468657_3537] - Starting incremental sync using share log storage mode 
[INFO ] 2025-05-13 02:45:53.471 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319][qa_mongodb_repl_42240_share_1717403468657_3537] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2025-05-13 02:45:53.471 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319][qa_mongodb_repl_42240_share_1717403468657_3537] - [Share CDC Task HZ Reader] - Read table count: 1, partition size: 1, read thread number: 1 
[INFO ] 2025-05-13 02:45:53.478 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319][qa_mongodb_repl_42240_share_1717403468657_3537] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6822abe23a705b6e417d1e90, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6822a9434c4e56217558ec7d_t1, version=v2, tableName=t1, externalStorageTableName=ExternalStorage_SHARE_CDC_1306654423, shareCdcTaskId=6822a9924c4e56217558eca0, connectionId=6822a9434c4e56217558ec7d) 
[INFO ] 2025-05-13 02:45:53.478 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319][qa_mongodb_repl_42240_share_1717403468657_3537] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537_t1_t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319, external storage name: ExternalStorage_SHARE_CDC_1306654423 
[INFO ] 2025-05-13 02:45:53.480 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319][qa_mongodb_repl_42240_share_1717403468657_3537] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [t1] 
[INFO ] 2025-05-13 02:45:53.480 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319][qa_mongodb_repl_42240_share_1717403468657_3537] - [Share CDC Task HZ Reader] - Find sequence in construct(t1) by timestamp(2025-05-13T02:45:53.317Z): 21 
[TRACE] 2025-05-13 02:45:53.480 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319][qa_mongodb_repl_42240_share_1717403468657_3537] - Connector MongoDB incremental start succeed, tables: [t1], data change syncing 
[INFO ] 2025-05-13 02:45:53.480 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319][qa_mongodb_repl_42240_share_1717403468657_3537] - [Share CDC Task HZ Reader] - Starting read 't1' log, sequence: 21 
[INFO ] 2025-05-13 02:45:53.681 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319][qa_mongodb_repl_42240_share_1717403468657_3537] - [Share CDC Task HZ Reader] - Find by t1 filter: {sequence=21} 
[TRACE] 2025-05-13 02:45:59.297 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319][qa_mongodb_repl_6040_1717403468657_3537] - Process after table "t1" initial sync finished, cost: 0 ms 
[INFO ] 2025-05-13 02:45:59.297 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319][qa_mongodb_repl_6040_1717403468657_3537] - Process after all table(s) initial sync are finished，table number: 1 
[INFO ] 2025-05-13 02:46:17.120 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319][qa_mongodb_repl_42240_share_1717403468657_3537] - [Share CDC Task HZ Reader] - Successfully read first log data: Document{{fromTable=t1, timestamp=1747104373000, date=Tue May 13 02:46:13 GMT 2025, after=Document{{_id=[B@4c66f9c3, key=1, x=1, y=str, z=[B@1463eae}}, op=i, offsetString=_tap_encode_gAFkABVvcmcuYnNvbi5Cc29uRG9jdW1lbnQBFAAFX2RhdGEBAgATb3JnLmJzb24uQnNvblN0cmlu
ZwAAAdR7ImFycmF5IjpmYWxzZSwiYmluYXJ5IjpmYWxzZSwiYm9vbGVhbiI6ZmFsc2UsImJzb25U
eXBlIjoiU1RSSU5HIiwiZEJQb2ludGVyIjpmYWxzZSwiZGF0ZVRpbWUiOmZhbHNlLCJkZWNpbWFs
MTI4IjpmYWxzZSwiZG9jdW1lbnQiOmZhbHNlLCJkb3VibGUiOmZhbHNlLCJpbnQzMiI6ZmFsc2Us
ImludDY0IjpmYWxzZSwiamF2YVNjcmlwdCI6ZmFsc2UsImphdmFTY3JpcHRXaXRoU2NvcGUiOmZh
bHNlLCJudWxsIjpmYWxzZSwibnVtYmVyIjpmYWxzZSwib2JqZWN0SWQiOmZhbHNlLCJyZWd1bGFy
RXhwcmVzc2lvbiI6ZmFsc2UsInN0cmluZyI6dHJ1ZSwic3ltYm9sIjpmYWxzZSwidGltZXN0YW1w
IjpmYWxzZSwidmFsdWUiOiI4MjY4MjJCMjc1MDAwMDAwMDEyQjAyMkMwMTAwMjk2RTVBMTAwNDEz
MzQxNzNCNDM4QjQyQjRBN0ZEMDE0RTIyRDU3NjNBNDY2NDVGNjk2NDAwNjQ2ODIyQjI3NTYwNzQ4
QTFBNDBGMUNBMzcwMDA0In2o
, type=DATA, connectionId=6822a9434c4e56217558ec7d, isReplaceEvent=false, _ts=1747104374}} 
[TRACE] 2025-05-13 02:53:17.460 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319][qa_mongodb_repl_42240_share_1717403468657_3537] - Node qa_mongodb_repl_42240_share_1717403468657_3537[f6f5ed02-b6fd-439d-b1da-6c407c34eb81] running status set to false 
[TRACE] 2025-05-13 02:53:17.460 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319][qa_mongodb_repl_42240_share_1717403468657_3537] - Incremental sync completed 
[TRACE] 2025-05-13 02:53:17.464 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319][qa_mongodb_repl_42240_share_1717403468657_3537] - PDK connector node stopped: HazelcastSourcePdkDataNode_f6f5ed02-b6fd-439d-b1da-6c407c34eb81_1747104353180 
[TRACE] 2025-05-13 02:53:17.464 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319][qa_mongodb_repl_42240_share_1717403468657_3537] - PDK connector node released: HazelcastSourcePdkDataNode_f6f5ed02-b6fd-439d-b1da-6c407c34eb81_1747104353180 
[TRACE] 2025-05-13 02:53:17.465 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319][qa_mongodb_repl_42240_share_1717403468657_3537] - Node qa_mongodb_repl_42240_share_1717403468657_3537[f6f5ed02-b6fd-439d-b1da-6c407c34eb81] schema data cleaned 
[TRACE] 2025-05-13 02:53:17.465 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319][qa_mongodb_repl_42240_share_1717403468657_3537] - Node qa_mongodb_repl_42240_share_1717403468657_3537[f6f5ed02-b6fd-439d-b1da-6c407c34eb81] monitor closed 
[TRACE] 2025-05-13 02:53:17.466 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319][qa_mongodb_repl_42240_share_1717403468657_3537] - Node qa_mongodb_repl_42240_share_1717403468657_3537[f6f5ed02-b6fd-439d-b1da-6c407c34eb81] close complete, cost 12 ms 
[TRACE] 2025-05-13 02:53:17.466 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[654f3e68-5681-47b0-b9c1-8590b436ecf0] running status set to false 
[TRACE] 2025-05-13 02:53:17.473 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319][qa_mongodb_repl_6040_1717403468657_3537] - PDK connector node stopped: HazelcastTargetPdkDataNode_654f3e68-5681-47b0-b9c1-8590b436ecf0_1747104353155 
[TRACE] 2025-05-13 02:53:17.473 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319][qa_mongodb_repl_6040_1717403468657_3537] - PDK connector node released: HazelcastTargetPdkDataNode_654f3e68-5681-47b0-b9c1-8590b436ecf0_1747104353155 
[TRACE] 2025-05-13 02:53:17.473 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[654f3e68-5681-47b0-b9c1-8590b436ecf0] schema data cleaned 
[TRACE] 2025-05-13 02:53:17.473 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[654f3e68-5681-47b0-b9c1-8590b436ecf0] monitor closed 
[TRACE] 2025-05-13 02:53:17.474 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[654f3e68-5681-47b0-b9c1-8590b436ecf0] close complete, cost 7 ms 
[TRACE] 2025-05-13 02:53:20.636 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-05-13 02:53:20.638 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@6534a110 
[TRACE] 2025-05-13 02:53:20.638 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319] - Stop task milestones: 6822b2554c4e56217558ef6a(t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319)  
[TRACE] 2025-05-13 02:53:20.756 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319] - Stopped task aspect(s) 
[TRACE] 2025-05-13 02:53:20.756 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319] - Snapshot order controller have been removed 
[INFO ] 2025-05-13 02:53:20.756 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319] - Task stopped. 
[TRACE] 2025-05-13 02:53:20.838 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319] - Remove memory task client succeed, task: t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319[6822b2554c4e56217558ef6a] 
[TRACE] 2025-05-13 02:53:20.838 - [t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319] - Destroy memory task client cache succeed, task: t_80.1.1-1-mdb-v4_to_mdb-v6_share_with_check_data_1_1717403468657_3537-1747104319[6822b2554c4e56217558ef6a] 
