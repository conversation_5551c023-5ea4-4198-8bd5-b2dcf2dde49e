[TRACE] 2025-06-04 04:17:07.614 - [CDC log cache task from S Agent Local DaaS_1] - Start task milestones: 683e9f99b023c8285961dcff(CDC log cache task from S Agent Local DaaS_1) 
[INFO ] 2025-06-04 04:17:07.909 - [CDC log cache task from S Agent Local DaaS_1] - Loading table structure completed 
[TRACE] 2025-06-04 04:17:07.973 - [CDC log cache task from S Agent Local DaaS_1] - <PERSON><PERSON> performs snapshot read asynchronously 
[TRACE] 2025-06-04 04:17:07.974 - [CDC log cache task from S Agent Local DaaS_1] - The engine receives CDC log cache task from S Agent Local DaaS_1 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-04 04:17:08.153 - [CDC log cache task from S Agent Local DaaS_1] - Task started 
[TRACE] 2025-06-04 04:17:08.184 - [CDC log cache task from S Agent Local DaaS_1][S Agent Local DaaS_1] - Node S Agent Local DaaS_1[4e2c7948fbd6478aa4cf71cefa020474] start preload schema,table counts: 3 
[TRACE] 2025-06-04 04:17:08.185 - [CDC log cache task from S Agent Local DaaS_1][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[TRACE] 2025-06-04 04:17:08.185 - [CDC log cache task from S Agent Local DaaS_1][S Agent Local DaaS_1] - Node S Agent Local DaaS_1[4e2c7948fbd6478aa4cf71cefa020474] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:17:08.185 - [CDC log cache task from S Agent Local DaaS_1][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-06-04 04:17:08.209 - [CDC log cache task from S Agent Local DaaS_1][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=683e9f99fb36645c5d490cd0, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=63468098c87faf3ba64fece0_MDM_sizeSet, version=v2, tableName=MDM_sizeSet, externalStorageTableName=ExternalStorage_SHARE_CDC_982476727, shareCdcTaskId=683e9f99b023c8285961dcff, connectionId=63468098c87faf3ba64fece0) 
[INFO ] 2025-06-04 04:17:08.211 - [CDC log cache task from S Agent Local DaaS_1][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=683e9f99fb36645c5d490ccf, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=63468098c87faf3ba64fece0_FDM_product, version=v2, tableName=FDM_product, externalStorageTableName=ExternalStorage_SHARE_CDC_-1738770242, shareCdcTaskId=683e9f99b023c8285961dcff, connectionId=63468098c87faf3ba64fece0) 
[INFO ] 2025-06-04 04:17:08.216 - [CDC log cache task from S Agent Local DaaS_1][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=683eca61fb36645c5d4fabc7, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=63468098c87faf3ba64fece0_MDM_packages, version=v2, tableName=MDM_packages, externalStorageTableName=ExternalStorage_SHARE_CDC_295938199, shareCdcTaskId=683e9f99b023c8285961dcff, connectionId=63468098c87faf3ba64fece0) 
[INFO ] 2025-06-04 04:17:08.420 - [CDC log cache task from S Agent Local DaaS_1][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost/tapdata_v35_jdk17', table='null', ttlDay=3] 
[INFO ] 2025-06-04 04:17:08.512 - [CDC log cache task from S Agent Local DaaS_1][S Agent Local DaaS_1] - Source connector(S Agent Local DaaS_1) initialization completed 
[TRACE] 2025-06-04 04:17:08.512 - [CDC log cache task from S Agent Local DaaS_1][S Agent Local DaaS_1] - Source node "S Agent Local DaaS_1" read batch size: 100 
[TRACE] 2025-06-04 04:17:08.513 - [CDC log cache task from S Agent Local DaaS_1][S Agent Local DaaS_1] - Source node "S Agent Local DaaS_1" event queue capacity: 200 
[INFO ] 2025-06-04 04:17:08.513 - [CDC log cache task from S Agent Local DaaS_1][S Agent Local DaaS_1] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2025-06-04 04:17:08.519 - [CDC log cache task from S Agent Local DaaS_1][S Agent Local DaaS_1] - Use existing stream offset: {"_data":{"value":"82683ED17B000000022B042C0100296E5A10042FE9F35A9E87435A9BC5501E14E83E6B463C6F7065726174696F6E54797065003C7570646174650046646F63756D656E744B65790046645F6964006466D9BD15BC1FC9D7A02EC8E5000004","bsonType":"STRING","number":false,"array":false,"null":false,"double":false,"boolean":false,"binary":false,"decimal128":false,"dbpointer":false,"timestamp":false,"regularExpression":false,"javaScript":false,"javaScriptWithScope":false,"document":false,"string":true,"int32":false,"int64":false,"objectId":false,"dateTime":false,"symbol":false}} 
[INFO ] 2025-06-04 04:17:08.579 - [CDC log cache task from S Agent Local DaaS_1][S Agent Local DaaS_1] - Batch read completed. 
[TRACE] 2025-06-04 04:17:08.582 - [CDC log cache task from S Agent Local DaaS_1][S Agent Local DaaS_1] - Starting stream read, table list: [MDM_sizeSet, FDM_product, MDM_packages], offset: {"_data":{"value":"82683ED17B000000022B042C0100296E5A10042FE9F35A9E87435A9BC5501E14E83E6B463C6F7065726174696F6E54797065003C7570646174650046646F63756D656E744B65790046645F6964006466D9BD15BC1FC9D7A02EC8E5000004","bsonType":"STRING","number":false,"array":false,"null":false,"double":false,"boolean":false,"binary":false,"decimal128":false,"dbpointer":false,"timestamp":false,"regularExpression":false,"javaScript":false,"javaScriptWithScope":false,"document":false,"string":true,"int32":false,"int64":false,"objectId":false,"dateTime":false,"symbol":false}} 
[INFO ] 2025-06-04 04:17:08.582 - [CDC log cache task from S Agent Local DaaS_1][S Agent Local DaaS_1] - Starting incremental sync using database log parser 
[TRACE] 2025-06-04 04:17:08.820 - [CDC log cache task from S Agent Local DaaS_1][S Agent Local DaaS_1] - Connector MongoDB incremental start succeed, tables: [MDM_sizeSet, FDM_product, MDM_packages], data change syncing 
[TRACE] 2025-06-04 06:39:43.470 - [CDC log cache task from S Agent Local DaaS_1][S Agent Local DaaS_1] - Node S Agent Local DaaS_1[4e2c7948fbd6478aa4cf71cefa020474] running status set to false 
