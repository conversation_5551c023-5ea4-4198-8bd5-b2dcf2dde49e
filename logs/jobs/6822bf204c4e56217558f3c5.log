[TRACE] 2025-05-13 03:40:26.981 - [t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560] - Task initialization... 
[TRACE] 2025-05-13 03:40:27.046 - [t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560] - Start task milestones: 6822bf204c4e56217558f3c5(t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560) 
[INFO ] 2025-05-13 03:40:27.046 - [t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560] - Loading table structure completed 
[TRACE] 2025-05-13 03:40:27.166 - [t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560] - Node performs snapshot read asynchronously 
[TRACE] 2025-05-13 03:40:27.167 - [t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560] - The engine receives t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-05-13 03:40:27.190 - [t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560] - Task started 
[TRACE] 2025-05-13 03:40:27.190 - [t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560][qa_mongodb_repl_42240_share_1717403468657_3537] - Node qa_mongodb_repl_42240_share_1717403468657_3537[0fc9ec27-a283-4bfa-92d2-847c77865721] start preload schema,table counts: 1 
[TRACE] 2025-05-13 03:40:27.190 - [t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560][qa_mongodb_repl_42240_share_1717403468657_3537] - Node qa_mongodb_repl_42240_share_1717403468657_3537[0fc9ec27-a283-4bfa-92d2-847c77865721] preload schema finished, cost 0 ms 
[TRACE] 2025-05-13 03:40:27.190 - [t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[2b931a72-ba30-43f5-8952-d95205adf32a] start preload schema,table counts: 1 
[TRACE] 2025-05-13 03:40:27.190 - [t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[2b931a72-ba30-43f5-8952-d95205adf32a] preload schema finished, cost 0 ms 
[INFO ] 2025-05-13 03:40:27.760 - [t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560][qa_mongodb_repl_42240_share_1717403468657_3537] - Source connector(qa_mongodb_repl_42240_share_1717403468657_3537) initialization completed 
[TRACE] 2025-05-13 03:40:27.760 - [t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560][qa_mongodb_repl_42240_share_1717403468657_3537] - Source node "qa_mongodb_repl_42240_share_1717403468657_3537" read batch size: 500 
[INFO ] 2025-05-13 03:40:27.760 - [t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560][qa_mongodb_repl_6040_1717403468657_3537] - Sink connector(qa_mongodb_repl_6040_1717403468657_3537) initialization completed 
[TRACE] 2025-05-13 03:40:27.760 - [t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560][qa_mongodb_repl_42240_share_1717403468657_3537] - Source node "qa_mongodb_repl_42240_share_1717403468657_3537" event queue capacity: 1000 
[TRACE] 2025-05-13 03:40:27.760 - [t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560][qa_mongodb_repl_6040_1717403468657_3537] - Write batch size: 1000, max wait ms per batch: 1000 
[TRACE] 2025-05-13 03:40:27.760 - [t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560][qa_mongodb_repl_42240_share_1717403468657_3537] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-05-13 03:40:27.770 - [t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560][qa_mongodb_repl_6040_1717403468657_3537] - Apply table structure to target database 
[INFO ] 2025-05-13 03:40:27.770 - [t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560][qa_mongodb_repl_42240_share_1717403468657_3537] - Use existing stream offset: {"cdcOffset":1747107627,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2025-05-13 03:40:27.840 - [t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560][qa_mongodb_repl_42240_share_1717403468657_3537] - Starting batch read from 1 tables 
[TRACE] 2025-05-13 03:40:27.840 - [t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560][qa_mongodb_repl_42240_share_1717403468657_3537] - Initial sync started 
[INFO ] 2025-05-13 03:40:27.840 - [t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560][qa_mongodb_repl_42240_share_1717403468657_3537] - Starting batch read from table: t2_80_1_2 
[TRACE] 2025-05-13 03:40:27.840 - [t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560][qa_mongodb_repl_42240_share_1717403468657_3537] - Table t2_80_1_2 is going to be initial synced 
[TRACE] 2025-05-13 03:40:27.841 - [t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560][qa_mongodb_repl_42240_share_1717403468657_3537] - Query snapshot row size completed: qa_mongodb_repl_42240_share_1717403468657_3537(0fc9ec27-a283-4bfa-92d2-847c77865721) 
[INFO ] 2025-05-13 03:40:27.843 - [t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560][qa_mongodb_repl_42240_share_1717403468657_3537] - Table t2_80_1_2 has been completed batch read 
[TRACE] 2025-05-13 03:40:27.843 - [t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560][qa_mongodb_repl_42240_share_1717403468657_3537] - Initial sync completed 
[INFO ] 2025-05-13 03:40:27.843 - [t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560][qa_mongodb_repl_42240_share_1717403468657_3537] - Batch read completed. 
[TRACE] 2025-05-13 03:40:27.843 - [t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560][qa_mongodb_repl_42240_share_1717403468657_3537] - Incremental sync starting... 
[TRACE] 2025-05-13 03:40:27.844 - [t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560][qa_mongodb_repl_42240_share_1717403468657_3537] - Initial sync completed 
[INFO ] 2025-05-13 03:40:27.871 - [t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560][qa_mongodb_repl_42240_share_1717403468657_3537] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2025-05-13 03:40:27.871 - [t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560][qa_mongodb_repl_42240_share_1717403468657_3537] - [Share CDC Task HZ Reader] - Step 1 - Check connection qa_mongodb_repl_42240_share_1717403468657_3537 enable share cdc: true 
[INFO ] 2025-05-13 03:40:27.871 - [t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560][qa_mongodb_repl_42240_share_1717403468657_3537] - [Share CDC Task HZ Reader] - Step 2 - Check task t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560 enable share cdc: true 
[INFO ] 2025-05-13 03:40:27.901 - [t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560][qa_mongodb_repl_42240_share_1717403468657_3537] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537 
[INFO ] 2025-05-13 03:40:27.901 - [t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560][qa_mongodb_repl_42240_share_1717403468657_3537] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost/tapdata_taptest_ci_jdk17', table='null', ttlDay=0] 
[INFO ] 2025-05-13 03:40:27.901 - [t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560][qa_mongodb_repl_42240_share_1717403468657_3537] - [Share CDC Task HZ Reader] - Step 6 - Init read thread pool completed 
[INFO ] 2025-05-13 03:40:27.901 - [t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560][qa_mongodb_repl_42240_share_1717403468657_3537] - Init share cdc reader completed 
[INFO ] 2025-05-13 03:40:27.901 - [t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560][qa_mongodb_repl_42240_share_1717403468657_3537] - Starting incremental sync using share log storage mode 
[INFO ] 2025-05-13 03:40:27.901 - [t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560][qa_mongodb_repl_42240_share_1717403468657_3537] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2025-05-13 03:40:27.901 - [t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560][qa_mongodb_repl_42240_share_1717403468657_3537] - [Share CDC Task HZ Reader] - Read table count: 1, partition size: 1, read thread number: 1 
[INFO ] 2025-05-13 03:40:27.930 - [t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560][qa_mongodb_repl_42240_share_1717403468657_3537] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6822a9b33a705b6e417cbddc, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6822a9434c4e56217558ec7d_t2_80_1_2, version=v2, tableName=t2_80_1_2, externalStorageTableName=ExternalStorage_SHARE_CDC_-1913986780, shareCdcTaskId=6822a9924c4e56217558eca0, connectionId=6822a9434c4e56217558ec7d) 
[INFO ] 2025-05-13 03:40:27.930 - [t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560][qa_mongodb_repl_42240_share_1717403468657_3537] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537_t2_80_1_2_t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560, external storage name: ExternalStorage_SHARE_CDC_-1913986780 
[INFO ] 2025-05-13 03:40:27.933 - [t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560][qa_mongodb_repl_42240_share_1717403468657_3537] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [t2_80_1_2] 
[INFO ] 2025-05-13 03:40:27.933 - [t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560][qa_mongodb_repl_42240_share_1717403468657_3537] - [Share CDC Task HZ Reader] - Find sequence in construct(t2_80_1_2) by timestamp(2025-05-13T03:40:27.760Z): 44731 
[TRACE] 2025-05-13 03:40:27.933 - [t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560][qa_mongodb_repl_42240_share_1717403468657_3537] - Connector MongoDB incremental start succeed, tables: [t2_80_1_2], data change syncing 
[INFO ] 2025-05-13 03:40:27.933 - [t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560][qa_mongodb_repl_42240_share_1717403468657_3537] - [Share CDC Task HZ Reader] - Starting read 't2_80_1_2' log, sequence: 44731 
[INFO ] 2025-05-13 03:40:28.137 - [t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560][qa_mongodb_repl_42240_share_1717403468657_3537] - [Share CDC Task HZ Reader] - Find by t2_80_1_2 filter: {sequence=44731} 
[TRACE] 2025-05-13 03:40:33.827 - [t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560][qa_mongodb_repl_6040_1717403468657_3537] - Process after table "t2_80_1_2" initial sync finished, cost: 0 ms 
[INFO ] 2025-05-13 03:40:33.827 - [t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560][qa_mongodb_repl_6040_1717403468657_3537] - Process after all table(s) initial sync are finished，table number: 1 
[INFO ] 2025-05-13 03:40:51.638 - [t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560][qa_mongodb_repl_42240_share_1717403468657_3537] - [Share CDC Task HZ Reader] - Successfully read first log data: Document{{fromTable=t2_80_1_2, timestamp=1747107648000, date=Tue May 13 03:40:48 GMT 2025, after=Document{{_id=[B@47044a12, key=1, x=1, y=str, z=[B@431c2296}}, op=i, offsetString=_tap_encode_gAFkABVvcmcuYnNvbi5Cc29uRG9jdW1lbnQBFAAFX2RhdGEBAgATb3JnLmJzb24uQnNvblN0cmlu
ZwAAAdR7ImFycmF5IjpmYWxzZSwiYmluYXJ5IjpmYWxzZSwiYm9vbGVhbiI6ZmFsc2UsImJzb25U
eXBlIjoiU1RSSU5HIiwiZEJQb2ludGVyIjpmYWxzZSwiZGF0ZVRpbWUiOmZhbHNlLCJkZWNpbWFs
MTI4IjpmYWxzZSwiZG9jdW1lbnQiOmZhbHNlLCJkb3VibGUiOmZhbHNlLCJpbnQzMiI6ZmFsc2Us
ImludDY0IjpmYWxzZSwiamF2YVNjcmlwdCI6ZmFsc2UsImphdmFTY3JpcHRXaXRoU2NvcGUiOmZh
bHNlLCJudWxsIjpmYWxzZSwibnVtYmVyIjpmYWxzZSwib2JqZWN0SWQiOmZhbHNlLCJyZWd1bGFy
RXhwcmVzc2lvbiI6ZmFsc2UsInN0cmluZyI6dHJ1ZSwic3ltYm9sIjpmYWxzZSwidGltZXN0YW1w
IjpmYWxzZSwidmFsdWUiOiI4MjY4MjJCRjQwMDAwMDAwMDEyQjAyMkMwMTAwMjk2RTVBMTAwNEZD
OEEyOEQ3RjYzMDQ5RkY4NkM2MzZDOTFBRTkyRDhGNDY2NDVGNjk2NDAwNjQ2ODIyQkY0MDUwQ0VG
QzIxNjM4RTczOUYwMDA0In2o
, type=DATA, connectionId=6822a9434c4e56217558ec7d, isReplaceEvent=false, _ts=1747107648}} 
[TRACE] 2025-05-13 03:51:29.737 - [t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560][qa_mongodb_repl_42240_share_1717403468657_3537] - Node qa_mongodb_repl_42240_share_1717403468657_3537[0fc9ec27-a283-4bfa-92d2-847c77865721] running status set to false 
[TRACE] 2025-05-13 03:51:29.743 - [t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560][qa_mongodb_repl_42240_share_1717403468657_3537] - Incremental sync completed 
[TRACE] 2025-05-13 03:51:29.744 - [t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560][qa_mongodb_repl_42240_share_1717403468657_3537] - PDK connector node stopped: HazelcastSourcePdkDataNode_0fc9ec27-a283-4bfa-92d2-847c77865721_1747107627647 
[TRACE] 2025-05-13 03:51:29.744 - [t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560][qa_mongodb_repl_42240_share_1717403468657_3537] - PDK connector node released: HazelcastSourcePdkDataNode_0fc9ec27-a283-4bfa-92d2-847c77865721_1747107627647 
[TRACE] 2025-05-13 03:51:29.744 - [t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560][qa_mongodb_repl_42240_share_1717403468657_3537] - Node qa_mongodb_repl_42240_share_1717403468657_3537[0fc9ec27-a283-4bfa-92d2-847c77865721] schema data cleaned 
[TRACE] 2025-05-13 03:51:29.744 - [t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560][qa_mongodb_repl_42240_share_1717403468657_3537] - Node qa_mongodb_repl_42240_share_1717403468657_3537[0fc9ec27-a283-4bfa-92d2-847c77865721] monitor closed 
[TRACE] 2025-05-13 03:51:29.745 - [t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560][qa_mongodb_repl_42240_share_1717403468657_3537] - Node qa_mongodb_repl_42240_share_1717403468657_3537[0fc9ec27-a283-4bfa-92d2-847c77865721] close complete, cost 7 ms 
[TRACE] 2025-05-13 03:51:29.745 - [t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[2b931a72-ba30-43f5-8952-d95205adf32a] running status set to false 
[TRACE] 2025-05-13 03:51:29.756 - [t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560][qa_mongodb_repl_6040_1717403468657_3537] - PDK connector node stopped: HazelcastTargetPdkDataNode_2b931a72-ba30-43f5-8952-d95205adf32a_1747107627557 
[TRACE] 2025-05-13 03:51:29.759 - [t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560][qa_mongodb_repl_6040_1717403468657_3537] - PDK connector node released: HazelcastTargetPdkDataNode_2b931a72-ba30-43f5-8952-d95205adf32a_1747107627557 
[TRACE] 2025-05-13 03:51:29.759 - [t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[2b931a72-ba30-43f5-8952-d95205adf32a] schema data cleaned 
[TRACE] 2025-05-13 03:51:29.759 - [t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[2b931a72-ba30-43f5-8952-d95205adf32a] monitor closed 
[TRACE] 2025-05-13 03:51:29.759 - [t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[2b931a72-ba30-43f5-8952-d95205adf32a] close complete, cost 11 ms 
[TRACE] 2025-05-13 03:51:30.095 - [t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-05-13 03:51:30.095 - [t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@3f5c9fed 
[TRACE] 2025-05-13 03:51:30.095 - [t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560] - Stop task milestones: 6822bf204c4e56217558f3c5(t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560)  
[TRACE] 2025-05-13 03:51:30.211 - [t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560] - Stopped task aspect(s) 
[TRACE] 2025-05-13 03:51:30.211 - [t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560] - Snapshot order controller have been removed 
[INFO ] 2025-05-13 03:51:30.211 - [t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560] - Task stopped. 
[TRACE] 2025-05-13 03:51:30.259 - [t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560] - Remove memory task client succeed, task: t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560[6822bf204c4e56217558f3c5] 
[TRACE] 2025-05-13 03:51:30.259 - [t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560] - Destroy memory task client cache succeed, task: t_80.1.2-2-mdb-v4_to_mdb-v6_share_with_check_data_2_1717403468657_3537-1747107560[6822bf204c4e56217558f3c5] 
