[TRACE] 2025-06-04 04:17:07.704 - [TAP-6799] - Task initialization... 
[TRACE] 2025-06-04 04:17:07.705 - [TAP-6799] - Start task milestones: 683ec91db023c8285961de54(TAP-6799) 
[TRACE] 2025-06-04 04:17:08.870 - [TAP-6799] - load tapTable task 683ec91db023c8285961de53-53f0606d-3528-4aa5-ad0b-e71efbe2c405 complete, cost 838ms 
[TRACE] 2025-06-04 04:17:09.477 - [TAP-6799] - load tapTable task 683ec91db023c8285961de53-66e17c34-77dd-4135-a109-1b4524b89f07 complete, cost 575ms 
[TRACE] 2025-06-04 04:17:10.110 - [TAP-6799] - load tapTable task 683ec91db023c8285961de53-1cc89333-71b2-4ffb-bea3-1bbc988a71ad complete, cost 611ms 
[INFO ] 2025-06-04 04:17:10.232 - [TAP-6799] - Loading table structure completed 
[TRACE] 2025-06-04 04:17:10.235 - [TAP-6799] - Node performs snapshot read by order list: [ null ] -> [ null ] 
[TRACE] 2025-06-04 04:17:11.504 - [TAP-6799] - The engine receives TAP-6799 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-04 04:17:11.504 - [TAP-6799] - Task started 
[TRACE] 2025-06-04 04:17:11.555 - [TAP-6799][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:17:11.555 - [TAP-6799][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:17:11.556 - [TAP-6799][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:17:11.556 - [TAP-6799][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:17:11.557 - [TAP-6799][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:17:11.557 - [TAP-6799][FDM_product] - Node js_processor(FDM_product: 73fce465-47ee-46f4-abb7-173648d43889) enable batch process 
[TRACE] 2025-06-04 04:17:11.557 - [TAP-6799][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:17:11.557 - [TAP-6799][MDM_packages] - Node field_add_del_processor(MDM_packages: 3080ce1e-1a69-43e1-8283-6bfbdbad03d9) enable batch process 
[TRACE] 2025-06-04 04:17:11.558 - [TAP-6799][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:17:11.558 - [TAP-6799][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:17:11.558 - [TAP-6799][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:17:11.559 - [TAP-6799][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:17:11.559 - [TAP-6799][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:17:11.559 - [TAP-6799][FDM_product] - Node js_processor(FDM_product: 1674c3ea-16fc-4674-9a27-0cd5951e0f48) enable batch process 
[TRACE] 2025-06-04 04:17:11.559 - [TAP-6799][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:17:11.560 - [TAP-6799][MDM_packages] - Node js_processor(MDM_packages: 8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d) enable batch process 
[TRACE] 2025-06-04 04:17:11.560 - [TAP-6799][主从合并] - Node 主从合并[48ad06a3-b6bb-40e0-9bf4-cd4c8b390e28] start preload schema,table counts: 3 
[TRACE] 2025-06-04 04:17:11.560 - [TAP-6799][主从合并] - Node 主从合并[48ad06a3-b6bb-40e0-9bf4-cd4c8b390e28] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:17:11.561 - [TAP-6799][主从合并] - Node merge_table_processor(主从合并: 48ad06a3-b6bb-40e0-9bf4-cd4c8b390e28) enable batch process 
[TRACE] 2025-06-04 04:17:11.561 - [TAP-6799][MDM_catalogItem_tmp_6799] - Node MDM_catalogItem_tmp_6799[c1f22337-5eaf-429f-99f6-8fd1a77ed0b0] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:17:11.561 - [TAP-6799][MDM_catalogItem_tmp_6799] - Node MDM_catalogItem_tmp_6799[c1f22337-5eaf-429f-99f6-8fd1a77ed0b0] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:17:11.765 - [TAP-6799][主从合并] - 
Merge lookup relation{
  FDM_product(1674c3ea-16fc-4674-9a27-0cd5951e0f48)
    ->MDM_packages(8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d)
} 
[INFO ] 2025-06-04 04:17:11.930 - [TAP-6799][MDM_packages] - Source connector(MDM_packages) initialization completed 
[TRACE] 2025-06-04 04:17:11.930 - [TAP-6799][MDM_packages] - Source node "MDM_packages" read batch size: 100 
[TRACE] 2025-06-04 04:17:11.930 - [TAP-6799][MDM_packages] - Source node "MDM_packages" event queue capacity: 200 
[TRACE] 2025-06-04 04:17:11.930 - [TAP-6799][MDM_packages] - On the first run, the breakpoint will be initialized 
[TRACE] 2025-06-04 04:17:11.930 - [TAP-6799][主从合并] - Create merge cache, node id: 8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d, imap name: *********, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost/tapdata_v35_jdk17', table='null', ttlDay=0] 
[INFO ] 2025-06-04 04:17:11.956 - [TAP-6799][MDM_packages] - Use existing stream offset: {"cdcOffset":1749010631,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[TRACE] 2025-06-04 04:17:11.959 - [TAP-6799][主从合并] - Merge table processor lookup thread num: 8 
[TRACE] 2025-06-04 04:17:11.959 - [TAP-6799][主从合并] - Merge table processor handle update join key thread num: 4 
[INFO ] 2025-06-04 04:17:12.065 - [TAP-6799][FDM_product] - Source connector(FDM_product) initialization completed 
[TRACE] 2025-06-04 04:17:12.068 - [TAP-6799][FDM_product] - Source node "FDM_product" read batch size: 100 
[TRACE] 2025-06-04 04:17:12.068 - [TAP-6799][FDM_product] - Source node "FDM_product" event queue capacity: 200 
[TRACE] 2025-06-04 04:17:12.081 - [TAP-6799][FDM_product] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-06-04 04:17:12.081 - [TAP-6799][FDM_product] - Use existing stream offset: {"cdcOffset":1749010631,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2025-06-04 04:17:12.193 - [TAP-6799][MDM_catalogItem_tmp_6799] - Sink connector(MDM_catalogItem_tmp_6799) initialization completed 
[TRACE] 2025-06-04 04:17:12.193 - [TAP-6799][MDM_catalogItem_tmp_6799] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-04 04:17:12.195 - [TAP-6799][MDM_catalogItem_tmp_6799] - Apply table structure to target database 
[INFO ] 2025-06-04 04:17:12.266 - [TAP-6799][MDM_packages] - Starting batch read from 1 tables 
[TRACE] 2025-06-04 04:17:12.267 - [TAP-6799] - Node[MDM_packages] is waiting for running 
[INFO ] 2025-06-04 04:17:12.352 - [TAP-6799][FDM_product] - Starting batch read from 1 tables 
[TRACE] 2025-06-04 04:17:12.352 - [TAP-6799][FDM_product] - Initial sync started 
[INFO ] 2025-06-04 04:17:12.352 - [TAP-6799][FDM_product] - Starting batch read from table: FDM_product 
[TRACE] 2025-06-04 04:17:12.353 - [TAP-6799][FDM_product] - Query snapshot row size completed: FDM_product(0e0a3626-c9f8-4b06-995c-45c95039db3b) 
[TRACE] 2025-06-04 04:17:12.354 - [TAP-6799][FDM_product] - Table FDM_product is going to be initial synced 
[INFO ] 2025-06-04 04:17:12.364 - [TAP-6799][FDM_product] - Table FDM_product has been completed batch read 
[TRACE] 2025-06-04 04:17:12.364 - [TAP-6799][FDM_product] - Initial sync completed 
[INFO ] 2025-06-04 04:17:12.364 - [TAP-6799][FDM_product] - Batch read completed. 
[TRACE] 2025-06-04 04:17:12.364 - [TAP-6799][FDM_product] - Incremental sync starting... 
[TRACE] 2025-06-04 04:17:12.399 - [TAP-6799][FDM_product] - Initial sync completed 
[INFO ] 2025-06-04 04:17:12.399 - [TAP-6799][FDM_product] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2025-06-04 04:17:12.401 - [TAP-6799][FDM_product] - [Share CDC Task HZ Reader] - Step 1 - Check connection S Agent Local DaaS_1 enable share cdc: true 
[INFO ] 2025-06-04 04:17:12.401 - [TAP-6799][FDM_product] - [Share CDC Task HZ Reader] - Step 2 - Check task TAP-6799 enable share cdc: true 
[INFO ] 2025-06-04 04:17:12.423 - [TAP-6799][FDM_product] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: CDC log cache task from S Agent Local DaaS_1 
[INFO ] 2025-06-04 04:17:12.423 - [TAP-6799][FDM_product] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost/tapdata_v35_jdk17', table='null', ttlDay=0] 
[INFO ] 2025-06-04 04:17:12.424 - [TAP-6799][FDM_product] - [Share CDC Task HZ Reader] - Step 6 - Init read thread pool completed 
[INFO ] 2025-06-04 04:17:12.424 - [TAP-6799][FDM_product] - Init share cdc reader completed 
[INFO ] 2025-06-04 04:17:12.425 - [TAP-6799][FDM_product] - Starting incremental sync using share log storage mode 
[INFO ] 2025-06-04 04:17:12.425 - [TAP-6799][FDM_product] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2025-06-04 04:17:12.430 - [TAP-6799][FDM_product] - [Share CDC Task HZ Reader] - Read table count: 1, partition size: 1, read thread number: 1 
[INFO ] 2025-06-04 04:17:12.431 - [TAP-6799][FDM_product] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=683e9f99fb36645c5d490ccf, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=63468098c87faf3ba64fece0_FDM_product, version=v2, tableName=FDM_product, externalStorageTableName=ExternalStorage_SHARE_CDC_-1738770242, shareCdcTaskId=683e9f99b023c8285961dcff, connectionId=63468098c87faf3ba64fece0) 
[INFO ] 2025-06-04 04:17:12.436 - [TAP-6799][FDM_product] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from S Agent Local DaaS_1_FDM_product_TAP-6799, external storage name: ExternalStorage_SHARE_CDC_-1738770242 
[INFO ] 2025-06-04 04:17:12.436 - [TAP-6799][FDM_product] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [FDM_product] 
[INFO ] 2025-06-04 04:17:12.441 - [TAP-6799][FDM_product] - [Share CDC Task HZ Reader] - Find sequence in construct(FDM_product) by timestamp(2025-06-04T04:17:11.965Z): 7 
[TRACE] 2025-06-04 04:17:12.445 - [TAP-6799][FDM_product] - Connector MongoDB incremental start succeed, tables: [FDM_product], data change syncing 
[INFO ] 2025-06-04 04:17:12.445 - [TAP-6799][FDM_product] - [Share CDC Task HZ Reader] - Starting read 'FDM_product' log, sequence: 7 
[INFO ] 2025-06-04 04:17:12.445 - [TAP-6799][FDM_product] - [Share CDC Task HZ Reader] - Find by FDM_product filter: {sequence=7} 
[TRACE] 2025-06-04 04:17:14.719 - [TAP-6799][MDM_catalogItem_tmp_6799] - The target node received dll event(io.tapdata.entity.event.ddl.index.TapCreateIndexEvent@22568a88: {"indexList":[{"indexFields":[{"fieldAsc":true,"name":"catalogItem"}]},{"indexFields":[{"fieldAsc":true,"name":"packages.productId"},{"fieldAsc":true,"name":"packages.weightUnit"},{"fieldAsc":true,"name":"packages.weightCode"}]},{"indexFields":[{"fieldAsc":true,"name":"productId"}]}],"tableId":"48ad06a3-b6bb-40e0-9bf4-cd4c8b390e28","type":101}). Wait for all previous events to be processed 
[TRACE] 2025-06-04 04:17:14.720 - [TAP-6799][MDM_catalogItem_tmp_6799] - The target node refreshes the memory model according to the ddl event(io.tapdata.entity.event.ddl.index.TapCreateIndexEvent@22568a88: {"indexList":[{"indexFields":[{"fieldAsc":true,"name":"catalogItem"}]},{"indexFields":[{"fieldAsc":true,"name":"packages.productId"},{"fieldAsc":true,"name":"packages.weightUnit"},{"fieldAsc":true,"name":"packages.weightCode"}]},{"indexFields":[{"fieldAsc":true,"name":"productId"}]}],"tableId":"48ad06a3-b6bb-40e0-9bf4-cd4c8b390e28","type":101}) 
[TRACE] 2025-06-04 04:17:15.308 - [TAP-6799] - Node[FDM_product] finish, notify next layer to run 
[TRACE] 2025-06-04 04:17:15.308 - [TAP-6799] - Next layer have been notified: [null] 
[TRACE] 2025-06-04 04:17:15.308 - [TAP-6799][MDM_packages] - Initial sync started 
[INFO ] 2025-06-04 04:17:15.308 - [TAP-6799][MDM_packages] - Starting batch read from table: MDM_packages 
[TRACE] 2025-06-04 04:17:15.308 - [TAP-6799][MDM_packages] - Table MDM_packages is going to be initial synced 
[TRACE] 2025-06-04 04:17:15.309 - [TAP-6799][MDM_packages] - Query snapshot row size completed: MDM_packages(7b05c753-5d0e-4b81-91b5-ef539e7f6ac8) 
[TRACE] 2025-06-04 04:17:15.326 - [TAP-6799][MDM_catalogItem_tmp_6799] - Process after table "MDM_catalogItem_tmp_6799" initial sync finished, cost: 0 ms 
[INFO ] 2025-06-04 04:17:15.326 - [TAP-6799][MDM_catalogItem_tmp_6799] - Process after all table(s) initial sync are finished，table number: 1 
[INFO ] 2025-06-04 04:17:15.327 - [TAP-6799][MDM_packages] - Table MDM_packages has been completed batch read 
[TRACE] 2025-06-04 04:17:15.327 - [TAP-6799][MDM_packages] - Initial sync completed 
[INFO ] 2025-06-04 04:17:15.327 - [TAP-6799][MDM_packages] - Batch read completed. 
[TRACE] 2025-06-04 04:17:15.327 - [TAP-6799][MDM_packages] - Incremental sync starting... 
[TRACE] 2025-06-04 04:17:15.351 - [TAP-6799][MDM_packages] - Initial sync completed 
[INFO ] 2025-06-04 04:17:15.351 - [TAP-6799][MDM_packages] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2025-06-04 04:17:15.351 - [TAP-6799][MDM_packages] - [Share CDC Task HZ Reader] - Step 1 - Check connection S Agent Local DaaS_1 enable share cdc: true 
[INFO ] 2025-06-04 04:17:15.351 - [TAP-6799][MDM_packages] - [Share CDC Task HZ Reader] - Step 2 - Check task TAP-6799 enable share cdc: true 
[INFO ] 2025-06-04 04:17:15.361 - [TAP-6799][MDM_packages] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: CDC log cache task from S Agent Local DaaS_1 
[INFO ] 2025-06-04 04:17:15.370 - [TAP-6799][MDM_packages] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost/tapdata_v35_jdk17', table='null', ttlDay=0] 
[INFO ] 2025-06-04 04:17:15.370 - [TAP-6799][MDM_packages] - [Share CDC Task HZ Reader] - Step 6 - Init read thread pool completed 
[INFO ] 2025-06-04 04:17:15.370 - [TAP-6799][MDM_packages] - Init share cdc reader completed 
[INFO ] 2025-06-04 04:17:15.370 - [TAP-6799][MDM_packages] - Starting incremental sync using share log storage mode 
[INFO ] 2025-06-04 04:17:15.370 - [TAP-6799][MDM_packages] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2025-06-04 04:17:15.377 - [TAP-6799][MDM_packages] - [Share CDC Task HZ Reader] - Read table count: 1, partition size: 1, read thread number: 1 
[INFO ] 2025-06-04 04:17:15.377 - [TAP-6799][MDM_packages] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=683eca61fb36645c5d4fabc7, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=63468098c87faf3ba64fece0_MDM_packages, version=v2, tableName=MDM_packages, externalStorageTableName=ExternalStorage_SHARE_CDC_295938199, shareCdcTaskId=683e9f99b023c8285961dcff, connectionId=63468098c87faf3ba64fece0) 
[INFO ] 2025-06-04 04:17:15.389 - [TAP-6799][MDM_packages] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from S Agent Local DaaS_1_MDM_packages_TAP-6799, external storage name: ExternalStorage_SHARE_CDC_295938199 
[INFO ] 2025-06-04 04:17:15.389 - [TAP-6799][MDM_packages] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [MDM_packages] 
[INFO ] 2025-06-04 04:17:15.397 - [TAP-6799][MDM_packages] - [Share CDC Task HZ Reader] - Find sequence in construct(MDM_packages) by timestamp(2025-06-04T04:17:11.910Z): 20 
[TRACE] 2025-06-04 04:17:15.397 - [TAP-6799][MDM_packages] - Connector MongoDB incremental start succeed, tables: [MDM_packages], data change syncing 
[INFO ] 2025-06-04 04:17:15.397 - [TAP-6799][MDM_packages] - [Share CDC Task HZ Reader] - Starting read 'MDM_packages' log, sequence: 20 
[INFO ] 2025-06-04 04:17:15.599 - [TAP-6799][MDM_packages] - [Share CDC Task HZ Reader] - Find by MDM_packages filter: {sequence=20} 
[TRACE] 2025-06-04 04:17:18.445 - [TAP-6799][MDM_catalogItem_tmp_6799] - Process after table "MDM_catalogItem_tmp_6799" initial sync finished, cost: 0 ms 
[INFO ] 2025-06-04 04:17:18.446 - [TAP-6799][MDM_catalogItem_tmp_6799] - Process after all table(s) initial sync are finished，table number: 1 
[INFO ] 2025-06-04 04:17:48.616 - [TAP-6799][MDM_packages] - [Share CDC Task HZ Reader] - Successfully read first log data: Document{{fromTable=MDM_packages, timestamp=1749010662000, date=Wed Jun 04 04:17:42 GMT 2025, after=Document{{_id=[B@1e6c5af3, productId=14891, wtUnit=GM, wtCde=1, nonStockId=5023, packRemrk=null, FDM_hkImStaticReference_id=66d970794e2dc77b838ad4e3, prodPackEng=5023 特殊擺件箱座 (L140*W94*H193mm), prodPackZhs=5023 特殊摆件箱座 (L140*W94*H193mm), prodPackZht=5023 特殊擺件箱座 (L140*W94*H193mm), lastModifyDate=[B@2cc8c79}}, op=i, offsetString=_tap_encode_gAFkABVvcmcuYnNvbi5Cc29uRG9jdW1lbnQBFAAFX2RhdGEBAgATb3JnLmJzb24uQnNvblN0cmlu
ZwAAAiB7ImFycmF5IjpmYWxzZSwiYmluYXJ5IjpmYWxzZSwiYm9vbGVhbiI6ZmFsc2UsImJzb25U
eXBlIjoiU1RSSU5HIiwiZEJQb2ludGVyIjpmYWxzZSwiZGF0ZVRpbWUiOmZhbHNlLCJkZWNpbWFs
MTI4IjpmYWxzZSwiZG9jdW1lbnQiOmZhbHNlLCJkb3VibGUiOmZhbHNlLCJpbnQzMiI6ZmFsc2Us
ImludDY0IjpmYWxzZSwiamF2YVNjcmlwdCI6ZmFsc2UsImphdmFTY3JpcHRXaXRoU2NvcGUiOmZh
bHNlLCJudWxsIjpmYWxzZSwibnVtYmVyIjpmYWxzZSwib2JqZWN0SWQiOmZhbHNlLCJyZWd1bGFy
RXhwcmVzc2lvbiI6ZmFsc2UsInN0cmluZyI6dHJ1ZSwic3ltYm9sIjpmYWxzZSwidGltZXN0YW1w
IjpmYWxzZSwidmFsdWUiOiI4MjY4M0ZDOEU2MDAwMDAwMDEyQjA0MkMwMTAwMjk2RTVBMTAwNDJG
RTlGMzVBOUU4NzQzNUE5QkM1NTAxRTE0RTgzRTZCNDYzQzZGNzA2NTcyNjE3NDY5NkY2RTU0Nzk3
MDY1MDAzQzY5NkU3MzY1NzI3NDAwNDY2NDZGNjM3NTZENjU2RTc0NEI2NTc5MDA0NjY0NUY2OTY0
MDA2NDY4M0ZDOEU2QUUyMzJCQzJGQkI1RjhBRDAwMDAwNCJ9qA==
, type=DATA, connectionId=63468098c87faf3ba64fece0, isReplaceEvent=false, _ts=1749010663}} 
[TRACE] 2025-06-04 04:19:35.557 - [TAP-6799][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] running status set to false 
[TRACE] 2025-06-04 04:19:35.557 - [TAP-6799][FDM_product] - Incremental sync completed 
[TRACE] 2025-06-04 04:19:35.561 - [TAP-6799][FDM_product] - PDK connector node stopped: HazelcastSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010631789 
[TRACE] 2025-06-04 04:19:35.561 - [TAP-6799][FDM_product] - PDK connector node released: HazelcastSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010631789 
[TRACE] 2025-06-04 04:19:35.561 - [TAP-6799][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] schema data cleaned 
[TRACE] 2025-06-04 04:19:35.563 - [TAP-6799][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] monitor closed 
[TRACE] 2025-06-04 04:19:35.563 - [TAP-6799][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] close complete, cost 10 ms 
[TRACE] 2025-06-04 04:19:35.563 - [TAP-6799][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] running status set to false 
[INFO ] 2025-06-04 04:19:35.566 - [TAP-6799][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-05b91823-8d39-4a96-aa28-9d4c0111220c 
[INFO ] 2025-06-04 04:19:35.566 - [TAP-6799][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-05b91823-8d39-4a96-aa28-9d4c0111220c 
[INFO ] 2025-06-04 04:19:35.567 - [TAP-6799][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de54-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[INFO ] 2025-06-04 04:19:35.567 - [TAP-6799][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-16e3aae8-b515-42ea-94bc-e3f75b3df589 
[INFO ] 2025-06-04 04:19:35.568 - [TAP-6799][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-16e3aae8-b515-42ea-94bc-e3f75b3df589 
[INFO ] 2025-06-04 04:19:35.568 - [TAP-6799][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de54-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:19:35.570 - [TAP-6799][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] schema data cleaned 
[TRACE] 2025-06-04 04:19:35.570 - [TAP-6799][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] monitor closed 
[TRACE] 2025-06-04 04:19:35.571 - [TAP-6799][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] close complete, cost 7 ms 
[TRACE] 2025-06-04 04:19:35.571 - [TAP-6799][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] running status set to false 
[TRACE] 2025-06-04 04:19:35.576 - [TAP-6799][MDM_packages] - Incremental sync completed 
[TRACE] 2025-06-04 04:19:35.576 - [TAP-6799][MDM_packages] - PDK connector node stopped: HazelcastSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749010631789 
[TRACE] 2025-06-04 04:19:35.577 - [TAP-6799][MDM_packages] - PDK connector node released: HazelcastSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749010631789 
[TRACE] 2025-06-04 04:19:35.577 - [TAP-6799][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] schema data cleaned 
[TRACE] 2025-06-04 04:19:35.577 - [TAP-6799][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] monitor closed 
[TRACE] 2025-06-04 04:19:35.577 - [TAP-6799][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] close complete, cost 6 ms 
[TRACE] 2025-06-04 04:19:35.603 - [TAP-6799][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] running status set to false 
[TRACE] 2025-06-04 04:19:35.603 - [TAP-6799][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] schema data cleaned 
[TRACE] 2025-06-04 04:19:35.603 - [TAP-6799][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] monitor closed 
[TRACE] 2025-06-04 04:19:35.603 - [TAP-6799][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] close complete, cost 25 ms 
[TRACE] 2025-06-04 04:19:35.603 - [TAP-6799][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] running status set to false 
[INFO ] 2025-06-04 04:19:35.605 - [TAP-6799][MDM_packages][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-4e2c371c-72c9-43b6-ab8f-b3a89d14fcf5 
[INFO ] 2025-06-04 04:19:35.605 - [TAP-6799][MDM_packages][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-4e2c371c-72c9-43b6-ab8f-b3a89d14fcf5 
[INFO ] 2025-06-04 04:19:35.605 - [TAP-6799][MDM_packages][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de54-8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d-63468098c87faf3ba64fece0] schema data cleaned 
[INFO ] 2025-06-04 04:19:35.606 - [TAP-6799][MDM_packages][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-d480b590-4af2-422a-b2d5-76d76621e00a 
[INFO ] 2025-06-04 04:19:35.606 - [TAP-6799][MDM_packages][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-d480b590-4af2-422a-b2d5-76d76621e00a 
[INFO ] 2025-06-04 04:19:35.606 - [TAP-6799][MDM_packages][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de54-8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:19:35.606 - [TAP-6799][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] schema data cleaned 
[TRACE] 2025-06-04 04:19:35.607 - [TAP-6799][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] monitor closed 
[TRACE] 2025-06-04 04:19:35.607 - [TAP-6799][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] close complete, cost 3 ms 
[TRACE] 2025-06-04 04:19:35.609 - [TAP-6799][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] running status set to false 
[INFO ] 2025-06-04 04:19:35.609 - [TAP-6799][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-c46f333e-859a-4086-b5ac-29de0021a968 
[INFO ] 2025-06-04 04:19:35.609 - [TAP-6799][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-c46f333e-859a-4086-b5ac-29de0021a968 
[INFO ] 2025-06-04 04:19:35.609 - [TAP-6799][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de54-1674c3ea-16fc-4674-9a27-0cd5951e0f48-63468098c87faf3ba64fece0] schema data cleaned 
[INFO ] 2025-06-04 04:19:35.609 - [TAP-6799][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-e6babcdc-1a99-4918-9bc1-81f86086e01a 
[INFO ] 2025-06-04 04:19:35.609 - [TAP-6799][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-e6babcdc-1a99-4918-9bc1-81f86086e01a 
[INFO ] 2025-06-04 04:19:35.609 - [TAP-6799][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de54-1674c3ea-16fc-4674-9a27-0cd5951e0f48-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:19:35.611 - [TAP-6799][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] schema data cleaned 
[TRACE] 2025-06-04 04:19:35.611 - [TAP-6799][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] monitor closed 
[TRACE] 2025-06-04 04:19:35.611 - [TAP-6799][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] close complete, cost 3 ms 
[TRACE] 2025-06-04 04:19:35.611 - [TAP-6799][主从合并] - Node 主从合并[48ad06a3-b6bb-40e0-9bf4-cd4c8b390e28] running status set to false 
[TRACE] 2025-06-04 04:19:35.611 - [TAP-6799][主从合并] - Destroy merge cache resource: ********* 
[TRACE] 2025-06-04 04:19:35.612 - [TAP-6799][主从合并] - Node 主从合并[48ad06a3-b6bb-40e0-9bf4-cd4c8b390e28] schema data cleaned 
[TRACE] 2025-06-04 04:19:35.612 - [TAP-6799][主从合并] - Node 主从合并[48ad06a3-b6bb-40e0-9bf4-cd4c8b390e28] monitor closed 
[TRACE] 2025-06-04 04:19:35.613 - [TAP-6799][主从合并] - Node 主从合并[48ad06a3-b6bb-40e0-9bf4-cd4c8b390e28] close complete, cost 1 ms 
[TRACE] 2025-06-04 04:19:35.613 - [TAP-6799][MDM_catalogItem_tmp_6799] - Node MDM_catalogItem_tmp_6799[c1f22337-5eaf-429f-99f6-8fd1a77ed0b0] running status set to false 
[TRACE] 2025-06-04 04:19:35.622 - [TAP-6799][MDM_catalogItem_tmp_6799] - PDK connector node stopped: HazelcastTargetPdkDataNode_c1f22337-5eaf-429f-99f6-8fd1a77ed0b0_1749010631896 
[TRACE] 2025-06-04 04:19:35.622 - [TAP-6799][MDM_catalogItem_tmp_6799] - PDK connector node released: HazelcastTargetPdkDataNode_c1f22337-5eaf-429f-99f6-8fd1a77ed0b0_1749010631896 
[TRACE] 2025-06-04 04:19:35.622 - [TAP-6799][MDM_catalogItem_tmp_6799] - Node MDM_catalogItem_tmp_6799[c1f22337-5eaf-429f-99f6-8fd1a77ed0b0] schema data cleaned 
[TRACE] 2025-06-04 04:19:35.622 - [TAP-6799][MDM_catalogItem_tmp_6799] - Node MDM_catalogItem_tmp_6799[c1f22337-5eaf-429f-99f6-8fd1a77ed0b0] monitor closed 
[TRACE] 2025-06-04 04:19:35.825 - [TAP-6799][MDM_catalogItem_tmp_6799] - Node MDM_catalogItem_tmp_6799[c1f22337-5eaf-429f-99f6-8fd1a77ed0b0] close complete, cost 9 ms 
[TRACE] 2025-06-04 04:19:43.731 - [TAP-6799] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-04 04:19:44.672 - [TAP-6799] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@14c87e78 
[TRACE] 2025-06-04 04:19:44.672 - [TAP-6799] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2663d167 
[TRACE] 2025-06-04 04:19:44.815 - [TAP-6799] - Stop task milestones: 683ec91db023c8285961de54(TAP-6799)  
[TRACE] 2025-06-04 04:19:44.815 - [TAP-6799] - Stopped task aspect(s) 
[TRACE] 2025-06-04 04:19:44.815 - [TAP-6799] - Snapshot order controller have been removed 
[INFO ] 2025-06-04 04:19:44.815 - [TAP-6799] - Task stopped. 
[TRACE] 2025-06-04 04:19:44.868 - [TAP-6799] - Remove memory task client succeed, task: TAP-6799[683ec91db023c8285961de54] 
[TRACE] 2025-06-04 04:19:44.868 - [TAP-6799] - Destroy memory task client cache succeed, task: TAP-6799[683ec91db023c8285961de54] 
[TRACE] 2025-06-04 04:19:50.999 - [TAP-6799] - load tapTable task 683ec91db023c8285961de53-08366e4c-72c0-4dc8-a43e-d112a297f6be complete, cost 572ms 
[TRACE] 2025-06-04 04:19:51.056 - [TAP-6799] - load tapTable task 683ec91db023c8285961de53-b7931ff8-dd16-4f92-b3b4-0ba306e310ab complete, cost 445ms 
[TRACE] 2025-06-04 04:19:51.593 - [TAP-6799] - load tapTable task 683ec91db023c8285961de53-3736b996-751c-4d21-850a-7e51c94d1f6e complete, cost 510ms 
[TRACE] 2025-06-04 04:19:51.657 - [TAP-6799] - load tapTable task 683ec91db023c8285961de53-19a102d5-e751-4d63-b4cd-a2c2100b6f54 complete, cost 625ms 
[TRACE] 2025-06-04 04:19:52.195 - [TAP-6799] - load tapTable task 683ec91db023c8285961de53-af63537f-21ba-433d-9fc0-eb901b29e142 complete, cost 525ms 
[TRACE] 2025-06-04 04:19:52.347 - [TAP-6799] - load tapTable task 683ec91db023c8285961de53-fe166732-edf5-4461-8454-e981de0be14c complete, cost 742ms 
[TRACE] 2025-06-04 04:19:52.454 - [TAP-6799] - load tapTable task 683ec91db023c8285961de53-084b95c7-32e9-43a8-aba5-723d2b518bb0 complete, cost 782ms 
[TRACE] 2025-06-04 04:19:52.902 - [TAP-6799] - load tapTable task 683ec91db023c8285961de53-049010a0-e7ed-445d-87de-100a17d0dd41 complete, cost 607ms 
[TRACE] 2025-06-04 04:19:53.553 - [TAP-6799] - load tapTable task 683ec91db023c8285961de53-f486eda8-a2bb-47af-9b31-06c95bd3d62a complete, cost 635ms 
[TRACE] 2025-06-04 04:19:56.392 - [TAP-6799] - load tapTable task 683ec91db023c8285961de53-07339ff6-6769-465e-934c-5b93541b5b04 complete, cost 376ms 
[TRACE] 2025-06-04 04:19:56.872 - [TAP-6799] - load tapTable task 683ec91db023c8285961de53-ac798dea-55f6-443d-a7ed-1b4e779441b2 complete, cost 464ms 
[TRACE] 2025-06-04 04:19:57.478 - [TAP-6799] - load tapTable task 683ec91db023c8285961de53-1cb76505-90ee-4802-b7fc-709d97a6a5ba complete, cost 590ms 
[TRACE] 2025-06-04 04:20:00.832 - [TAP-6799] - load tapTable task 683ec91db023c8285961de53-8b95dcde-ac55-4ecf-b9eb-2f561c8ec177 complete, cost 396ms 
[TRACE] 2025-06-04 04:20:01.327 - [TAP-6799] - load tapTable task 683ec91db023c8285961de53-eb0b59af-1c21-4e1b-bb71-6b8201e4416f complete, cost 478ms 
[TRACE] 2025-06-04 04:20:01.912 - [TAP-6799] - load tapTable task 683ec91db023c8285961de53-9a581254-b0b8-4da9-9411-416c8b6becce complete, cost 572ms 
[TRACE] 2025-06-04 04:20:02.211 - [TAP-6799] - load tapTable task 683ec91db023c8285961de53-8e968e4c-55e0-4f87-8b2d-6deca21d56a7 complete, cost 366ms 
[TRACE] 2025-06-04 04:20:02.602 - [TAP-6799] - load tapTable task 683ec91db023c8285961de53-fa4ce548-9479-4767-b9d2-76961702ff13 complete, cost 375ms 
[TRACE] 2025-06-04 04:20:03.259 - [TAP-6799] - load tapTable task 683ec91db023c8285961de53-d8afe0fe-56a0-4b1e-88bf-e894bac43bb4 complete, cost 637ms 
[TRACE] 2025-06-04 04:20:03.534 - [TAP-6799] - load tapTable task 683ec91db023c8285961de53-06604b9f-8f89-4ff5-a851-d06044e0ea4a complete, cost 435ms 
[TRACE] 2025-06-04 04:20:03.927 - [TAP-6799] - load tapTable task 683ec91db023c8285961de53-0bb8aa78-bb23-45c4-a498-ba6ea37a7e30 complete, cost 378ms 
[TRACE] 2025-06-04 04:20:04.498 - [TAP-6799] - load tapTable task 683ec91db023c8285961de53-7fd705e8-7727-4e6e-9f96-3cf3f27cdc8c complete, cost 559ms 
[TRACE] 2025-06-04 04:20:06.342 - [TAP-6799] - load tapTable task 683ec91db023c8285961de53-291b55b5-b5a2-459b-a16b-89fe684276a3 complete, cost 387ms 
[TRACE] 2025-06-04 04:20:06.840 - [TAP-6799] - load tapTable task 683ec91db023c8285961de53-0d6162f0-aa0c-4e1f-8d56-5f81eb31e6e1 complete, cost 481ms 
[TRACE] 2025-06-04 04:20:07.446 - [TAP-6799] - load tapTable task 683ec91db023c8285961de53-09028c31-8bb5-4e5e-a049-cf7048c80e5d complete, cost 591ms 
[TRACE] 2025-06-04 04:20:09.419 - [TAP-6799] - Task initialization... 
[TRACE] 2025-06-04 04:20:09.419 - [TAP-6799] - Start task milestones: 683ec91db023c8285961de54(TAP-6799) 
[TRACE] 2025-06-04 04:20:09.860 - [TAP-6799] - load tapTable task 683ec91db023c8285961de53-5b63de86-64ce-407f-a045-9c70ab5d8fbd complete, cost 355ms 
[TRACE] 2025-06-04 04:20:10.224 - [TAP-6799] - load tapTable task 683ec91db023c8285961de53-21433e6b-d312-4a39-a72e-8728ed7bdf4e complete, cost 350ms 
[TRACE] 2025-06-04 04:20:10.751 - [TAP-6799] - load tapTable task 683ec91db023c8285961de53-a5ede849-34da-4369-b0d1-7d96b9bbccc7 complete, cost 515ms 
[INFO ] 2025-06-04 04:20:10.818 - [TAP-6799] - Loading table structure completed 
[TRACE] 2025-06-04 04:20:10.819 - [TAP-6799] - Node performs snapshot read by order list: [ null ] -> [ null ] 
[TRACE] 2025-06-04 04:20:11.412 - [TAP-6799] - The engine receives TAP-6799 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-04 04:20:11.466 - [TAP-6799] - Task started 
[TRACE] 2025-06-04 04:20:11.466 - [TAP-6799][MDM_catalogItem_tmp_6799] - Node MDM_catalogItem_tmp_6799[c1f22337-5eaf-429f-99f6-8fd1a77ed0b0] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:11.466 - [TAP-6799][MDM_catalogItem_tmp_6799] - Node MDM_catalogItem_tmp_6799[c1f22337-5eaf-429f-99f6-8fd1a77ed0b0] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:11.466 - [TAP-6799][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:11.467 - [TAP-6799][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:11.467 - [TAP-6799][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:11.467 - [TAP-6799][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:11.467 - [TAP-6799][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:11.467 - [TAP-6799][MDM_packages] - Node field_add_del_processor(MDM_packages: 3080ce1e-1a69-43e1-8283-6bfbdbad03d9) enable batch process 
[TRACE] 2025-06-04 04:20:11.467 - [TAP-6799][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:11.469 - [TAP-6799][MDM_packages] - Node js_processor(MDM_packages: 8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d) enable batch process 
[TRACE] 2025-06-04 04:20:11.469 - [TAP-6799][主从合并] - Node 主从合并[48ad06a3-b6bb-40e0-9bf4-cd4c8b390e28] start preload schema,table counts: 3 
[TRACE] 2025-06-04 04:20:11.469 - [TAP-6799][主从合并] - Node 主从合并[48ad06a3-b6bb-40e0-9bf4-cd4c8b390e28] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:11.469 - [TAP-6799][主从合并] - Node merge_table_processor(主从合并: 48ad06a3-b6bb-40e0-9bf4-cd4c8b390e28) enable batch process 
[TRACE] 2025-06-04 04:20:11.469 - [TAP-6799][主从合并] - 
Merge lookup relation{
  FDM_product(1674c3ea-16fc-4674-9a27-0cd5951e0f48)
    ->MDM_packages(8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d)
} 
[TRACE] 2025-06-04 04:20:11.471 - [TAP-6799][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:11.471 - [TAP-6799][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:11.482 - [TAP-6799][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:11.485 - [TAP-6799][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:11.485 - [TAP-6799][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:11.485 - [TAP-6799][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:11.485 - [TAP-6799][FDM_product] - Node js_processor(FDM_product: 1674c3ea-16fc-4674-9a27-0cd5951e0f48) enable batch process 
[TRACE] 2025-06-04 04:20:11.486 - [TAP-6799][FDM_product] - Node js_processor(FDM_product: 73fce465-47ee-46f4-abb7-173648d43889) enable batch process 
[INFO ] 2025-06-04 04:20:11.737 - [TAP-6799][FDM_product] - Source connector(FDM_product) initialization completed 
[TRACE] 2025-06-04 04:20:11.737 - [TAP-6799][FDM_product] - Source node "FDM_product" read batch size: 100 
[TRACE] 2025-06-04 04:20:11.737 - [TAP-6799][FDM_product] - Source node "FDM_product" event queue capacity: 200 
[TRACE] 2025-06-04 04:20:11.737 - [TAP-6799][FDM_product] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-06-04 04:20:11.843 - [TAP-6799][FDM_product] - Use existing stream offset: {"cdcOffset":1749010811,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2025-06-04 04:20:11.847 - [TAP-6799][MDM_packages] - Source connector(MDM_packages) initialization completed 
[TRACE] 2025-06-04 04:20:11.847 - [TAP-6799][MDM_packages] - Source node "MDM_packages" read batch size: 100 
[TRACE] 2025-06-04 04:20:11.848 - [TAP-6799][MDM_packages] - Source node "MDM_packages" event queue capacity: 200 
[TRACE] 2025-06-04 04:20:11.848 - [TAP-6799][MDM_packages] - On the first run, the breakpoint will be initialized 
[TRACE] 2025-06-04 04:20:11.882 - [TAP-6799][主从合并] - Create merge cache, node id: 8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d, imap name: *********, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost/tapdata_v35_jdk17', table='null', ttlDay=0] 
[INFO ] 2025-06-04 04:20:11.882 - [TAP-6799][MDM_packages] - Use existing stream offset: {"cdcOffset":1749010811,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[TRACE] 2025-06-04 04:20:11.888 - [TAP-6799][主从合并] - Merge table processor lookup thread num: 8 
[TRACE] 2025-06-04 04:20:11.888 - [TAP-6799][主从合并] - Merge table processor handle update join key thread num: 4 
[INFO ] 2025-06-04 04:20:11.906 - [TAP-6799][FDM_product] - Starting batch read from 1 tables 
[INFO ] 2025-06-04 04:20:11.906 - [TAP-6799][MDM_catalogItem_tmp_6799] - Sink connector(MDM_catalogItem_tmp_6799) initialization completed 
[TRACE] 2025-06-04 04:20:11.907 - [TAP-6799][MDM_catalogItem_tmp_6799] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-04 04:20:11.907 - [TAP-6799][MDM_catalogItem_tmp_6799] - Apply table structure to target database 
[TRACE] 2025-06-04 04:20:11.908 - [TAP-6799][FDM_product] - Initial sync started 
[INFO ] 2025-06-04 04:20:11.908 - [TAP-6799][FDM_product] - Starting batch read from table: FDM_product 
[TRACE] 2025-06-04 04:20:11.908 - [TAP-6799][FDM_product] - Table FDM_product is going to be initial synced 
[TRACE] 2025-06-04 04:20:11.912 - [TAP-6799][FDM_product] - Query snapshot row size completed: FDM_product(0e0a3626-c9f8-4b06-995c-45c95039db3b) 
[INFO ] 2025-06-04 04:20:11.912 - [TAP-6799][FDM_product] - Table FDM_product has been completed batch read 
[TRACE] 2025-06-04 04:20:11.912 - [TAP-6799][FDM_product] - Initial sync completed 
[INFO ] 2025-06-04 04:20:11.913 - [TAP-6799][FDM_product] - Batch read completed. 
[TRACE] 2025-06-04 04:20:11.913 - [TAP-6799][FDM_product] - Incremental sync starting... 
[TRACE] 2025-06-04 04:20:11.913 - [TAP-6799][FDM_product] - Initial sync completed 
[INFO ] 2025-06-04 04:20:11.939 - [TAP-6799][FDM_product] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2025-06-04 04:20:11.939 - [TAP-6799][FDM_product] - [Share CDC Task HZ Reader] - Step 1 - Check connection S Agent Local DaaS_1 enable share cdc: true 
[INFO ] 2025-06-04 04:20:11.939 - [TAP-6799][FDM_product] - [Share CDC Task HZ Reader] - Step 2 - Check task TAP-6799 enable share cdc: true 
[INFO ] 2025-06-04 04:20:11.955 - [TAP-6799][FDM_product] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: CDC log cache task from S Agent Local DaaS_1 
[INFO ] 2025-06-04 04:20:11.955 - [TAP-6799][MDM_packages] - Starting batch read from 1 tables 
[TRACE] 2025-06-04 04:20:11.955 - [TAP-6799] - Node[MDM_packages] is waiting for running 
[INFO ] 2025-06-04 04:20:11.958 - [TAP-6799][FDM_product] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost/tapdata_v35_jdk17', table='null', ttlDay=0] 
[INFO ] 2025-06-04 04:20:11.959 - [TAP-6799][FDM_product] - [Share CDC Task HZ Reader] - Step 6 - Init read thread pool completed 
[INFO ] 2025-06-04 04:20:11.959 - [TAP-6799][FDM_product] - Init share cdc reader completed 
[INFO ] 2025-06-04 04:20:11.959 - [TAP-6799][FDM_product] - Starting incremental sync using share log storage mode 
[INFO ] 2025-06-04 04:20:11.959 - [TAP-6799][FDM_product] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2025-06-04 04:20:11.959 - [TAP-6799][FDM_product] - [Share CDC Task HZ Reader] - Read table count: 1, partition size: 1, read thread number: 1 
[INFO ] 2025-06-04 04:20:11.981 - [TAP-6799][FDM_product] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=683e9f99fb36645c5d490ccf, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=63468098c87faf3ba64fece0_FDM_product, version=v2, tableName=FDM_product, externalStorageTableName=ExternalStorage_SHARE_CDC_-1738770242, shareCdcTaskId=683e9f99b023c8285961dcff, connectionId=63468098c87faf3ba64fece0) 
[INFO ] 2025-06-04 04:20:11.981 - [TAP-6799][FDM_product] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from S Agent Local DaaS_1_FDM_product_TAP-6799, external storage name: ExternalStorage_SHARE_CDC_-1738770242 
[INFO ] 2025-06-04 04:20:11.981 - [TAP-6799][FDM_product] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [FDM_product] 
[INFO ] 2025-06-04 04:20:11.999 - [TAP-6799][FDM_product] - [Share CDC Task HZ Reader] - Find sequence in construct(FDM_product) by timestamp(2025-06-04T04:20:11.737Z): 7 
[TRACE] 2025-06-04 04:20:12.003 - [TAP-6799][FDM_product] - Connector MongoDB incremental start succeed, tables: [FDM_product], data change syncing 
[INFO ] 2025-06-04 04:20:12.003 - [TAP-6799][FDM_product] - [Share CDC Task HZ Reader] - Starting read 'FDM_product' log, sequence: 7 
[INFO ] 2025-06-04 04:20:12.003 - [TAP-6799][FDM_product] - [Share CDC Task HZ Reader] - Find by FDM_product filter: {sequence=7} 
[TRACE] 2025-06-04 04:20:14.914 - [TAP-6799][MDM_catalogItem_tmp_6799] - The target node received dll event(io.tapdata.entity.event.ddl.index.TapCreateIndexEvent@1d40e5c: {"indexList":[{"indexFields":[{"fieldAsc":true,"name":"catalogItem"}]},{"indexFields":[{"fieldAsc":true,"name":"packages.productId"},{"fieldAsc":true,"name":"packages.weightUnit"},{"fieldAsc":true,"name":"packages.weightCode"}]},{"indexFields":[{"fieldAsc":true,"name":"productId"}]}],"tableId":"48ad06a3-b6bb-40e0-9bf4-cd4c8b390e28","type":101}). Wait for all previous events to be processed 
[TRACE] 2025-06-04 04:20:15.116 - [TAP-6799][MDM_catalogItem_tmp_6799] - The target node refreshes the memory model according to the ddl event(io.tapdata.entity.event.ddl.index.TapCreateIndexEvent@1d40e5c: {"indexList":[{"indexFields":[{"fieldAsc":true,"name":"catalogItem"}]},{"indexFields":[{"fieldAsc":true,"name":"packages.productId"},{"fieldAsc":true,"name":"packages.weightUnit"},{"fieldAsc":true,"name":"packages.weightCode"}]},{"indexFields":[{"fieldAsc":true,"name":"productId"}]}],"tableId":"48ad06a3-b6bb-40e0-9bf4-cd4c8b390e28","type":101}) 
[TRACE] 2025-06-04 04:20:15.524 - [TAP-6799] - Node[FDM_product] finish, notify next layer to run 
[TRACE] 2025-06-04 04:20:15.524 - [TAP-6799] - Next layer have been notified: [null] 
[TRACE] 2025-06-04 04:20:15.524 - [TAP-6799][MDM_packages] - Initial sync started 
[INFO ] 2025-06-04 04:20:15.525 - [TAP-6799][MDM_packages] - Starting batch read from table: MDM_packages 
[TRACE] 2025-06-04 04:20:15.525 - [TAP-6799][MDM_packages] - Table MDM_packages is going to be initial synced 
[TRACE] 2025-06-04 04:20:15.535 - [TAP-6799][MDM_packages] - Query snapshot row size completed: MDM_packages(7b05c753-5d0e-4b81-91b5-ef539e7f6ac8) 
[INFO ] 2025-06-04 04:20:15.540 - [TAP-6799][MDM_packages] - Table MDM_packages has been completed batch read 
[TRACE] 2025-06-04 04:20:15.540 - [TAP-6799][MDM_packages] - Initial sync completed 
[INFO ] 2025-06-04 04:20:15.540 - [TAP-6799][MDM_packages] - Batch read completed. 
[TRACE] 2025-06-04 04:20:15.540 - [TAP-6799][MDM_packages] - Incremental sync starting... 
[TRACE] 2025-06-04 04:20:15.540 - [TAP-6799][MDM_packages] - Initial sync completed 
[TRACE] 2025-06-04 04:20:15.541 - [TAP-6799][MDM_catalogItem_tmp_6799] - Process after table "MDM_catalogItem_tmp_6799" initial sync finished, cost: 0 ms 
[INFO ] 2025-06-04 04:20:15.541 - [TAP-6799][MDM_catalogItem_tmp_6799] - Process after all table(s) initial sync are finished，table number: 1 
[INFO ] 2025-06-04 04:20:15.561 - [TAP-6799][MDM_packages] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2025-06-04 04:20:15.561 - [TAP-6799][MDM_packages] - [Share CDC Task HZ Reader] - Step 1 - Check connection S Agent Local DaaS_1 enable share cdc: true 
[INFO ] 2025-06-04 04:20:15.561 - [TAP-6799][MDM_packages] - [Share CDC Task HZ Reader] - Step 2 - Check task TAP-6799 enable share cdc: true 
[INFO ] 2025-06-04 04:20:15.571 - [TAP-6799][MDM_packages] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: CDC log cache task from S Agent Local DaaS_1 
[INFO ] 2025-06-04 04:20:15.577 - [TAP-6799][MDM_packages] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost/tapdata_v35_jdk17', table='null', ttlDay=0] 
[INFO ] 2025-06-04 04:20:15.577 - [TAP-6799][MDM_packages] - [Share CDC Task HZ Reader] - Step 6 - Init read thread pool completed 
[INFO ] 2025-06-04 04:20:15.577 - [TAP-6799][MDM_packages] - Init share cdc reader completed 
[INFO ] 2025-06-04 04:20:15.577 - [TAP-6799][MDM_packages] - Starting incremental sync using share log storage mode 
[INFO ] 2025-06-04 04:20:15.577 - [TAP-6799][MDM_packages] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2025-06-04 04:20:15.578 - [TAP-6799][MDM_packages] - [Share CDC Task HZ Reader] - Read table count: 1, partition size: 1, read thread number: 1 
[INFO ] 2025-06-04 04:20:15.593 - [TAP-6799][MDM_packages] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=683eca61fb36645c5d4fabc7, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=63468098c87faf3ba64fece0_MDM_packages, version=v2, tableName=MDM_packages, externalStorageTableName=ExternalStorage_SHARE_CDC_295938199, shareCdcTaskId=683e9f99b023c8285961dcff, connectionId=63468098c87faf3ba64fece0) 
[INFO ] 2025-06-04 04:20:15.593 - [TAP-6799][MDM_packages] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from S Agent Local DaaS_1_MDM_packages_TAP-6799, external storage name: ExternalStorage_SHARE_CDC_295938199 
[INFO ] 2025-06-04 04:20:15.595 - [TAP-6799][MDM_packages] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [MDM_packages] 
[INFO ] 2025-06-04 04:20:15.595 - [TAP-6799][MDM_packages] - [Share CDC Task HZ Reader] - Find sequence in construct(MDM_packages) by timestamp(2025-06-04T04:20:11.843Z): 21 
[TRACE] 2025-06-04 04:20:15.595 - [TAP-6799][MDM_packages] - Connector MongoDB incremental start succeed, tables: [MDM_packages], data change syncing 
[INFO ] 2025-06-04 04:20:15.595 - [TAP-6799][MDM_packages] - [Share CDC Task HZ Reader] - Starting read 'MDM_packages' log, sequence: 21 
[INFO ] 2025-06-04 04:20:15.800 - [TAP-6799][MDM_packages] - [Share CDC Task HZ Reader] - Find by MDM_packages filter: {sequence=21} 
[TRACE] 2025-06-04 04:20:19.252 - [TAP-6799][MDM_catalogItem_tmp_6799] - Process after table "MDM_catalogItem_tmp_6799" initial sync finished, cost: 0 ms 
[INFO ] 2025-06-04 04:20:19.456 - [TAP-6799][MDM_catalogItem_tmp_6799] - Process after all table(s) initial sync are finished，table number: 1 
[INFO ] 2025-06-04 04:20:54.272 - [TAP-6799][MDM_packages] - [Share CDC Task HZ Reader] - Successfully read first log data: Document{{fromTable=MDM_packages, timestamp=1749010849000, date=Wed Jun 04 04:20:49 GMT 2025, after=Document{{_id=[B@21d90435, wtCde=1, productId=13, wtUnit=TL, nonStockId=5023, packRemrk=null, FDM_hkImStaticReference_id=66d970794e2dc77b838ad4e3, prodPackEng=5023 特殊擺件箱座 (L140*W94*H193mm), prodPackZhs=5023 特殊摆件箱座 (L140*W94*H193mm), prodPackZht=5023 特殊擺件箱座 (L140*W94*H193mm), lastModifyDate=[B@123884e2}}, op=i, offsetString=_tap_encode_gAFkABVvcmcuYnNvbi5Cc29uRG9jdW1lbnQBFAAFX2RhdGEBAgATb3JnLmJzb24uQnNvblN0cmlu
ZwAAAiB7ImFycmF5IjpmYWxzZSwiYmluYXJ5IjpmYWxzZSwiYm9vbGVhbiI6ZmFsc2UsImJzb25U
eXBlIjoiU1RSSU5HIiwiZEJQb2ludGVyIjpmYWxzZSwiZGF0ZVRpbWUiOmZhbHNlLCJkZWNpbWFs
MTI4IjpmYWxzZSwiZG9jdW1lbnQiOmZhbHNlLCJkb3VibGUiOmZhbHNlLCJpbnQzMiI6ZmFsc2Us
ImludDY0IjpmYWxzZSwiamF2YVNjcmlwdCI6ZmFsc2UsImphdmFTY3JpcHRXaXRoU2NvcGUiOmZh
bHNlLCJudWxsIjpmYWxzZSwibnVtYmVyIjpmYWxzZSwib2JqZWN0SWQiOmZhbHNlLCJyZWd1bGFy
RXhwcmVzc2lvbiI6ZmFsc2UsInN0cmluZyI6dHJ1ZSwic3ltYm9sIjpmYWxzZSwidGltZXN0YW1w
IjpmYWxzZSwidmFsdWUiOiI4MjY4M0ZDOUExMDAwMDAwMDYyQjA0MkMwMTAwMjk2RTVBMTAwNDJG
RTlGMzVBOUU4NzQzNUE5QkM1NTAxRTE0RTgzRTZCNDYzQzZGNzA2NTcyNjE3NDY5NkY2RTU0Nzk3
MDY1MDAzQzY5NkU3MzY1NzI3NDAwNDY2NDZGNjM3NTZENjU2RTc0NEI2NTc5MDA0NjY0NUY2OTY0
MDA2NDY4M0ZDOUExQUUyMzJCQzJGQkI1RjhCMDAwMDAwNCJ9qA==
, type=DATA, connectionId=63468098c87faf3ba64fece0, isReplaceEvent=false, _ts=1749010851}} 
[TRACE] 2025-06-04 04:21:46.883 - [TAP-6799][主从合并] - Exception skipping - The current exception does not match the skip exception strategy, message: - Map name: *********
- Join key: catalogItem
- Data: Document{{wtCde=1, productId=13, wtUnit=TL, nonStockId=5023, packRemrk=null, prodPackEng=5023 特殊擺件箱座 (L140*W94*H193mm), prodPackZhs=5023 特殊摆件箱座 (L140*W94*H193mm), prodPackZht=5023 特殊擺件箱座 (L140*W94*H193mm), lastModifyDate=DateTime nano 0 seconds 1491820114 timeZone null}} 
[ERROR] 2025-06-04 04:21:46.909 - [TAP-6799][主从合并] - - Map name: *********
- Join key: catalogItem
- Data: Document{{wtCde=1, productId=13, wtUnit=TL, nonStockId=5023, packRemrk=null, prodPackEng=5023 特殊擺件箱座 (L140*W94*H193mm), prodPackZhs=5023 特殊摆件箱座 (L140*W94*H193mm), prodPackZht=5023 特殊擺件箱座 (L140*W94*H193mm), lastModifyDate=DateTime nano 0 seconds 1491820114 timeZone null}} <-- Error Message -->
- Map name: *********
- Join key: catalogItem
- Data: Document{{wtCde=1, productId=13, wtUnit=TL, nonStockId=5023, packRemrk=null, prodPackEng=5023 特殊擺件箱座 (L140*W94*H193mm), prodPackZhs=5023 特殊摆件箱座 (L140*W94*H193mm), prodPackZht=5023 特殊擺件箱座 (L140*W94*H193mm), lastModifyDate=DateTime nano 0 seconds 1491820114 timeZone null}}

<-- Simple Stack Trace -->
Caused by: io.tapdata.exception.TapCodeException: - Map name: *********
- Join key: catalogItem
- Data: Document{{wtCde=1, productId=13, wtUnit=TL, nonStockId=5023, packRemrk=null, prodPackEng=5023 特殊擺件箱座 (L140*W94*H193mm), prodPackZhs=5023 特殊摆件箱座 (L140*W94*H193mm), prodPackZht=5023 特殊擺件箱座 (L140*W94*H193mm), lastModifyDate=DateTime nano 0 seconds 1491820114 timeZone null}}
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.getJoinValueKeyBySource(HazelcastMergeNode.java:1388)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.removeMergeCacheIfUpdateJoinKey(HazelcastMergeNode.java:1987)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.handleUpdateJoinKey(HazelcastMergeNode.java:1963)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.lambda$handleBatchUpdateJoinKey$30(HazelcastMergeNode.java:1916)
	java.base/java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1804)
	...

<-- Full Stack Trace -->
- Map name: *********
- Join key: catalogItem
- Data: Document{{wtCde=1, productId=13, wtUnit=TL, nonStockId=5023, packRemrk=null, prodPackEng=5023 特殊擺件箱座 (L140*W94*H193mm), prodPackZhs=5023 特殊摆件箱座 (L140*W94*H193mm), prodPackZht=5023 特殊擺件箱座 (L140*W94*H193mm), lastModifyDate=DateTime nano 0 seconds 1491820114 timeZone null}}
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.getJoinValueKeyBySource(HazelcastMergeNode.java:1388)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.removeMergeCacheIfUpdateJoinKey(HazelcastMergeNode.java:1987)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.handleUpdateJoinKey(HazelcastMergeNode.java:1963)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.lambda$handleBatchUpdateJoinKey$30(HazelcastMergeNode.java:1916)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.<init>(CompletableFuture.java:1790)
	at java.base/java.util.concurrent.CompletableFuture.asyncRunStage(CompletableFuture.java:1818)
	at java.base/java.util.concurrent.CompletableFuture.runAsync(CompletableFuture.java:2033)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.handleBatchUpdateJoinKey(HazelcastMergeNode.java:1916)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.tryProcess(HazelcastMergeNode.java:291)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$batchProcess$9(HazelcastProcessorBaseNode.java:195)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.batchProcess(HazelcastProcessorBaseNode.java:191)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$initBatchProcessorIfNeed$1(HazelcastProcessorBaseNode.java:136)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode$EventBatchProcessor.lambda$new$0(HazelcastProcessorBaseNode.java:475)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:98)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:122)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode$EventBatchProcessor.<init>(HazelcastProcessorBaseNode.java:473)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.initBatchProcessorIfNeed(HazelcastProcessorBaseNode.java:124)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.doInit(HazelcastProcessorBaseNode.java:87)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.doInit(HazelcastMergeNode.java:162)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:240)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:98)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:122)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1625)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService.submitBlockingTasklets(TaskletExecutionService.java:177)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService.beginExecute(TaskletExecutionService.java:156)
	at com.hazelcast.jet.impl.execution.ExecutionContext.beginExecution(ExecutionContext.java:233)
	at com.hazelcast.jet.impl.JobExecutionService.beginExecution0(JobExecutionService.java:568)
	at com.hazelcast.jet.impl.JobExecutionService.beginExecution(JobExecutionService.java:563)
	at com.hazelcast.jet.impl.operation.StartExecutionOperation.doRun(StartExecutionOperation.java:50)
	at com.hazelcast.jet.impl.operation.AsyncOperation.run(AsyncOperation.java:55)
	at com.hazelcast.spi.impl.operationservice.Operation.call(Operation.java:190)
	at com.hazelcast.spi.impl.operationservice.impl.OperationRunnerImpl.call(OperationRunnerImpl.java:283)
	at com.hazelcast.spi.impl.operationservice.impl.OperationRunnerImpl.run(OperationRunnerImpl.java:258)
	at com.hazelcast.spi.impl.operationservice.impl.OperationRunnerImpl.run(OperationRunnerImpl.java:219)
	at com.hazelcast.spi.impl.operationexecutor.impl.OperationExecutorImpl.run(OperationExecutorImpl.java:411)
	at com.hazelcast.spi.impl.operationexecutor.impl.OperationExecutorImpl.runOrExecute(OperationExecutorImpl.java:438)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.doInvokeLocal(Invocation.java:601)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.doInvoke(Invocation.java:580)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.invoke0(Invocation.java:541)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.invoke(Invocation.java:241)
	at com.hazelcast.spi.impl.operationservice.impl.InvocationBuilderImpl.invoke(InvocationBuilderImpl.java:61)
	at com.hazelcast.jet.impl.MasterContext.invokeOnParticipant(MasterContext.java:294)
	at com.hazelcast.jet.impl.MasterContext.invokeOnParticipants(MasterContext.java:277)
	at com.hazelcast.jet.impl.MasterJobContext.invokeStartExecution(MasterJobContext.java:506)
	at com.hazelcast.jet.impl.MasterJobContext.lambda$onInitStepCompleted$7(MasterJobContext.java:473)
	at com.hazelcast.jet.impl.JobCoordinationService.lambda$submitToCoordinatorThread$54(JobCoordinationService.java:1306)
	at com.hazelcast.jet.impl.JobCoordinationService.lambda$submitToCoordinatorThread$55(JobCoordinationService.java:1327)
	at com.hazelcast.internal.util.executor.CompletableFutureTask.run(CompletableFutureTask.java:64)
	at com.hazelcast.internal.util.executor.CachedExecutorServiceDelegate$Worker.run(CachedExecutorServiceDelegate.java:217)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
	at com.hazelcast.internal.util.executor.HazelcastManagedThread.executeRun(HazelcastManagedThread.java:76)
	at com.hazelcast.internal.util.executor.HazelcastManagedThread.run(HazelcastManagedThread.java:102)

[TRACE] 2025-06-04 04:21:46.909 - [TAP-6799][主从合并] - Job suspend in error handle 
[TRACE] 2025-06-04 04:21:47.863 - [TAP-6799][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] running status set to false 
[TRACE] 2025-06-04 04:21:47.863 - [TAP-6799][MDM_packages] - Incremental sync completed 
[TRACE] 2025-06-04 04:21:47.866 - [TAP-6799][MDM_packages] - PDK connector node stopped: HazelcastSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749010811725 
[TRACE] 2025-06-04 04:21:47.866 - [TAP-6799][MDM_packages] - PDK connector node released: HazelcastSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749010811725 
[TRACE] 2025-06-04 04:21:47.866 - [TAP-6799][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] schema data cleaned 
[TRACE] 2025-06-04 04:21:47.866 - [TAP-6799][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] monitor closed 
[TRACE] 2025-06-04 04:21:47.867 - [TAP-6799][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] close complete, cost 5 ms 
[TRACE] 2025-06-04 04:21:47.867 - [TAP-6799][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] running status set to false 
[TRACE] 2025-06-04 04:21:47.898 - [TAP-6799][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] schema data cleaned 
[TRACE] 2025-06-04 04:21:47.898 - [TAP-6799][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] monitor closed 
[TRACE] 2025-06-04 04:21:47.898 - [TAP-6799][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] close complete, cost 31 ms 
[TRACE] 2025-06-04 04:21:47.898 - [TAP-6799][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] running status set to false 
[INFO ] 2025-06-04 04:21:47.900 - [TAP-6799][MDM_packages][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-cccd610b-801a-4b1d-aa48-d5f4ae088cff 
[INFO ] 2025-06-04 04:21:47.901 - [TAP-6799][MDM_packages][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-cccd610b-801a-4b1d-aa48-d5f4ae088cff 
[INFO ] 2025-06-04 04:21:47.901 - [TAP-6799][MDM_packages][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de54-8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d-63468098c87faf3ba64fece0] schema data cleaned 
[INFO ] 2025-06-04 04:21:47.901 - [TAP-6799][MDM_packages][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-86417ef0-f6f6-42dd-aa2c-f2054b9fda30 
[INFO ] 2025-06-04 04:21:47.901 - [TAP-6799][MDM_packages][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-86417ef0-f6f6-42dd-aa2c-f2054b9fda30 
[INFO ] 2025-06-04 04:21:47.901 - [TAP-6799][MDM_packages][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de54-8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:21:47.902 - [TAP-6799][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] schema data cleaned 
[TRACE] 2025-06-04 04:21:47.902 - [TAP-6799][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] monitor closed 
[TRACE] 2025-06-04 04:21:47.902 - [TAP-6799][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] close complete, cost 4 ms 
[TRACE] 2025-06-04 04:21:47.902 - [TAP-6799][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] running status set to false 
[TRACE] 2025-06-04 04:21:47.909 - [TAP-6799][FDM_product] - Incremental sync completed 
[TRACE] 2025-06-04 04:21:47.909 - [TAP-6799][FDM_product] - PDK connector node stopped: HazelcastSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010811626 
[TRACE] 2025-06-04 04:21:47.909 - [TAP-6799][FDM_product] - PDK connector node released: HazelcastSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010811626 
[TRACE] 2025-06-04 04:21:47.909 - [TAP-6799][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] schema data cleaned 
[TRACE] 2025-06-04 04:21:47.909 - [TAP-6799][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] monitor closed 
[TRACE] 2025-06-04 04:21:47.909 - [TAP-6799][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] close complete, cost 6 ms 
[TRACE] 2025-06-04 04:21:47.909 - [TAP-6799][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] running status set to false 
[INFO ] 2025-06-04 04:21:47.910 - [TAP-6799][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-8ac33eee-6f99-4ed6-a053-1b71cf63d69a 
[INFO ] 2025-06-04 04:21:47.910 - [TAP-6799][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-8ac33eee-6f99-4ed6-a053-1b71cf63d69a 
[INFO ] 2025-06-04 04:21:47.910 - [TAP-6799][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de54-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[INFO ] 2025-06-04 04:21:47.911 - [TAP-6799][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-093ba09b-8bf1-484d-b968-d29f033abcbf 
[INFO ] 2025-06-04 04:21:47.911 - [TAP-6799][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-093ba09b-8bf1-484d-b968-d29f033abcbf 
[INFO ] 2025-06-04 04:21:47.911 - [TAP-6799][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de54-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:21:47.912 - [TAP-6799][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] schema data cleaned 
[TRACE] 2025-06-04 04:21:47.912 - [TAP-6799][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] monitor closed 
[TRACE] 2025-06-04 04:21:47.912 - [TAP-6799][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] close complete, cost 3 ms 
[TRACE] 2025-06-04 04:21:47.912 - [TAP-6799][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] running status set to false 
[INFO ] 2025-06-04 04:21:47.914 - [TAP-6799][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-33f14cd8-7494-46b5-b093-0fe567c0a721 
[INFO ] 2025-06-04 04:21:47.914 - [TAP-6799][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-33f14cd8-7494-46b5-b093-0fe567c0a721 
[INFO ] 2025-06-04 04:21:47.914 - [TAP-6799][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de54-1674c3ea-16fc-4674-9a27-0cd5951e0f48-63468098c87faf3ba64fece0] schema data cleaned 
[INFO ] 2025-06-04 04:21:47.914 - [TAP-6799][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-df619768-3277-4b7f-928b-00a582b0882c 
[INFO ] 2025-06-04 04:21:47.914 - [TAP-6799][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-df619768-3277-4b7f-928b-00a582b0882c 
[INFO ] 2025-06-04 04:21:47.914 - [TAP-6799][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de54-1674c3ea-16fc-4674-9a27-0cd5951e0f48-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:21:47.915 - [TAP-6799][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] schema data cleaned 
[TRACE] 2025-06-04 04:21:47.915 - [TAP-6799][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] monitor closed 
[TRACE] 2025-06-04 04:21:47.915 - [TAP-6799][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] close complete, cost 2 ms 
[TRACE] 2025-06-04 04:21:47.915 - [TAP-6799][主从合并] - Node 主从合并[48ad06a3-b6bb-40e0-9bf4-cd4c8b390e28] running status set to false 
[TRACE] 2025-06-04 04:21:47.915 - [TAP-6799][主从合并] - Destroy merge cache resource: ********* 
[TRACE] 2025-06-04 04:21:47.917 - [TAP-6799][主从合并] - Node 主从合并[48ad06a3-b6bb-40e0-9bf4-cd4c8b390e28] schema data cleaned 
[TRACE] 2025-06-04 04:21:47.917 - [TAP-6799][主从合并] - Node 主从合并[48ad06a3-b6bb-40e0-9bf4-cd4c8b390e28] monitor closed 
[TRACE] 2025-06-04 04:21:47.917 - [TAP-6799][主从合并] - Node 主从合并[48ad06a3-b6bb-40e0-9bf4-cd4c8b390e28] close complete, cost 1 ms 
[TRACE] 2025-06-04 04:21:47.917 - [TAP-6799][MDM_catalogItem_tmp_6799] - Node MDM_catalogItem_tmp_6799[c1f22337-5eaf-429f-99f6-8fd1a77ed0b0] running status set to false 
[TRACE] 2025-06-04 04:21:47.935 - [TAP-6799][MDM_catalogItem_tmp_6799] - PDK connector node stopped: HazelcastTargetPdkDataNode_c1f22337-5eaf-429f-99f6-8fd1a77ed0b0_1749010811817 
[TRACE] 2025-06-04 04:21:47.936 - [TAP-6799][MDM_catalogItem_tmp_6799] - PDK connector node released: HazelcastTargetPdkDataNode_c1f22337-5eaf-429f-99f6-8fd1a77ed0b0_1749010811817 
[TRACE] 2025-06-04 04:21:47.936 - [TAP-6799][MDM_catalogItem_tmp_6799] - Node MDM_catalogItem_tmp_6799[c1f22337-5eaf-429f-99f6-8fd1a77ed0b0] schema data cleaned 
[TRACE] 2025-06-04 04:21:47.936 - [TAP-6799][MDM_catalogItem_tmp_6799] - Node MDM_catalogItem_tmp_6799[c1f22337-5eaf-429f-99f6-8fd1a77ed0b0] monitor closed 
[TRACE] 2025-06-04 04:21:47.936 - [TAP-6799][MDM_catalogItem_tmp_6799] - Node MDM_catalogItem_tmp_6799[c1f22337-5eaf-429f-99f6-8fd1a77ed0b0] close complete, cost 18 ms 
[INFO ] 2025-06-04 04:21:49.973 - [TAP-6799] - Task [TAP-6799] cannot retry, reason: Task retry service not start 
[INFO ] 2025-06-04 04:21:54.981 - [TAP-6799] - Task [TAP-6799] cannot retry, reason: Task retry service not start 
[TRACE] 2025-06-04 04:21:54.981 - [TAP-6799] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-04 04:21:55.988 - [TAP-6799] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@4ffdbe6d 
[TRACE] 2025-06-04 04:21:55.989 - [TAP-6799] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@79f2263b 
[TRACE] 2025-06-04 04:21:56.114 - [TAP-6799] - Stop task milestones: 683ec91db023c8285961de54(TAP-6799)  
[TRACE] 2025-06-04 04:21:56.115 - [TAP-6799] - Stopped task aspect(s) 
[TRACE] 2025-06-04 04:21:56.115 - [TAP-6799] - Snapshot order controller have been removed 
[INFO ] 2025-06-04 04:21:56.115 - [TAP-6799] - Task stopped. 
[TRACE] 2025-06-04 04:21:56.156 - [TAP-6799] - Remove memory task client succeed, task: TAP-6799[683ec91db023c8285961de54] 
[TRACE] 2025-06-04 04:21:56.156 - [TAP-6799] - Destroy memory task client cache succeed, task: TAP-6799[683ec91db023c8285961de54] 
