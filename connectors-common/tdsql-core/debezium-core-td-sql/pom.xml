<?xml version="1.0"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>io.tapdata</groupId>
        <artifactId>tdsql-core</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>debezium-core-td-sql</artifactId>
    <name>Debezium Core TD SQL</name>
    <version>1.5.4.Final</version>
    <packaging>jar</packaging>

    <properties>
        <version.kafka.scala>2.12</version.kafka.scala>
        <!-- Kafka and it's dependencies MUST reflect what the Kafka version uses -->
        <version.kafka>2.7.0</version.kafka>
        <version.curator>4.2.0</version.curator>
        <version.zookeeper>3.5.8</version.zookeeper>
        <version.jackson>2.10.5</version.jackson>
        <version.org.slf4j>1.7.30</version.org.slf4j>
        <version.log4j>1.2.17</version.log4j>
        <version.netty>4.1.51.Final</version.netty>
        <!-- check new release version at https://github.com/confluentinc/schema-registry/releases -->
        <version.confluent.platform>6.0.2</version.confluent.platform>
        <version.apicurio>1.3.2.Final</version.apicurio>

        <!-- Database drivers, should align with databases in debezium-build-parent -->
        <version.postgresql.driver>42.2.14</version.postgresql.driver>
        <version.mysql.driver>8.0.33</version.mysql.driver>
        <version.mysql.binlog>0.28.0</version.mysql.binlog>
        <version.mongo.driver>4.2.1</version.mongo.driver>
        <version.sqlserver.driver>7.2.2.jre8</version.sqlserver.driver>
        <version.oracle.driver>12.2.0.1</version.oracle.driver>
        <version.db2.driver>11.5.0.0</version.db2.driver>
        <version.cassandra.driver>3.5.0</version.cassandra.driver>

        <!-- Connectors -->
        <version.com.google.protobuf>3.8.0</version.com.google.protobuf>
        <version.dropwizard>4.0.1</version.dropwizard>
        <version.guava>30.0-jre</version.guava>
        <version.jaxb>2.3.1</version.jaxb>
        <version.jsqlparser>2.1</version.jsqlparser>
        <version.jetty>9.4.12.v20180830</version.jetty>

        <!-- Vitess dependencies -->
        <version.vitess.grpc>9.0.0</version.vitess.grpc>
        <version.gson>2.7</version.gson>
        <version.grpc>1.33.0</version.grpc>
        <version.joda>2.10.1</version.joda>
        <version.google.protos>1.17.0</version.google.protos>

        <!-- Tracing -->
        <version.opentracing.strimzi>0.33.0</version.opentracing.strimzi>

        <!-- HTTP client -->
        <version.okhttp>4.8.1</version.okhttp>

        <!-- Scripting -->
        <version.groovy>3.0.7</version.groovy>
        <version.graalvm.js>20.0.0</version.graalvm.js>

        <!-- Testing -->
        <version.junit>4.13.1</version.junit>
        <version.fest>1.4</version.fest>
        <version.jmh>1.21</version.jmh>
        <version.mockito>3.0.0</version.mockito>
        <version.awaitility>4.0.1</version.awaitility>
        <version.testcontainers>1.15.1</version.testcontainers>
        <version.jsonpath>2.4.0</version.jsonpath>
        <version.jsonassert>1.5.0</version.jsonassert>
        <version.org.reflections>0.9.12</version.org.reflections>
        <version.cassandra.unit>*******</version.cassandra.unit>
    </properties>
    <dependencies>
        <dependency>
            <groupId>io.debezium</groupId>
            <artifactId>debezium-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>${version.org.slf4j}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.kafka</groupId>
            <artifactId>connect-api</artifactId>
            <version>${version.kafka}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.kafka</groupId>
            <artifactId>connect-transforms</artifactId>
            <version>${version.kafka}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.kafka</groupId>
            <artifactId>connect-json</artifactId>
            <version>${version.kafka}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
            <version>${version.jackson}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>${version.jackson}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
            <version>${version.jackson}</version>
        </dependency>

        <!-- OpenTracing Integration, Strimzi version is not aligned with Quarkus -->
        <dependency>
            <groupId>io.opentracing</groupId>
            <artifactId>opentracing-api</artifactId>
            <version>${version.opentracing.strimzi}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>io.opentracing</groupId>
            <artifactId>opentracing-util</artifactId>
            <version>${version.opentracing.strimzi}</version>
            <scope>provided</scope>
        </dependency>

        <!-- Testing -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-log4j12</artifactId>
            <version>${version.org.slf4j}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
            <version>${version.log4j}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>${version.junit}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.easytesting</groupId>
            <artifactId>fest-assert</artifactId>
            <version>${version.fest}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.reflections</groupId>
            <artifactId>reflections</artifactId>
            <version>${version.org.reflections}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.codehaus.groovy</groupId>
            <artifactId>groovy-json</artifactId>
            <version>${version.groovy}</version>
            <scope>test</scope>
        </dependency>

        <!-- Used for unit testing with Kafka -->
        <dependency>
            <groupId>org.apache.kafka</groupId>
            <artifactId>kafka_${version.kafka.scala}</artifactId>
            <version>${version.kafka}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.curator</groupId>
            <artifactId>curator-test</artifactId>
            <version>${version.curator}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.zookeeper</groupId>
            <artifactId>zookeeper</artifactId>
            <version>${version.zookeeper}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.confluent</groupId>
            <artifactId>kafka-connect-avro-converter</artifactId>
            <version>${version.confluent.platform}</version>
            <scope>test</scope>
        </dependency>
        <!-- open libraries -->
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>${version.guava}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.google.code.findbugs</groupId>
                    <artifactId>jsr305</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.google.errorprone</groupId>
                    <artifactId>error_prone_annotations</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.google.j2objc</groupId>
                    <artifactId>j2objc-annotations</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.checkerframework</groupId>
                    <artifactId>checker-qual</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>
    <build>
        <resources>
            <!-- Apply the properties set in the POM to the resource files -->
            <resource>
                <filtering>true</filtering>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/build.properties</include>
                </includes>
            </resource>
        </resources>
    </build>
</project>
