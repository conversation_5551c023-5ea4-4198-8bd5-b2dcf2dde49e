CONNECTOR_CAPABILITIES_START='s The scanning results show that the data source implements the following functions:%s
TOTAL_CAPABILITIES_OF=%s have %s capabilities be implemented.%s
error.insert.record.into.mongodb=Write ${number}$ records failed, because ${message}$

ERROR.CASE=Case of ERROR:\n
WARN.CASE=Case of WARN:\n
SUCCEED.CASE=Case of SUCCEED:\n

TEST_SUCCEED_END=%s(\u2714)Congratulations! %s has passed all tests!
TEST_ERROR_END=%s(\u2573) Oops, PDK %s didn't pass all tests, please resolve above issue(s) and try again.
SUCCEED_WITH_WARN=but with some warn,please check your tests.
ONCE_HISTORY=%s\u7ED3\u679C\uFF1A%s\uFF0C \u901A\u8FC7\u7528\u4F8B\uFF1A%s\uFF0C \u8B66\u544A\u7528\u4F8B\uFF1A%s\uFF0C \u9519\u8BEF\u7528\u4F8B\uFF1A%s\uFF0C \u8DF3\u8FC7\u7528\u4F8B\uFF1A%s

NOT_SUPPORT_FUNCTION=It is detected that there is an unimplemented method in the data source, \nand the test process of the current case is terminated. \nThe unimplemented method is:%s
TEST_RESULT_SUCCEED=Test succeed
TEST_RESULT_ERROR=Test error
SUCCEED_COUNT_LABEL=succeed:
WARN_COUNT_LABEL=warn:
ERROR_COUNT_LABEL=error:
HAS_WARN_COUNT=However, %s warning occurred in one of the use cases.
TEST_OF_SUCCEED=Test of succeed
TEST_OF_WARN=Test of warn
TEST_OF_ERROR=Test of error

Test.WriteRecordTestV2=Test about CreateTableFunctionV2
Test.WriteRecordTestV2.case.sourceTest=Test case 1 about CreateTableFunctionV2
Test.WriteRecordTest=Test about CreateTableFunction
Test.WriteRecordTest.case.sourceTest=Test case 1 about CreateTableFunction
Test.WriteRecordTest.case.sourceTest2=Test case 2 about CreateTableFunction
Test.WriteRecordWithQueryTest=Test about WriteRecordWithQueryTest
Test.WriteRecordWithQueryTest.case.sourceTest=Test case 1 about WriteRecordWithQueryTest
Test.WriteRecordWithQueryTest.case.sourceTest2=Test case 2 about WriteRecordWithQueryTest

Test.TestNotImplementFunErr=\u6D4B\u8BD5\u4E00\u4E2A\u6CA1\u6709\u5B9E\u73B0\u65B9\u6CD5\u7684\u6D4B\u8BD5\u7C7B\uFF08\u793A\u4F8B\uFF09
Test.TestNotImplementFunErr.case.sourceTest=\u6CA1\u6709\u5B9E\u73B0\u65B9\u6CD5\u7684\u6D4B\u8BD5\u7C7B\u7684\u6D4B\u8BD5\u7528\u4F8B\uFF08\u793A\u4F8B\uFF09

test.queryByFilterTest=QueryByFilterFunction\u57FA\u4E8E\u5339\u914D\u5B57\u6BB5\u67E5\u8BE2\uFF08\u4F9D\u8D56WriteRecordFunction\uFF09
test.queryByFilterTest.insertWithQuery=\u7528\u4F8B1\uFF0C\u63D2\u5165\u6570\u636E\u80FD\u6B63\u5E38\u67E5\u8BE2\u5E76\u8FDB\u884C\u503C\u6BD4\u5BF9
test.queryByFilterTest.queryWithLotTapFilter=\u7528\u4F8B2\uFF0C\u67E5\u8BE2\u6570\u636E\u65F6\uFF0C\u6307\u5B9A\u591A\u4E2ATapFilter\uFF0C\u9700\u8981\u8FD4\u56DE\u591A\u4E2AFilterResult\uFF0C\u505A\u4E00\u4E00\u5BF9\u5E94

RecordEventExecute.insert.assert.error=You inserted %s record, but the insertion operation failed.
RecordEventExecute.insert.assert.succeed=You inserted %s record, and the insertion operation succeed.
RecordEventExecute.update.assert.error=You modified %s records, but the modification failed.
RecordEventExecute.update.assert.succeed=You have modified %s records, and the modification is successful.
RecordEventExecute.delete.assert.error=You deleted %s records, but the deletion operation failed.
RecordEventExecute.delete.assert.succeed=You have deleted %s records, and the deletion operation is successful.
RecordEventExecute.drop.table.error=Error in deleting table. Exception caught during deletion.
RecordEventExecute.drop.notCatch.thrower=No exception was detected during the table deletion operation.
RecordEventExecute.drop.error.not.support.function=Please implement a function named Drop Table function.
RecordEventExecute.drop.table.succeed=Successfully deleted table with name: %s .

WriteRecordTest.sourceTest2.verify.firstInsert=\u63D2\u5165\u7B56\u7565\u662Fupdate_on_exists\uFF0C\u65B0\u63D2\u5165\u5E94\u8BE5\u662F\u63D2\u5165%s\u4E2A,\u4F46\u662F\u5B9E\u9645\u4E0A\u8FD4\u56DE\u7ED3\u679C\u4E3A%s\uFF0C\u63D2\u5165\u5931\u8D25\u3002
WriteRecordTest.sourceTest2.verify.firstInsert.succeed=\u63D2\u5165\u7B56\u7565\u662Fupdate_on_exists\uFF0C\u65B0\u63D2\u5165\u662F%s\u4E2A,\u5B9E\u9645\u4E0A\u64CD\u4F5C\u603B\u6570\u4E5F\u4E3A%s\uFF0C\u63D2\u5165\u6210\u529F\u3002
WriteRecordTest.sourceTest2.verify.insertAfter.NotEquals=\u63D2\u5165\u7B56\u7565\u662Fupdate_on_exists\uFF0C\u63D2\u5165%s\u6761\u6570\u636E\uFF0C\u518D\u6B21\u63D2\u5165\u76F8\u540C\u4E3B\u952E\u7684%s\u6761\u6570\u636E.\u63D2\u5165\u7B56\u7565\u662Fignore_on_exists,\u4F46\u662F\u7ED3\u679C\u663E\u793A\u63D2\u5165\u6570\u548C\u4FEE\u6539\u6570\u4E4B\u548C\u4E0D\u7B49\u4E8E%s\uFF0C\u63D2\u5165\u5931\u8D25\u3002
WriteRecordTest.sourceTest2.verify.insertAfter.WarnInsert=\u63D2\u5165\u7B56\u7565\u662Fupdate_on_exists\uFF0C\u4F46\u662F\u63D2\u5165\u64CD\u4F5C\u8FD4\u56DE\u63D2\u5165\u6570\u662F%s,\u6B64\u65F6\u63D2\u5165\u6570\u5E94\u4E3A0\u4F46\u4E0D\u4E3A0\uFF0C\u53EF\u89C2\u6D4B\u6027\u6570\u636E\u53EF\u80FD\u4E0D\u51C6\u786E\u3002
WriteRecordTest.sourceTest2.verify.insertAfter.WarnUpdate=\u63D2\u5165\u7B56\u7565\u662Fupdate_on_exists\uFF0C\u4F46\u662F\u63D2\u5165\u64CD\u4F5C\u8FD4\u56DE\u66F4\u65B0\u6570\u662F%s,\u6B64\u65F6\u66F4\u65B0\u6570\u5E94\u4E3A%s\u4F46\u4E0D\u4E3A%s\uFF0C\u7ED3\u679C\u663E\u793A\u4E0D\u6B63\u786E\u3002
WriteRecordTest.sourceTest2.verify.insertAfter.ErrorOther=\u63D2\u5165\u7B56\u7565\u662Fupdate_on_exists\uFF0C\u63D2\u5165\u64CD\u4F5C\u8FD4\u56DE\u66F4\u65B0\u6570\u662F%s,\u63D2\u5165\u6570\u4E3A%s,\u6B64\u65F6\u66F4\u65B0\u6570\u5E94\u4E3A%s,\u63D2\u5165\u6570\u5E94\u4E3A0\uFF0C\u64CD\u4F5C\u5931\u8D25\u3002
WriteRecordTest.sourceTest2.verify.insertAfter.Succeed=\u63D2\u5165\u7B56\u7565\u662Fupdate_on_exists\uFF0C\u63D2\u5165%s,\u7ED3\u679C\u663E\u793A\u66F4\u65B0%s\u6761\uFF0C\u63D2\u5165%s\u6761\u3002\u6548\u679C\u7B26\u5408\u9884\u671F\uFF0C\u63D2\u5165\u6210\u529F\u3002

WriteRecordTest.sourceTest2.IOE.verify.insertAfter.succeed=\u63D2\u5165\u7B56\u7565\u662Fignore_on_exists\uFF0C\u63D2\u5165\u64CD\u4F5C\u8FD4\u56DE\u63D2\u5165\u6570\u662F%s,\u66F4\u65B0\u6570\u4E3A%s,\u5B9E\u9645\u7ED3\u679C\u5E94\u8FD4\u56DE\u63D2\u5165\u65700\uFF0C\u66F4\u65B0\u65700\u3002\u7ED3\u679C\u663E\u793A\u7B26\u5408\u9884\u671F\uFF0C\u63D2\u5165\u64CD\u4F5C\u6210\u529F\u3002
WriteRecordTest.sourceTest2.IOE.verify.insertAfter.error=\u63D2\u5165\u7B56\u7565\u662Fignore_on_exists\uFF0C\u4F46\u662F\u63D2\u5165\u64CD\u4F5C\u8FD4\u56DE\u63D2\u5165\u6570\u662F%s,\u66F4\u65B0\u6570\u4E3A%s,\u5B9E\u9645\u7ED3\u679C\u5E94\u8FD4\u56DE\u63D2\u5165\u65700\uFF0C\u66F4\u65B0\u65700\uFF0C\u63D2\u5165\u64CD\u4F5C\u5931\u8D25\u3002


WriteRecordWithQueryTest.sourceTest.insert.error.null=Query results should be not null.
WriteRecordWithQueryTest.sourceTest.insert.succeed.notNull=Succeed query by advance when insert record,the filter Results not null.
WriteRecordWithQueryTest.sourceTest.insert.error.nullResult=Query results should be not null.
WriteRecordWithQueryTest.sourceTest.insert.succeed.notNullResult=Succeed query by advance when insert record,the filter Results not empty results.
WriteRecordWithQueryTest.sourceTest.insert.error.notEquals=Succeed insert record,However, the query results show that the inserted data is inconsistent.
WriteRecordWithQueryTest.sourceTest.insert.succeed.equals=Succeed insert record, and the inserted record was compared successfully.

WriteRecordWithQueryTest.sourceTest.update.error.null=Modify the inserted record and compare the results before and after the insertion. Discovery query returned null.
WriteRecordWithQueryTest.sourceTest.update.succeed.notNull=Modify the inserted record and compare the results before and after the insertion. The query return is not null.
WriteRecordWithQueryTest.sourceTest.update.error.nullResult=Modify the inserted record and compare the results before and after the insertion. The query result returned is null.
WriteRecordWithQueryTest.sourceTest.update.succeed.notNullResult=Modify the inserted record and compare the results before and after the insertion. The query result returned is not null.
WriteRecordWithQueryTest.sourceTest.update.error.notEquals=Modify the inserted record and compare the results before and after the insertion. Inconsistent results.
WriteRecordWithQueryTest.sourceTest.update.succeed.equals=Modify the inserted record and compare the results before and after the insertion. Consistent results.

WriteRecordWithQueryTest.sourceTest.delete.error.null=Deleted the inserted record and compare the results before and after the insertion. Discovery query returned null.
WriteRecordWithQueryTest.sourceTest.delete.succeed.notNull=Deleted the inserted record and compare the results before and after the insertion. The query return is not null.
WriteRecordWithQueryTest.sourceTest.delete.succeed.nullResult=Deleted the inserted record and compare the results before and after the insertion. The query result returned is null and deleted is succeed.
WriteRecordWithQueryTest.sourceTest.delete.error.notNullResult=Deleted the inserted record and compare the results before and after the insertion. The query result returned is not null,but deleted is not succeed.

batch_read_function=%s%s.batchReadFunction:This method supports reading data in batches.%s
stream_read_function=%s%s.streamReadFunction:This method supports incremental data reading.%s
batch_count_function=%s%s.batchCountFunction:This method supports obtaining the total number of data records.%s
timestamp_to_stream_offset_function=%s%s.timestampToStreamOffsetFunction:%s
write_record_function=%s%s.writeRecordFunction:This method supports batch writing.%s
query_by_advance_filter_function=%s%s.queryByAdvanceFilterFunction:This method support query data by advance filter.%s
drop_table_function=%s%s.dropTableFunction:This method support drop table.%s
create_index_function=%s%s.createIndexFunction:This method support create index for cloumn.%s
get_table_names_function=%s%s.getTableNamesFunction:This methos support get table name.%s
error_handle_function=%s%s.errorHandleFunction:This method support handle error and retry.%s

please_support_create_table_function=Please support create table function.

null_after_create_table=Exec create table table function error,please check the create table function.
create_table_table_not_exists=After exec create table table ,the table is exists.

connectionTest.test=\u8FDE\u63A5\u6D4B\u8BD5\uFF0C\u5FC5\u6D4B\u65B9\u6CD5
connectionTest.testConnectionTest=\u7528\u4F8B1\uFF0C\u8FD4\u56DE\u6070\u5F53\u7684\u6D4B\u8BD5\u7ED3\u679C
connectionTest.testConnectionTest.errorVCL=Version\uFF0C Connection\uFF0C Login\u7684TestItem\u9879\u6CA1\u6709\u4E0A\u62A5\u3002
connectionTest.testConnectionTest.succeedVCL=Version\uFF0C Connection\uFF0C Login\u7684TestItem\u9879\u4E0A\u62A5\u6210\u529F\u3002
connectionTest.testConnectionTest.errorBatchRead=\u5DF2\u7ECF\u5B9E\u73B0\u4E86BatchReadFunction\uFF0C\u4F46\u662FRead\u6CA1\u6709\u4E0A\u62A5\u65F6\u3002
connectionTest.testConnectionTest.succeedBatchRead=\u5DF2\u7ECF\u5B9E\u73B0\u4E86BatchReadFunction\uFF0CRead\u4E0A\u62A5\u6210\u529F\u3002
connectionTest.testConnectionTest.errorStreamRead=\u5DF2\u7ECF\u5B9E\u73B0\u4E86StreamReadFunction\uFF0C\u4F46\u662FRead log\u6CA1\u6709\u4E0A\u62A5\u65F6\u3002
connectionTest.testConnectionTest.succeedStreamRead=\u5DF2\u7ECF\u5B9E\u73B0\u4E86StreamReadFunction\uFF0CRead log\u4E0A\u62A5\u6210\u529F\u3002
connectionTest.testConnectionTest.errorWriteRecord=\u5DF2\u7ECF\u5B9E\u73B0\u4E86WriteRecordFunction\uFF0C\u4F46\u662FWrite\u6CA1\u6709\u4E0A\u62A5\u65F6\u3002
connectionTest.testConnectionTest.succeedWriteRecord=\u5DF2\u7ECF\u5B9E\u73B0\u4E86StreamReadFunction\uFF0CWrite\u4E0A\u62A5\u6210\u529F\u3002