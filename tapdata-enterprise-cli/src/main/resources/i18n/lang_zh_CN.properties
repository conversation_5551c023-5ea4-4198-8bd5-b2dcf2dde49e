CONNECTOR_CAPABILITIES_START= \u7684\u626B\u63CF\u7ED3\u679C\u663E\u793A\uFF0C\u6570\u636E\u6E90\u5B9E\u73B0\u4E86\u4EE5\u4E0B\u80FD\u529B\u65B9\u6CD5:%s
TOTAL_CAPABILITIES_OF=%s\u603B\u5171\u5177\u5907\u7684\u80FD\u529B\u6709%s\u4E2A\u3002%s

ERROR.CASE=\u6D4B\u8BD5\u5931\u8D25\u7684\u7528\u4F8B\uFF1A\n
WARN.CASE=\u8D28\u91CF\u8B66\u544A\u7528\u4F8B\uFF1A\n
SUCCEED.CASE=\u6D4B\u8BD5\u6210\u529F\u7684\u7528\u4F8B\uFF1A\n

TEST_SUCCEED_END=%s(\u2714)\u975E\u5E38\u68D2! %s \u7684\u6240\u6709\u6D4B\u8BD5\u7528\u4F8B\u5168\u90E8\u901A\u8FC7\u6D4B\u8BD5\u6D41\u7A0B!
TEST_ERROR_END=%s(\u2573) Oops, PDK %s \u672A\u901A\u8FC7\u6240\u6709\u6D4B\u8BD5\uFF0C\u8BF7\u89E3\u51B3\u4EE5\u4E0A\u95EE\u9898\uFF0C\u7136\u540E\u91CD\u8BD5\u3002
SUCCEED_WITH_WARN=\u4F46\u662F\u90E8\u5206\u6D4B\u8BD5\u7ED3\u679C\u663E\u793A\u4E3A\u8B66\u544A\uFF0C\u8BF7\u68C0\u67E5\u6D4B\u8BD5\u7528\u4F8B\u3002
SUMMARY_END= \u25A3 \u9488\u5BF9 %s \u6570\u636E\u6E90\u8FDB\u884C\u80FD\u529B\u8D28\u91CF\u68C0\u6D4B\uFF0C\u5DF2\u8986\u76D6\u7528\u4F8B\u603B\u8BA1 %s \u6761\u3002\n \t\u25C9 \u7D2F\u8BA1\u6267\u884C\u7528\u4F8B %s \u6761\uFF1A \u6210\u529F\u7528\u4F8B [%s] \u6761 | \u544A\u8B66\u7528\u4F8B [%s] \u6761 | \u5931\u8D25\u7528\u4F8B [%s] \u6761\u3002\n \t\u25C8 \u7D2F\u8BA1\u8DF3\u8FC7\u7528\u4F8B %s \u6761\u3002\n

ONCE_HISTORY=%s\u7ED3\u679C\uFF1A%s\uFF0C \u901A\u8FC7\u7528\u4F8B\uFF1A%s\uFF0C \u8B66\u544A\u7528\u4F8B\uFF1A%s\uFF0C \u9519\u8BEF\u7528\u4F8B\uFF1A%s\uFF0C \u8DF3\u8FC7\u7528\u4F8B\uFF1A%s

#NOT_SUPPORT_FUNCTION=\u68C0\u6D4B\u5230\u6570\u636E\u6E90\u5B58\u5728\u672A\u5B9E\u73B0\u7684\u65B9\u6CD5\uFF0C\u7EC8\u6B62\u4E86\u5F53\u524D\u7528\u4F8B\u7684\u6D4B\u8BD5\u6D41\u7A0B\uFF0C\u672A\u5B9E\u73B0\u7684\u65B9\u6CD5\u4E3A\uFF1A%s
TEST_RESULT_SUCCEED=\u6D4B\u8BD5\u6210\u529F
TEST_RESULT_ERROR=\u6D4B\u8BD5\u5931\u8D25
TEST_RESULT_WARN=\u6D4B\u8BD5\u544A\u8B66
base.jumpCase.list=\u8DF3\u8FC7\u7684\u6D4B\u8BD5\u7528\u4F8B\u5217\u8868\uFF1A
SUCCEED_COUNT_LABEL=\u901A\u8FC7\u7528\u4F8B\uFF1A
WARN_COUNT_LABEL=\u544A\u8B66\u7528\u4F8B\uFF1A
ERROR_COUNT_LABEL=\u9519\u8BEF\u7528\u4F8B\uFF1A
HAS_WARN_COUNT=\u4F46\u662F\u5176\u4E2D\u6709%s\u6761\u7528\u4F8B\u53D1\u751F\u4E86\u8B66\u544A\u3002
TEST_OF_SUCCEED=\u6210\u529F\u7684\u65AD\u8A00
TEST_OF_WARN=\u544A\u8B66\u7684\u65AD\u8A00
TEST_OF_ERROR=\u5931\u8D25\u7684\u65AD\u8A00

test.writeRecordTest=WriteRecordFunction \u5199\u6570\u636E
test.writeRecordTest.case.sourceTest1=\u7528\u4F8B1\uFF0C\u589E\u5220\u6539\u6570\u91CF\u8FD4\u56DE\u6B63\u786E
test.writeRecordTest.case.sourceTest2=\u7528\u4F8B2\uFF0C\u591A\u6B21\u63D2\u5165\u76F8\u540C\u4E3B\u952E\u7684\u6570\u636E\uFF0C\u63D2\u5165\u4FEE\u6539\u6570\u91CF\u5E94\u8BE5\u6B63\u786E
test.writeRecordTest.case.sourceTest3=\u7528\u4F8B3\uFF0C\u5220\u9664\u4E0D\u5B58\u5728\u7684\u6570\u636E\u65F6\uFF0C\u5220\u9664\u6570\u91CF\u5E94\u8BE5\u6B63\u786E
test.writeRecordTest.case.sourceTest4=\u7528\u4F8B4\uFF0C\u4FEE\u6539\u4E0D\u5B58\u5728\u7684\u6570\u636E\uFF0C\u63D2\u5165\u4FEE\u6539\u6570\u91CF\u5E94\u8BE5\u6B63\u786E

Test.TestNotImplementFunErr=\u6D4B\u8BD5\u4E00\u4E2A\u6CA1\u6709\u5B9E\u73B0\u65B9\u6CD5\u7684\u6D4B\u8BD5\u7C7B\uFF08\u793A\u4F8B\uFF09
Test.TestNotImplementFunErr.case.sourceTest=\u6CA1\u6709\u5B9E\u73B0\u65B9\u6CD5\u7684\u6D4B\u8BD5\u7C7B\u7684\u6D4B\u8BD5\u7528\u4F8B\uFF08\u793A\u4F8B\uFF09

test.queryByFilterTest=QueryByFilterFunction\u57FA\u4E8E\u5339\u914D\u5B57\u6BB5\u67E5\u8BE2\uFF08\u4F9D\u8D56WriteRecordFunction\uFF09
test.queryByFilterTest.insertWithQuery=\u7528\u4F8B1\uFF0C\u63D2\u5165\u6570\u636E\u80FD\u6B63\u5E38\u67E5\u8BE2\u5E76\u8FDB\u884C\u503C\u6BD4\u5BF9
queryByFilter.insert.error=\u4F7F\u7528WriteRecordFunction\u63D2\u5165\u4E86%s\u6761\u5168\u7C7B\u578B\uFF08\u8986\u76D6TapType\u768411\u4E2D\u7C7B\u578B\u6570\u636E\uFF09\u6570\u636E\uFF0C\u76EE\u6807\u8868\u540D\u79F0\u4E3A%s\u3002
test.queryByFilterTest.queryWithLotTapFilter=\u7528\u4F8B2\uFF0C\u67E5\u8BE2\u6570\u636E\u65F6\uFF0C\u6307\u5B9A\u591A\u4E2ATapFilter\uFF0C\u9700\u8981\u8FD4\u56DE\u591A\u4E2AFilterResult\uFF0C\u505A\u4E00\u4E00\u5BF9\u5E94
lotFilter.notEquals=\u4F7F\u7528WriteRecordFunction\u63D2\u5165\u4E00\u6761\u5168\u7C7B\u578B\u6570\u636E\u540E\uFF0C\u4F7F\u7528QueryByFilter\u6307\u5B9A%s\u4E2ATapFilter\u8FDB\u884C\u5339\u914D\uFF0C\u5206\u522B\u4E3A\uFF1A\u901A\u8FC7\u4E3B\u952E\u4F5C\u4E3A\u5339\u914D\u53C2\u6570\u751F\u6210\u4E00\u4E2ATapFilter\uFF08%s=%s\uFF09\uFF0C\u5728\u751F\u6210\u4E00\u4E2A\u4E00\u5B9A\u5339\u914D\u4E0D\u4E0A\u7684TapFilter\uFF08%s=%s\uFF09\uFF0C\u8FD9\u6837\u6267\u884C\u4E4B\u540E\uFF0C \u5E94\u8BE5\u8FD4\u56DE%s\u4E2AFilterResult\uFF0C\u4E00\u4E2A\u662F\u6210\u529F\u8FD4\u56DE\u6570\u636E\uFF0C \u4E00\u4E2A\u662F\u5931\u8D25\u8FD4\u56DE\u7A7A\u7ED3\u679C\uFF0C\u5B9E\u9645\u8FD4\u56DE\u7ED3\u679C\u4E0D\u7B26\u5408\u8981\u6C42\u3002
lotFilter.equals.succeed=\u4F7F\u7528WriteRecordFunction\u63D2\u5165\u4E00\u6761\u5168\u7C7B\u578B\u6570\u636E\u540E\uFF0C\u4F7F\u7528QueryByFilter\u6307\u5B9A%s\u4E2ATapFilter\u8FDB\u884C\u5339\u914D\uFF0C\u5206\u522B\u4E3A\uFF1A\u901A\u8FC7\u4E3B\u952E\u4F5C\u4E3A\u5339\u914D\u53C2\u6570\u751F\u6210\u4E00\u4E2ATapFilter\uFF08%s=%s\uFF09\uFF0C\u5728\u751F\u6210\u4E00\u4E2A\u4E00\u5B9A\u5339\u914D\u4E0D\u4E0A\u7684TapFilter\uFF08%s=%s\uFF09\uFF0C\u8FD9\u6837\u6267\u884C\u4E4B\u540E\uFF0C \u5E94\u8BE5\u8FD4\u56DE%s\u4E2AFilterResult\uFF0C\u4E00\u4E2A\u662F\u6210\u529F\u8FD4\u56DE\u6570\u636E\uFF0C \u4E00\u4E2A\u662F\u5931\u8D25\u8FD4\u56DE\u7A7A\u7ED3\u679C\uFF0C\u5B9E\u9645\u8FD4\u56DE\u7ED3\u679C\u7B26\u5408\u8981\u6C42\u3002
lotFilter.notEquals.numError=\u4F7F\u7528WriteRecordFunction\u63D2\u5165\u4E00\u6761\u5168\u7C7B\u578B\u6570\u636E\u540E\uFF0C\u4F7F\u7528QueryByFilter\u6307\u5B9A%s\u4E2ATapFilter\u8FDB\u884C\u5339\u914D\uFF0C\u5206\u522B\u4E3A\uFF1A\u901A\u8FC7\u4E3B\u952E\u4F5C\u4E3A\u5339\u914D\u53C2\u6570\u751F\u6210\u4E00\u4E2ATapFilter\uFF08%s=%s\uFF09\uFF0C\u5728\u751F\u6210\u4E00\u4E2A\u4E00\u5B9A\u5339\u914D\u4E0D\u4E0A\u7684TapFilter\uFF08%s=%s\uFF09\uFF0C\u8FD9\u6837\u6267\u884C\u4E4B\u540E\uFF0C \u5E94\u8BE5\u8FD4\u56DE%s\u4E2AFilterResult\uFF0C\u5B9E\u9645\u8FD4\u56DE%s\u4E2AFilterResult\uFF0C\u4E0D\u7B26\u5408\u9884\u671F\u3002

queryByAdvanced=QueryByAdvancedFilterFunction \u57FA\u4E8E\u5339\u914D\u5B57\u6BB5\u9AD8\u7EA7\u67E5\u8BE2\uFF08\u4F9D\u8D56WriteRecordFunction\uFF09
test.byAdvance.sourceTest=\u7528\u4F8B1\uFF0C\u63D2\u5165\u6570\u636E\u80FD\u6B63\u5E38\u67E5\u8BE2\u5E76\u8FDB\u884C\u503C\u6BD4\u5BF9
test.byAdvance.sourceTest2=\u7528\u4F8B2\uFF0C\u67E5\u8BE2\u6570\u636E\u65F6\uFF0C\u901A\u8FC7\u4F7F\u7528TapAdvanceFilter\u7684\u5404\u79CD\u53C2\u6570\u8FDB\u884C\u67E5\u8BE2
queryByAdvanced.operator.succeed=\u901A\u8FC7TapAdvanceFilter\u7684\u529F\u80FD\u8FDB\u884C\u5339\u914D\uFF0C\u4F7F\u7528\u2018%s\u2019\u64CD\u4F5C\u7B26\u6D4B\u8BD5\uFF0C\u64CD\u4F5C%s %s %s\uFF0C\u5DF2\u67E5\u51FA\u67E5\u51FA\u7ED3\u679C\uFF0C\u7B26\u5408\u9884\u671F\uFF0C\u6D4B\u8BD5\u8868\uFF1A%s\u3002
queryByAdvanced.operator.error=\u901A\u8FC7TapAdvanceFilter\u7684\u529F\u80FD\u8FDB\u884C\u5339\u914D\uFF0C\u4F7F\u7528\u2018%s\u2019\u64CD\u4F5C\u7B26\u6D4B\u8BD5\uFF0C\u64CD\u4F5C%s %s %s\uFF0C\u67E5\u8BE2\u65E0\u7ED3\u679C\uFF0C\u5B9E\u9645\u8BB0\u5F55\u5DF2\u63D2\u5165\uFF0C\u4F46\u672A\u67E5\u51FA\uFF0C\u4E0D\u7B26\u5408\u9884\u671F\uFF0C\u6D4B\u8BD5\u8868\uFF1A%s\u3002
queryByAdvanced.sort.error=\u901A\u8FC7TapAdvanceFilter\u7684\u529F\u80FD\u8FDB\u884C\u5339\u914D\uFF0C\u4F7F\u7528\u6392\u5E8F\u6D4B\u8BD5\uFF0C\u66F4\u5177%s\u5B57\u6BB5\u8FDB\u884C%s\u89C4\u5219\u6392\u5E8F\uFF0C\u6392\u5E8F\u65E0\u7ED3\u679C\uFF0C\u4E0D\u7B26\u5408\u9884\u671F\uFF0C\u6D4B\u8BD5\u8868\uFF1A%s\u3002
queryByAdvanced.sort.succeed=\u901A\u8FC7TapAdvanceFilter\u7684\u529F\u80FD\u8FDB\u884C\u5339\u914D\uFF0C\u4F7F\u7528\u6392\u5E8F\u6D4B\u8BD5\uFF0C\u66F4\u5177%s\u5B57\u6BB5\u8FDB\u884C%s\u89C4\u5219\u6392\u5E8F\uFF0C\u6392\u5E8F\u6709\u7ED3\u679C\uFF0C\u7B26\u5408\u9884\u671F\uFF0C\u6D4B\u8BD5\u8868\uFF1A%s\u3002
queryByAdvanced.projection.succeed=\u901A\u8FC7TapAdvanceFilter\u7684\u529F\u80FD\u8FDB\u884C\u5339\u914D\uFF0C\u4F7F\u7528Projection\u8FDB\u884C\uFF0C\u67E5\u8BE2\u7ED3\u679C\u6309\u7167%s\u5B57\u6BB5\u8F93\u51FA\uFF0C\u8F93\u51FA\u6709\u7ED3\u679C\uFF0C\u7B26\u5408\u9884\u671F\uFF0C\u6D4B\u8BD5\u8868\uFF1A%s\u3002
queryByAdvanced.projection.error=\u901A\u8FC7TapAdvanceFilter\u7684\u529F\u80FD\u8FDB\u884C\u5339\u914D\uFF0C\u4F7F\u7528Projection\u8FDB\u884C\uFF0C\u67E5\u8BE2\u7ED3\u679C\u6309\u7167%s\u5B57\u6BB5\u8F93\u51FA\uFF0C\u8F93\u51FA\u65E0\u7ED3\u679C\uFF0C\u7B26\u5408\u9884\u671F\uFF0C\u6D4B\u8BD5\u8868\uFF1A%s\u3002
byAdvance.query.error=\u4F7F\u7528QueryByAdvanceFilterFunction\uFF0C\u8BFB\u51FA\u6240\u6709\u6570\u636E\uFF0C\u5DF2\u8BFB\u53D6\u5230%s\u6761\uFF0C\u4E0D\u7B26\u5408\u7B26\u5408\u9884\u671F\u3002
byAdvance.query.succeed=\u4F7F\u7528QueryByAdvanceFilterFunction\uFF0C\u8BFB\u51FA\u6240\u6709\u6570\u636E\uFF0C\u5DF2\u8BFB\u53D6\u5230%s\u6761\uFF0C\u7B26\u5408\u9884\u671F\u3002

recordEventExecute.insert.assert.error=\u60A8\u63D2\u5165\u4E86%s\u6761\u8BB0\u5F55\uFF0C\u4F46\u662F\u63D2\u5165\u64CD\u4F5C\u672A\u6B63\u5E38\u6267\u884C\uFF0C\u63D2\u5165\u5931\u8D25\u4E86\u3002
recordEventExecute.insert.assert.succeed=\u60A8\u63D2\u5165\u4E86%s\u6761\u8BB0\u5F55\uFF0C\u5E76\u4E14\u63D2\u5165\u64CD\u4F5C\u662F\u6210\u529F\u7684\u3002
recordEventExecute.update.assert.error=\u60A8\u4FEE\u6539\u4E86%s\u6761\u8BB0\u5F55\uFF0C\u4F46\u662F\u4FEE\u6539\u64CD\u4F5C\u5931\u8D25\u4E86\u3002
recordEventExecute.update.assert.succeed=\u60A8\u4FEE\u6539\u4E86%s\u6761\u8BB0\u5F55\uFF0C\u5E76\u4E14\u4FEE\u6539\u64CD\u4F5C\u662F\u6210\u529F\u7684\u3002
recordEventExecute.delete.assert.error=\u60A8\u5220\u9664\u4E86%s\u6761\u8BB0\u5F55\uFF0C\u4F46\u662F\u5220\u9664\u64CD\u4F5C\u5931\u8D25\u4E86\u3002
recordEventExecute.delete.assert.succeed=\u60A8\u5220\u9664\u4E86%s\u6761\u8BB0\u5F55\uFF0C\u5E76\u4E14\u5220\u9664\u64CD\u4F5C\u662F\u6210\u529F\u7684\u3002
recordEventExecute.drop.table.error=\u5220\u9664\u8868\u64CD\u4F5C\u9519\u8BEF\uFF0C\u5220\u9664\u65F6\u6355\u83B7\u5230\u5F02\u5E38\u3002
recordEventExecute.drop.notCatch.thrower=\u5220\u9664\u8868\u64CD\u4F5C\u8FC7\u7A0B\u672A\u68C0\u51FA\u5F02\u5E38\u3002
recordEventExecute.drop.error.not.support.function=\u8BF7\u5B9E\u73B0\u540D\u4E3ADrop Table function\u7684\u51FD\u6570\u3002
recordEventExecute.drop.table.succeed=\u5220\u9664\u8868\u64CD\u4F5C\u6210\u529F\uFF0C\u8868\u540D\u79F0\u4E3A\uFF1A%s \u3002

writeRecordTest.sourceTest2.verify.firstInsert=\u63D2\u5165\u7B56\u7565\u662Fupdate_on_exists\uFF08\u5B58\u5728\u65F6\u66F4\u65B0\uFF09\uFF0C\u6267\u884C\u9996\u6B21\u63D2\u5165\u64CD\u4F5C\u65F6\uFF0C\u5F85\u63D2\u5165\u7684\u8BB0\u5F55\u6570\u5E94\u8BE5\u662F%s\u4E2A,\u4F46\u662F\u5B9E\u9645\u4E0A\u8FD4\u56DE\u8BB0\u5F55\u6570\u7ED3\u679C\u63D2\u5165%s\u3001\u4FEE\u6539%s\u3001\u5220\u9664%s\uFF0C\u63D2\u5165\u5931\u8D25\u3002
writeRecordTest.sourceTest2.verify.firstInsert.succeed=\u63D2\u5165\u7B56\u7565\u662Fupdate_on_exists\uFF08\u5B58\u5728\u65F6\u66F4\u65B0\uFF09\uFF0C\u6267\u884C\u9996\u6B21\u63D2\u5165\u64CD\u4F5C\u65F6\uFF0C\u65B0\u63D2\u5165\u7684\u8BB0\u5F55\u6570\u5E94\u8BE5\u662F%s\u4E2A,\u5B9E\u9645\u4E0A\u64CD\u4F5C\u8FD4\u56DE\u8BB0\u5F55\u6570\u7ED3\u679C\u4E5F\u4E3A\u63D2\u5165%s\u3001\u4FEE\u6539%s\u3001\u5220\u9664%s\uFF0C\u7B26\u5408\u9884\u671F\uFF0C\u63D2\u5165\u6210\u529F\u3002
wr.test2.insertAfter.notEquals=\u63D2\u5165\u7B56\u7565\u662Fupdate_on_exists\uFF08\u5B58\u5728\u65F6\u66F4\u65B0\uFF09\uFF0C\u518D\u6B21\u4F7F\u7528\u76F8\u540C\u4E3B\u952E\u6570\u636E\u6267\u884C\u63D2\u5165\u64CD\u4F5C\u65F6\uFF0C\u63D2\u5165%s\u6761\u6570\u636E\uFF0C\u9884\u671F\u63D2\u5165\u6570%s\u3001\u66F4\u65B0\u6570%s\u3001\u5220\u9664\u6570%s\u3002\u4F46\u662F\u7ED3\u679C\u663E\u793A\u63D2\u5165\u6570\u548C\u4FEE\u6539\u6570\u4E4B\u548C\u4E0D\u7B49\u4E8E%s\uFF0C\u63D2\u5165%s\u3001\u4FEE\u6539%s\u3001\u5220\u9664%s\uFF0C\u4E0D\u7B26\u5408\u9884\u671F\uFF0C\u63D2\u5165\u5931\u8D25\u3002
wr.test2.insertAfter.warnInsert=\u63D2\u5165\u7B56\u7565\u662Fupdate_on_exists\uFF08\u5B58\u5728\u65F6\u66F4\u65B0\uFF09\uFF0C\u518D\u6B21\u4F7F\u7528\u76F8\u540C\u4E3B\u952E\u6570\u636E\u6267\u884C\u63D2\u5165\u64CD\u4F5C\u65F6\uFF0C\u63D2\u5165%s\u6761\u6570\u636E\uFF0C\u9884\u671F\u63D2\u5165\u6570%s\u3001\u66F4\u65B0\u6570%s\u3001\u5220\u9664\u6570%s\uFF0C\u4F46\u662F\u7ED3\u679C\u663E\u793A\u64CD\u4F5C\u8FD4\u56DE\u63D2\u5165%s\u3001\u4FEE\u6539%s\u3001\u5220\u9664%s\uFF0C\u4E0D\u7B26\u5408\u9884\u671F\uFF0C\u53EF\u89C2\u6D4B\u6027\u6570\u636E\u53EF\u80FD\u4E0D\u51C6\u786E\u3002
wr.test2.insertAfter.warnUpdate=\u63D2\u5165\u7B56\u7565\u662Fupdate_on_exists\uFF08\u5B58\u5728\u65F6\u66F4\u65B0\uFF09\uFF0C\u518D\u6B21\u4F7F\u7528\u76F8\u540C\u4E3B\u952E\u6570\u636E\u6267\u884C\u63D2\u5165\u64CD\u4F5C\u65F6\uFF0C\u63D2\u5165%s\u6761\u6570\u636E\uFF0C\u9884\u671F\u63D2\u5165\u6570%s\u3001\u66F4\u65B0\u6570%s\u3001\u5220\u9664\u6570%s\uFF0C\u4F46\u662F\u7ED3\u679C\u63D2\u5165\u64CD\u4F5C\u8FD4\u56DE\u63D2\u5165%s\u3001\u4FEE\u6539%s\u3001\u5220\u9664%s\uFF0C\u4E0D\u7B26\u5408\u9884\u671F\uFF0C\u7ED3\u679C\u663E\u793A\u4E0D\u6B63\u786E\u3002
wr.test2.insertAfter.errorOther=\u63D2\u5165\u7B56\u7565\u662Fupdate_on_exists\uFF08\u5B58\u5728\u65F6\u66F4\u65B0\uFF09\uFF0C\u518D\u6B21\u4F7F\u7528\u76F8\u540C\u4E3B\u952E\u6570\u636E\u6267\u884C\u63D2\u5165\u64CD\u4F5C\u65F6\uFF0C\u63D2\u5165%s\u6761\u6570\u636E\uFF0C\u9884\u671F\u63D2\u5165\u6570%s\u3001\u66F4\u65B0\u6570%s\u3001\u5220\u9664\u6570%s\uFF0C\u4F46\u662F\u5B9E\u9645\u8FD4\u56DE\u7ED3\u679C\u663E\u793A\u63D2\u5165%s\u3001\u4FEE\u6539%s\u3001\u5220\u9664%s\uFF0C\u4E0D\u7B26\u5408\u9884\u671F\uFF0C\u64CD\u4F5C\u5931\u8D25\u3002
wr.test2.insertAfter.succeed=\u63D2\u5165\u7B56\u7565\u662Fupdate_on_exists\uFF08\u5B58\u5728\u65F6\u66F4\u65B0\uFF09\uFF0C\u518D\u6B21\u4F7F\u7528\u76F8\u540C\u4E3B\u952E\u6570\u636E\u6267\u884C\u63D2\u5165\u64CD\u4F5C\u65F6\uFF0C\u6267\u884C\u64CD\u4F5C\u63D2\u5165%s\u6761\u6570\u636E\uFF0C\u9884\u671F\u63D2\u5165\u6570%s\u3001\u66F4\u65B0\u6570%s\u3001\u5220\u9664\u6570%s\uFF0C\u7ED3\u679C\u663E\u793A\u63D2\u5165%s\u3001\u4FEE\u6539%s\u3001\u5220\u9664%s\uFF0C\u6548\u679C\u7B26\u5408\u9884\u671F\uFF0C\u63D2\u5165\u6210\u529F\u3002

wr.test2.IOE.insertAfter.succeed=\u63D2\u5165\u7B56\u7565\u662Fignore_on_exists\uFF08\u5B58\u5728\u65F6\u5FFD\u7565\uFF09\uFF0C\u518D\u6B21\u4F7F\u7528\u76F8\u540C\u4E3B\u952E\u6570\u636E\u6267\u884C\u63D2\u5165\u64CD\u4F5C\u65F6\uFF0C\u9884\u671F\u63D2\u5165\u64CD\u4F5C\u8FD4\u56DE\u63D2\u5165\u6570%s\u3001\u66F4\u65B0\u6570%s\u3001\u5220\u9664\u6570%s\uFF0C\u5B9E\u9645\u7ED3\u679C\u8FD4\u56DE\u63D2\u5165\u6570%s\u3001\u66F4\u65B0\u6570%s\u3001\u5220\u9664\u6570%s\u3002\u7ED3\u679C\u663E\u793A\u7B26\u5408\u9884\u671F\uFF0C\u63D2\u5165\u64CD\u4F5C\u6210\u529F\u3002
wr.test2.IOE.insertAfter.error=\u63D2\u5165\u7B56\u7565\u662Fignore_on_exists\uFF08\u5B58\u5728\u65F6\u5FFD\u7565\uFF09\uFF0C\u518D\u6B21\u4F7F\u7528\u76F8\u540C\u4E3B\u952E\u6570\u636E\u6267\u884C\u63D2\u5165\u64CD\u4F5C\u65F6\uFF0C\u9884\u671F\u63D2\u5165\u64CD\u4F5C\u8FD4\u56DE\u63D2\u5165\u6570%s\u3001\u66F4\u65B0\u6570%s\u3001\u5220\u9664\u6570%s\uFF0C\u5B9E\u9645\u7ED3\u679C\u8FD4\u56DE\u63D2\u5165\u6570%s\u3001\u66F4\u65B0\u6570%s\u3001\u5220\u9664\u6570%s\uFF0C\u63D2\u5165\u64CD\u4F5C\u4E0D\u7B26\u5408\u9884\u671F\u3002

wr.test3.deleteNotExist.error=\u5220\u9664%s\u6761\u4E0D\u5B58\u5728\u7684\u6570\u636E\uFF0C\u4E14\u8FD4\u56DE\u7ED9\u5F15\u64CE\u7684\u63D2\u5165\u3001\u4FEE\u6539\u548C\u5220\u9664\u90FD\u5E94\u8BE5\u4E3A0\uFF0C\u4F46\u5B9E\u9645\u4E0A\u63D2\u5165%s\uFF0C\u4FEE\u6539%s\uFF0C\u5220\u9664%s\u3002\u7ED3\u679C\u4E0D\u5728\u9884\u671F\u5185\uFF0C\u5220\u9664\u5931\u8D25\u3002
wr.test3.deleteNotExist.succeed=\u5220\u9664%s\u6761\u4E0D\u5B58\u5728\u7684\u6570\u636E\uFF0C\u4E14\u8FD4\u56DE\u7ED9\u5F15\u64CE\u7684\u63D2\u5165\u3001\u4FEE\u6539\u548C\u5220\u9664\u6570\u91CF\u90FD\u4E3A0\uFF0C\u5220\u9664\u6309\u9884\u671F\u6210\u529F\u6267\u884C.
wr.test3.deleteNotExist.catchThrowable=\u5220\u9664%s\u6761\u4E0D\u5B58\u5728\u7684\u6570\u636E\uFF0C\u6B64\u65F6\u4E0D\u5E94\u8BE5\u62A5\u9519\u3002\u4F46\u5B9E\u9645\u4E0A\u62A5\u9519\u4E86\uFF0C\u5220\u9664\u5931\u8D25\u3002
wr.test3.deleteNotExist.notThrowable=\u5220\u9664%s\u6761\u4E0D\u5B58\u5728\u7684\u6570\u636E\uFF0C\u5220\u9664\u8FC7\u7A0B\u4E2D\u672A\u68C0\u51FA\u5F02\u5E38\u3002

wr.test4.insertOnNotExists.error=\u4FEE\u6539\u7B56\u7565\u662Finsert_on_nonexists\uFF08\u4E0D\u5B58\u5728\u65F6\u63D2\u5165\uFF09\uFF0C\u4FEE\u6539%s\u6761\u4E0D\u5B58\u5728\u7684\u6570\u636E\uFF0C\u6B64\u65F6\u9884\u671F\u63D2\u5165%s\u3001\u4FEE\u6539%s\u3001\u5220\u9664%s\uFF0C\u4F46\u5B9E\u9645\u63D2\u5165%s\uFF0C\u4FEE\u6539%s\uFF0C\u5220\u9664%s\uFF0C\u4FEE\u6539\u4E0D\u7B26\u5408\u9884\u671F\u3002
wr.test4.insertOnNotExists.succeed=\u4FEE\u6539\u7B56\u7565\u662Finsert_on_nonexists\uFF08\u4E0D\u5B58\u5728\u65F6\u63D2\u5165\uFF09\uFF0C\u4FEE\u6539%s\u6761\u4E0D\u5B58\u5728\u7684\u6570\u636E\uFF0C\u6B64\u65F6\u9884\u671F\u63D2\u5165%s\u3001\u4FEE\u6539%s\u3001\u5220\u9664%s\uFF0C\u5B9E\u9645\u63D2\u5165%s\uFF0C\u4FEE\u6539%s\uFF0C\u5220\u9664%s\uFF0C\u4FEE\u6539\u7B26\u5408\u9884\u671F\u3002
wr.test4.insertOnNotExists.throwable=\u4FEE\u6539\u7B56\u7565\u662Finsert_on_nonexists\uFF08\u4E0D\u5B58\u5728\u65F6\u63D2\u5165\uFF09\uFF0C\u4FEE\u6539%s\u6761\u4E0D\u5B58\u5728\u7684\u6570\u636E\uFF0C\u6B64\u65F6\u4ECE\u6570\u636E\u6E90\u6355\u83B7\u5230\u4E86\u5F02\u5E38: %s,\u4FEE\u6539\u5931\u8D25\u3002
wr.test4.ignoreOnNotExists.error=\u4FEE\u6539\u7B56\u7565\u662Fignore_on_nonexists\uFF08\u4E0D\u5B58\u5728\u65F6\u5FFD\u7565\uFF09\uFF0C\u4FEE\u6539%s\u6761\u4E0D\u5B58\u5728\u7684\u6570\u636E\uFF0C\u6B64\u65F6\u9884\u671F\u63D2\u5165%s\u3001\u4FEE\u6539%s\u3001\u5220\u9664%s\uFF0C\u4F46\u5B9E\u9645\u4E3A\u63D2\u5165%s\uFF0C\u4FEE\u6539%s\uFF0C\u5220\u9664%s\uFF0C\u4FEE\u6539\u5931\u8D25\u3002
wr.test4.ignoreOnNotExists.succeed=\u4FEE\u6539\u7B56\u7565\u662Fignore_on_nonexists\uFF08\u4E0D\u5B58\u5728\u65F6\u5FFD\u7565\uFF09\uFF0C\u4FEE\u6539%s\u6761\u4E0D\u5B58\u5728\u7684\u6570\u636E\uFF0C\u6B64\u65F6\u9884\u671F\u63D2\u5165%s\u3001\u4FEE\u6539%s\u3001\u5220\u9664%s,\u5B9E\u9645\u63D2\u5165%s\uFF0C\u4FEE\u6539%s\uFF0C\u5220\u9664%s,\u7B26\u5408\u9884\u671F\uFF0C\u4FEE\u6539\u6210\u529F\u3002
wr.test4.ignoreOnNotExists.throwable=\u4FEE\u6539\u7B56\u7565\u662Fignore_on_nonexists\uFF08\u4E0D\u5B58\u5728\u65F6\u5FFD\u7565\uFF09\uFF0C\u4FEE\u6539%s\u6761\u4E0D\u5B58\u5728\u7684\u6570\u636E\uFF0C\u6B64\u65F6\u4ECE\u6570\u636E\u6E90\u6355\u83B7\u5230\u4E86\u5F02\u5E38: %s,\u4FEE\u6539\u5931\u8D25\u3002

#writeRecordWithQueryTest.sourceTest.insert.error.null=\u63D2\u5165\u64CD\u4F5C\u540E\uFF0C\u4F7F\u7528QueryByAdvanceFilter\u7684\u67E5\u8BE2\u7ED3\u679C\u4E0D\u5E94\u4E3A\u7A7A\uFF0C\u8BF7\u68C0\u67E5\u64CD\u4F5C\u662F\u5426\u6709\u6548\u3002
#writeRecordWithQueryTest.sourceTest.insert.error.nullResult=\u63D2\u5165\u64CD\u4F5C\u540E\uFF0C\u4F7F\u7528QueryByAdvanceFilter\u7684\u67E5\u8BE2\u7ED3\u679C\u4E0D\u5E94\u4E3A\u7A7A\uFF0C\u8BF7\u68C0\u67E5\u67E5\u8BE2\u6761\u4EF6\u6216\u63D2\u5165\u662F\u5426\u751F\u6548\u3002
#writeRecordWithQueryTest.sourceTest.insert.error.notEquals=\u63D2\u5165\u8BB0\u5F55\u540E\uFF0C\u4F7F\u7528QueryByAdvanceFilter\u7684\u67E5\u8BE2\u7ED3\u679C\u663E\u793A\uFF0C\u67E5\u8BE2\u5230\u7684\u8BB0\u5F55\u4E0E\u63D2\u5165\u8BA1\u5165\u4E0D\u4E00\u81F4\uFF0C\u63D2\u5165\u8BB0\u5F55\u4E0D\u6210\u529F\uFF0C\u8BF7\u68C0\u67E5\u63D2\u5165\u8BB0\u5F55\u662F\u5426\u751F\u6548\u3002
#writeRecordWithQueryTest.sourceTest.insert.succeed.equals=\u6210\u529F\u63D2\u5165\u8BB0\u5F55\uFF0C\u5E76\u6BD4\u8F83\u63D2\u5165\u7684\u8BB0\u5F55\u540E\u663E\u793A\u63D2\u5165\u524D\u540E\u8BB0\u5F55\u4FDD\u6301\u4E86\u4E00\u81F4\u3002

#writeRecordWithQueryTest.sourceTest.update.error.null=\u4FEE\u6539\u63D2\u5165\u7684\u8BB0\u5F55\uFF0C\u5E76\u5BF9\u6BD4\u63D2\u5165\u524D\u540E\u7684\u7ED3\u679C\u662F\u5426\u4E00\u81F4\u3002\u8FD4\u56DE\u4E3ANULL\u3002
#writeRecordWithQueryTest.sourceTest.update.error.nullResult=\u4FEE\u6539\u63D2\u5165\u7684\u8BB0\u5F55\uFF0C\u5E76\u5BF9\u6BD4\u63D2\u5165\u524D\u540E\u7684\u7ED3\u679C\u662F\u5426\u4E00\u81F4\u3002\u8FD4\u56DE\u7ED3\u679C\u4E3A\u7A7A\u3002
#writeRecordWithQueryTest.sourceTest.update.error.notEquals=\u4FEE\u6539\u63D2\u5165\u7684\u8BB0\u5F55\uFF0C\u5E76\u5BF9\u6BD4\u63D2\u5165\u524D\u540E\u7684\u7ED3\u679C\u662F\u5426\u4E00\u81F4\u3002\u524D\u540E\u7ED3\u679C\u4E0D\u4E00\u81F4\u3002
#writeRecordWithQueryTest.sourceTest.update.succeed.equals=\u4FEE\u6539\u63D2\u5165\u7684\u8BB0\u5F55\uFF0C\u5E76\u5BF9\u6BD4\u63D2\u5165\u524D\u540E\u7684\u7ED3\u679C\u662F\u5426\u4E00\u81F4\u3002\u524D\u540E\u7ED3\u679C\u4FDD\u6301\u4E00\u81F4\u3002

#writeRecordWithQueryTest.sourceTest.delete.error.null=\u5220\u9664\u63D2\u5165\u7684\u8BB0\u5F55\uFF0C\u5E76\u5BF9\u6BD4\u63D2\u5165\u524D\u540E\u7684\u7ED3\u679C\u662F\u5426\u4E00\u81F4\u3002\u8FD4\u56DE\u4E3ANULL\u3002
#writeRecordWithQueryTest.sourceTest.delete.error.notNullResult=\u5220\u9664\u63D2\u5165\u7684\u8BB0\u5F55\uFF0C\u5E76\u5BF9\u6BD4\u63D2\u5165\u524D\u540E\u7684\u7ED3\u679C\u662F\u5426\u4E00\u81F4\u3002\u8FD4\u56DE\u7ED3\u679C\u4E0D\u4E3A\u7A7A,\u5220\u9664\u5931\u8D25\u3002
#writeRecordWithQueryTest.sourceTest.delete.succeed.nullResult=\u5220\u9664\u63D2\u5165\u7684\u8BB0\u5F55\uFF0C\u5E76\u5BF9\u6BD4\u63D2\u5165\u524D\u540E\u7684\u7ED3\u679C\u662F\u5426\u4E00\u81F4\u3002\u8FD4\u56DE\u7ED3\u679C\u7A7A\uFF0C\u5220\u9664\u6210\u529F\u3002

memory_fetcher_function=MemoryFetcherFunction\uFF1A\u6B64\u65B9\u6CD5\u7528\u4E8E\u903B\u8F91\u5185\u5B58\u5BFC\u51FA\u65B9\u6CD5\u3002
memory_fetcher_function_v2=MemoryFetcherFunctionV2\uFF1A\u6B64\u65B9\u6CD5\u7528\u4E8E\u903B\u8F91\u5185\u5B58\u5BFC\u51FA\u65B9\u6CD5\uFF08v2\u7248\uFF09\u3002
connection_check_function=ConnectionCheckFunction\uFF1A\u6B64\u65B9\u6CD5\u7528\u4E8E\u8FDE\u63A5\u68C0\u6D4B\u3002
get_charsets_function=GetCharsetsFunction\uFF1A\u6B64\u65B9\u6CD5\u7528\u4E8E\u83B7\u53D6\u5B57\u7B26\u96C6\u3002
command_callback_function=CommandCallbackFunction\uFF1A\u5728\u8FDE\u63A5\u9875\u9762\u548C\u8282\u70B9\u9875\u9762\u7531\u7528\u6237\u70B9\u51FB\u53D1\u9001Command\u83B7\u53D6\u6570\u636E\u6E90\u76F8\u5173\u6570\u636E\u7684\u65B9\u6CD5\u3002
release_external_function=ReleaseExternalFunction\uFF1A\u4EFB\u52A1\u91CD\u7F6E\u91CA\u653E\u5916\u90E8\u8D44\u6E90\u7684\u65B9\u6CD5\u3002
query_by_filter_function=QueryByFilterFunction\uFF1A\u6B64\u65B9\u6CD5\u7528\u4E8E\u901A\u8FC7\u6784\u5EFAFilter\u67E5\u8BE2\u8BB0\u5F55\u3002
create_table_function=CreateTableFunction\uFF1A\u6B64\u65B9\u6CD5\u7528\u4E8E\u521B\u5EFA\u8868\u3002
create_table_v2_function=CreateTableV2Function\uFF1A\u6B64\u65B9\u6CD5\u5728\u521B\u5EFA\u8868\u540E\u8FD4\u56DE\u521B\u5EFA\u7ED3\u679C\u3002
clear_table_function=ClearTableFunction\uFF1A\u6B64\u65B9\u6CD5\u7528\u4E8E\u6E05\u7A7A\u8868\u6570\u636E\u4F46\u4E0D\u5220\u9664\u8868\u3002
control_function=ControlFunction\uFF1A\u63A7\u5236\u4E8B\u4EF6\u7684\u63A5\u6536\u65B9\u6CD5\u3002
delete_index_function=DeleteIndexFunction\uFF1A\u6B64\u65B9\u6CD5\u7528\u4E8E\u5220\u9664\u7D22\u5F15\u3002
query_indexes_function=QueryIndexesFunction\uFF1A\u6B64\u65B9\u6CD5\u7528\u4E8E\u67E5\u8BE2\u7D22\u5F15\u5217\u8868\u3002
alter_database_time_zone_function=AlterDatabaseTimeZoneFunction\uFF1A\u57FA\u4E8EDDL\u4FEE\u6539\u6570\u636E\u5E93\u65F6\u533A\u65B9\u6CD5\u3002
alter_field_attributes_function=AlterFieldAttributesFunction\uFF1A\u6B64\u65B9\u6CD5\u7528\u4E8E\u4FEE\u6539\u8868\u5B57\u6BB5\u5C5E\u6027\u3002
alter_field_name_function=AlterFieldNameFunction\uFF1A\u6B64\u65B9\u6CD5\u7528\u4E8E\u4FEE\u6539\u8868\u5B57\u6BB5\u540D\u79F0\u3002
alter_table_charset_function=AlterTableCharsetFunction\uFF1A\u6B64\u65B9\u6CD5\u7528\u4E8E\u4FEE\u6539\u8868\u7684\u5B57\u7B26\u96C6\u3002
drop_field_function=DropFieldFunction\uFF1A\u6B64\u65B9\u6CD5\u7528\u4E8E\u5220\u9664\u5B57\u6BB5\u5C5E\u6027\u3002
new_field_function=NewFieldFunction\uFF1A\u6B64\u65B9\u6CD5\u7528\u4E8E\u65B0\u589E\u5B57\u6BB5\u5C5E\u6027\u3002
raw_data_callback_filter_function=RawDataCallbackFilterFunction\uFF1ASaas\u5E73\u53F0WebHook\u56DE\u8C03\u7684\u65B9\u6CD5\uFF0C\u5355\u4E2A\u63A5\u6536\u3002
raw_data_callback_filter_function_v2=RawDataCallbackFilterFunctionV2\uFF1ASaas\u5E73\u53F0WebHook\u56DE\u8C03\u7684\u65B9\u6CD5\uFF0C\u6279\u91CF\u63A5\u6536\u3002
process_record_function=ProcessRecordFunction\uFF1A\u5904\u7406\u5668\u5904\u7406\u6570\u636E\u65B9\u6CD5\u3002
batch_read_function=BatchReadFunction\uFF1A\u6B64\u65B9\u6CD5\u652F\u6301\u5168\u91CF\u8BFB\u53D6\u6570\u636E\u3002
stream_read_function=StreamReadFunction\uFF1A\u6B64\u65B9\u6CD5\u652F\u6301\u589E\u91CF\u8BFB\u53D6\u6570\u636E\u3002
batch_count_function=BatchCountFunction\uFF1A\u6B64\u65B9\u6CD5\u7528\u4E8E\u83B7\u53D6\u6570\u636E\u8BB0\u5F55\u6570\u3002
timestamp_to_stream_offset_function=TimestampToStreamOffsetFunction\uFF1A\u6B64\u65B9\u6CD5\u7528\u4E8E\u901A\u8FC7\u65F6\u95F4\u6233\u83B7\u5F97\u589E\u91CF\u65AD\u70B9\u3002
write_record_function=WriteRecordFunction\uFF1A\u6B64\u65B9\u6CD5\u7528\u4E8E\u6279\u91CF\u5199\u5165\u6570\u636E\u3002
query_by_advance_filter_function=QueryByAdvanceFilterFunction\uFF1A\u6B64\u65B9\u6CD5\u53EF\u6839\u636E\u9884\u5148\u6761\u4EF6\u7B5B\u9009\u67E5\u8BE2\u7ED3\u679C\u3002
drop_table_function=DropTableFunction\uFF1A\u6B64\u65B9\u6CD5\u7528\u4E8E\u5220\u9664\u8868\u3002
create_index_function=CreateIndexFunction\uFF1A\u5B9E\u73B0\u6B64\u65B9\u6CD5\u540E\u6570\u636E\u6E90\u53EF\u4EE5\u5BF9\u5B57\u6BB5\u521B\u5EFA\u7D22\u5F15\u3002
get_table_names_function=GetTableNamesFunction\uFF1A\u6B64\u65B9\u6CD5\u7528\u4E8E\u83B7\u53D6\u8868\u540D\u79F0\u3002
error_handle_function=ErrorHandleFunction\uFF1A\u51FA\u9519\u91CD\u8BD5\u7684\u5904\u7406\u65B9\u6CD5\uFF0C\u5B9E\u73B0\u8FD9\u4E2A\u80FD\u529B\u53EF\u4EE5\u6307\u5B9A\u76F8\u5E94\u7684\u5F02\u5E38\u8FDB\u884C\u91CD\u8BD5\u3002

function.inNeed=\u5BF9\u4E8E\u8FD9\u4E2A\u6D4B\u8BD5\u800C\u8A00\uFF0C%s \u662F\u4E00\u4E2A\u5FC5\u8981\u7684\u65B9\u6CD5,\u8BF7\u5728registerCapabilities\u65B9\u6CD5\u4E2D\u5B9E\u73B0\u8FD9\u4E2A\u65B9\u6CD5\u3002
functions.anyOneNeed=\u5F53\u524D\u6D4B\u8BD5\u9700\u8981\u4F9D\u8D56%s \u4E2D\u7684\u4EFB\u4F55\u4E00\u4E2A\u65B9\u6CD5\uFF0C\u8BF7\u4FDD\u8BC1\u4E0A\u8FF0\u65B9\u6CD5\u4E2D\u81F3\u5C11\u4E00\u4E2A\u5DF2\u5728\u5F53\u524D\u6570\u636E\u6E90\u4E2D\u88AB\u5B9E\u73B0\u3002

#please_support_create_table_function=\u8BF7\u652F\u6301\u521B\u5EFA\u8868\u51FD\u6570\u3002
#null_after_create_table=\u6267\u884C\u5EFA\u8868\u64CD\u4F5C\u5931\u8D25\uFF0C\u8BF7\u68C0\u67E5\u521B\u5EFA\u8868\u51FD\u6570\u3002
#create_table_table_not_exists=\u6267\u884C\u521B\u5EFA\u8868\u8868\u540E\uFF0C\u8BE5\u8868\u5DF2\u5B58\u5728\u3002

notFunctions=\u6570\u636E\u6E90\u52A0\u8F7D\u51FA\u73B0\u5F02\u5E38\uFF0C\u4E3A\u627E\u5230\u4EFB\u4F55\u80FD\u529B\u7684\u5B9E\u73B0\uFF0CConnectorFunctions\u4E3ANULL\u3002


connectionTest.test=\u8FDE\u63A5\u6D4B\u8BD5\uFF0C\u5FC5\u6D4B\u65B9\u6CD5
connectionTest.testConnectionTest=\u7528\u4F8B1\uFF0C\u8FD4\u56DE\u6070\u5F53\u7684\u6D4B\u8BD5\u7ED3\u679C
connectionTest.testConnectionTest.errorVCL=Version\uFF0C Connection\uFF0C Login\u7684TestItem\u9879\u6CA1\u6709\u4E0A\u62A5\u3002
connectionTest.testConnectionTest.succeedVCL=Version\uFF0C Connection\uFF0C Login\u7684TestItem\u9879\u4E0A\u62A5\u6210\u529F\u3002
connectionTest.testConnectionTest.errorBatchRead=\u5DF2\u7ECF\u5B9E\u73B0\u4E86BatchReadFunction\uFF0C\u4F46\u662FRead\u6CA1\u6709\u4E0A\u62A5\u65F6\u3002
connectionTest.testConnectionTest.succeedBatchRead=\u5DF2\u7ECF\u5B9E\u73B0\u4E86BatchReadFunction\uFF0CRead\u4E0A\u62A5\u6210\u529F\u3002
connectionTest.testConnectionTest.errorStreamRead=\u5DF2\u7ECF\u5B9E\u73B0\u4E86StreamReadFunction\uFF0C\u4F46\u662FRead log\u6CA1\u6709\u4E0A\u62A5\u65F6\u3002
connectionTest.testConnectionTest.succeedStreamRead=\u5DF2\u7ECF\u5B9E\u73B0\u4E86StreamReadFunction\uFF0CRead log\u4E0A\u62A5\u6210\u529F\u3002
connectionTest.testConnectionTest.errorWriteRecord=\u5DF2\u7ECF\u5B9E\u73B0\u4E86WriteRecordFunction\uFF0C\u4F46\u662FWrite\u6CA1\u6709\u4E0A\u62A5\u65F6\u3002
connectionTest.testConnectionTest.succeedWriteRecord=\u5DF2\u7ECF\u5B9E\u73B0\u4E86StreamReadFunction\uFF0CWrite\u4E0A\u62A5\u6210\u529F\u3002

tableCount.test=tableCount\u8868\u6570\u91CF\uFF0C\u5FC5\u6D4B\u65B9\u6CD5
tableCount.findTableCount=\u7528\u4F8B1\uFF0C\u67E5\u8BE2\u8868\u6570\u91CF
tableCount.findTableCount.errorFun=TableCountFunction\u80FD\u529B\u6821\u9A8C\uFF0C\u901A\u8FC7\u8C03\u7528tableCount\u65B9\u6CD5\u4E4B\u540E\u8FD4\u56DE\u8868\u6570\u91CF\u5927\u4E8E1\u4E3A\u6B63\u786E,tableCount\u65B9\u6CD5\u8C03\u7528\u5931\u8D25\uFF0C\u67E5\u8BE2\u5931\u8D25\uFF1A%s\u3002
tableCount.findTableCount.error=TableCountFunction\u80FD\u529B\u6821\u9A8C\uFF0C\u901A\u8FC7\u8C03\u7528tableCount\u65B9\u6CD5\u4E4B\u540E\u8FD4\u56DE\u8868\u6570\u91CF\u5927\u4E8E1\u4E3A\u6B63\u786E,\u6B64\u65F6\u8FD4\u56DE\u7684\u8868\u6570\u91CF\u4E3A%s,\u6D4B\u8BD5\u4E0D\u7B26\u5408\u9884\u671F\u3002
tableCount.findTableCount.succeed=TableCountFunction\u80FD\u529B\u6821\u9A8C\uFF0C\u901A\u8FC7\u8C03\u7528tableCount\u65B9\u6CD5\u4E4B\u540E\u8FD4\u56DE\u8868\u6570\u91CF\u5927\u4E8E1\u4E3A\u6B63\u786E,\u6B64\u65F6\u8FD4\u56DE\u7684\u8868\u6570\u91CF\u4E3A%s,\u6D4B\u8BD5\u7B26\u5408\u9884\u671F\u3002
tableCount.findTableCountAfterNewTable=\u7528\u4F8B2\uFF0C\u65B0\u5EFA\u8868\u4E4B\u540E\u67E5\u8BE2\u8868\u6570\u91CF
tableCount.findTableCountAfterNewTable.newTable.createTableV2Function.error=\u6570\u636E\u6E90\u5B9E\u73B0\u4E86createTableV2Function\uFF0C\u4F46\u662F\u8C03\u7528\u6B64\u65B9\u6CD5\u521B\u5EFA\u8868\u5931\u8D25\uFF0C\u8868\u540D\u79F0\uFF1A%s\u3002
tableCount.findTableCountAfterNewTable.newTable.createTableV2Function.succeed=\u6570\u636E\u6E90\u5B9E\u73B0\u4E86createTableV2Function\uFF0C\u6210\u529F\u8C03\u7528\u6B64\u65B9\u6CD5\u521B\u5EFA\u8868\uFF0C\u8868\u540D\u79F0\uFF1A%s\u3002
tableCount.findTableCountAfterNewTable.newTable.createTableFunction.error=\u6570\u636E\u6E90\u5B9E\u73B0\u4E86createTableFunction\uFF0C\u4F46\u662F\u8C03\u7528\u6B64\u65B9\u6CD5\u521B\u5EFA\u8868\u5931\u8D25\uFF0C\u8868\u540D\u79F0\uFF1A%s\u3002
tableCount.findTableCountAfterNewTable.newTable.createTableFunction.succeed=\u6570\u636E\u6E90\u5B9E\u73B0\u4E86createTableFunction\uFF0C\u6210\u529F\u8C03\u7528\u6B64\u65B9\u6CD5\u521B\u5EFA\u8868\uFF0C\u8868\u540D\u79F0\uFF1A%s\u3002
tableCount.findTableCountAfterNewTable.newTable.insertForCreateTable.error=\u6570\u636E\u6E90\u672A\u5B9E\u73B0createTableFunction\uFF0C\u4E5F\u672A\u5B9E\u73B0createTableV2Function\uFF0C\u4F46\u662F\u901A\u8FC7\u63D2\u5165\u6570\u636E\u81EA\u52A8\u5EFA\u8868\uFF0C\u5F53\u524D\u63D2\u5165\u6570\u636E%s\u6761\uFF0C\u4F46\u662F\u63D2\u5165\u5931\u8D25\u5BFC\u81F4\u521B\u5EFA\u8868\u5931\u8D25\uFF0C\u8868\u540D\u79F0\uFF1A%s\u3002
tableCount.findTableCountAfterNewTable.newTable.insertForCreateTable.succeed=\u6570\u636E\u6E90\u672A\u5B9E\u73B0createTableFunction\uFF0C\u4E5F\u672A\u5B9E\u73B0createTableV2Function\uFF0C\u4F46\u662F\u901A\u8FC7\u63D2\u5165\u6570\u636E\u81EA\u52A8\u5EFA\u8868\uFF0C\u5F53\u524D\u63D2\u5165\u6570\u636E%s\u6761\uFF0C\u8C03\u7528\u6B64\u65B9\u6CD5\u521B\u5EFA\u8868\u6210\u529F\uFF0C\u8868\u540D\u79F0\uFF1A%s\u3002
tableCount.findTableCountAfterNewTable.afterNewTable.error=\u521B\u5EFA\u8868\u524D\u83B7\u53D6\u8868\u6570\u91CF\u4E3A%s,\u521B\u5EFA\u8868\u540E\u8868\u6570\u91CF\u4E3A%s\uFF0C\u521B\u5EFA\u524D\u8868\u6570\u76EE\u5E94\u8BE5\u6BD4\u521B\u5EFA\u8868\u540E\u8868\u6570\u76EE\u5C0F1\uFF0C\u7ED3\u679C\u4E0D\u7B26\u5408\u9884\u671F\uFF0C\u8BF7\u68C0\u67E5\u540E\u91CD\u8BD5\u3002
tableCount.findTableCountAfterNewTable.afterNewTable.succeed=\u521B\u5EFA\u8868\u524D\u83B7\u53D6\u8868\u6570\u91CF\u4E3A%s,\u521B\u5EFA\u8868\u540E\u8868\u6570\u91CF\u4E3A%s\uFF0C\u521B\u5EFA\u524D\u8868\u6570\u76EE\u6BD4\u521B\u5EFA\u8868\u540E\u8868\u6570\u76EE\u5C0F1\uFF0C\u7ED3\u679C\u7B26\u5408\u9884\u671F\u3002
tableCount.findTableCountAfterNewTable.newTable.error=\u6570\u636E\u6E90\u4E0D\u652F\u6301\u5EFA\u8868\uFF0C\u65E0\u6CD5\u8FDB\u884C\u4E0B\u4E00\u6B65\u64CD\u4F5C\uFF0C\u6D4B\u8BD5\u7EC8\u6B62\u3002
table.create.succeed=\u5EFA\u8868\u64CD\u4F5C\u540E\u6267\u884CDiscoverSchema\u83B7\u53D6\u8868\u6210\u529F\uFF0C\u5EFA\u8868\u64CD\u4F5C\u901A\u8FC7\u9A8C\u8BC1\uFF0C\u5EFA\u8868\u6210\u529F\uFF0C\u8868\u540D\u79F0\uFF1A%s\u3002
table.create.error=\u5EFA\u8868\u64CD\u4F5C\u540E\u6267\u884CDiscoverSchema\u83B7\u53D6\u8868\u5931\u8D25\uFF0C\u5EFA\u8868\u64CD\u4F5C\u672A\u901A\u8FC7\u9A8C\u8BC1\uFF0C\u65E0\u6CD5\u83B7\u53D6\u5230\u65B0\u5EFA\u7684\u8868\uFF0C\u5EFA\u8868\u5931\u8D25\uFF0C\u8868\u540D\u79F0\uFF1A%s\u3002

discoverSchema.test=discoverSchema\u53D1\u73B0\u8868\uFF0C\u5FC5\u6D4B\u65B9\u6CD5
discoverSchema.discover=\u7528\u4F8B1\uFF0C\u53D1\u73B0\u8868
discoverSchema.discoverAfterCreate=\u7528\u4F8B2\uFF0C\u5EFA\u8868\u4E4B\u540E\u80FD\u53D1\u73B0\u8868\uFF08\u4F9D\u8D56CreateTableFunction\uFF09
discoverSchema.discoverByTableName1=\u7528\u4F8B3\uFF0C\u901A\u8FC7\u6307\u5B9A\u8868\u660E\u52A0\u8F7D\u7279\u5B9A\u8868\uFF08\u4F9D\u8D56\u5DF2\u7ECF\u5B58\u5728\u591A\u8868\uFF09
discoverSchema.discoverByTableName2=\u7528\u4F8B4\uFF0C\u901A\u8FC7\u6307\u5B9A\u8868\u540D\u52A0\u8F7D\u7279\u5B9A\u8868\uFF08\u4F9D\u8D56CreateTableFunction\uFF09
discoverSchema.discoverByTableCount1=\u7528\u4F8B5\uFF0C\u901A\u8FC7\u6307\u5B9A\u8868\u6570\u91CF\u52A0\u8F7D\u56FA\u5B9A\u6570\u91CF\u7684\u8868\uFF08\u4F9D\u8D56\u5DF2\u7ECF\u5B58\u5728\u591A\u8868\uFF09
discoverSchema.discoverByTableCount2=\u7528\u4F8B6\uFF0C\u901A\u8FC7\u6307\u5B9A\u8868\u6570\u91CF\u52A0\u8F7D\u56FA\u5B9A\u6570\u91CF\u7684\u8868\uFF08\u4F9D\u8D56CreateTableFunction\uFF09

discover.notAnyTable=\u6267\u884CdiscoverSchema\u4E4B\u540E\uFF0C\u81F3\u5C11\u8FD4\u56DE\u4E00\u5F20\u8868,\u4F46\u5B9E\u9645\u7ED3\u679C\u5E76\u672A\u8FD4\u56DE\u4EFB\u4F55\u4E00\u5F20\u8868\uFF0C\u6D4B\u8BD5\u4E0D\u901A\u8FC7\u3002
discover.succeed=\u6267\u884CdiscoverSchema\u4E4B\u540E\uFF0C\u81F3\u5C11\u8FD4\u56DE\u4E00\u5F20\u8868,\u5B9E\u9645\u7ED3\u679C\u8FD4\u56DE%s\u5F20\u8868\uFF0C\u6D4B\u8BD5\u901A\u8FC7\u3002
discover.nullTable=\u6267\u884CdiscoverSchema\u4E4B\u540E\uFF0C\u53D1\u73B0\u8FD4\u56DE\u7ED3\u679C\u4E2D\u5B58\u5728\u7A7A\u8868\uFF0C\u6D4B\u8BD5\u4E0D\u901A\u8FC7\u3002
discover.emptyTableName=\u6267\u884CdiscoverSchema\u4E4B\u540E\uFF0C\u53D1\u73B0\u8FD4\u56DE\u7ED3\u679C\u4E2D\u5B58\u5728\u7A7A\u8868\u540D\u7684\u8868\uFF0C\u6D4B\u8BD5\u4E0D\u901A\u8FC7\u3002
discover.emptyTFields=\u6267\u884CdiscoverSchema\u4E4B\u540E\uFF0C\u5B58\u5728\u8868\u540D\u79F0\u4E3A%s\u7684\u8868\u91CC\u6CA1\u6709\u5B57\u6BB5\u63CF\u8FF0\u3002
discover.hasWarnFields=\u6267\u884CdiscoverSchema\u4E4B\u540E\uFF0C\u83B7\u53D6\u5230\u7684\u8868\u91CC\u5B58\u5728\u5B57\u6BB5\uFF0C\u4F46\u662F\u67D0\u4E9B\u5B57\u6BB5\u7684name\u6216\u8005dataType\u4E3A\u7A7A\uFF0C\u5177\u4F53\u5982\u4E0B\uFF1A%s
discover.notWarnFields=\u6267\u884CdiscoverSchema\u4E4B\u540E\uFF0C\u83B7\u53D6\u5230\u7684\u8868\u91CC\u5B58\u5728\u5B57\u6BB5\uFF0C\u6CA1\u6709name\u6216\u8005dataType\u4E3A\u7A7A\u7684\u5B57\u6BB5\uFF0C\u7B26\u5408\u9884\u671F\u7ED3\u679C\u3002
discoverAfterCreate.notFindTargetTable=\u8868\u5217\u8868\u91CC\u4E0D\u5305\u542B\u968F\u673A\u521B\u5EFA\u7684\u8868\uFF0C\u8868\u540D\u79F0\uFF1A%s\uFF0C\u672C\u6B21DiscoverSchema\u8017\u65F6%sms\u3002
discoverAfterCreate.fundTargetTable=\u8868\u5217\u8868\u91CC\u5305\u542B\u968F\u673A\u521B\u5EFA\u7684\u8868\uFF0C\u8868\u540D\u79F0\uFF1A%s\uFF0C\u672C\u6B21DiscoverSchema\u8017\u65F6%sms\u3002
discoverAfterCreate.exitsNullFiledMap=\u65B0\u521B\u5EFA\u7684\u8868\u6CA1\u6709\u4EFB\u4F55\u5B57\u6BB5\u4FE1\u606F\uFF0C\u521B\u5EFA\u8868%s\u5931\u8D25\u3002
discoverAfterCreate.fieldsNotEqualsCount=\u65B0\u521B\u5EFA\u8868\u4E2D\u5B57\u6BB5\u6570\u76EE\u4E0E\u76EE\u6807\u521B\u5EFA\u8868\u5B57\u6BB5\u6570\u76EE\u4E0D\u7B26\u5408\u9884\u671F\u7ED3\u679C\uFF0C\u9884\u671F\u67E5\u51FA\u6765\u7684\u8868\u5B57\u6BB5\u6570\u5E94\u5927\u4E8E\u539F\u8868\u5B57\u6BB5\u6570\uFF0C\u67E5\u51FA\u6765\u7684\u8868\u5B57\u6BB5\u6570\u4E3A%s\uFF0C\u539F\u8868\u5B57\u6BB5\u6570\u76EE\u4E3A%s\u3002
discoverAfterCreate.fieldsEqualsCount=\u67E5\u51FA\u6765\u7684\u8868\u5B57\u6BB5\u6570\u4E3A%s\uFF0C\u76EE\u6807\u8868\u5B57\u6BB5\u6570\u76EE\u4E3A%s\u3002\u5B57\u6BB5\u6570\u76EE\u7B26\u5408\u9884\u671F\u3002
discoverAfterCreate.allFieldNotEquals=\u8868\u540D\u79F0%s\uFF0C\u521B\u5EFA\u540E\u67E5\u51FA\u6765\u7684\u8868\u4E2D\u5B58\u5728\u5B57\u6BB5\u4E0E\u5F85\u521B\u5EFA\u8868\u4E0D\u5339\u914D\uFF0C\u4E0D\u5339\u914D\u7684\u5185\u5BB9\u4E3A\uFF1A\u5F85\u521B\u5EFA\u8868\u4E2D%s \u4E0E \u67E5\u51FA\u6765\u7684\u8868\u4E2D%s \u4E0D\u5339\u914D\u3002
discoverAfterCreate.allFieldEquals=\u8868\u540D\u79F0%s\uFF0C\u5EFA\u8868\u524D\u540E\u5B57\u6BB5\u5339\u914D\u4E00\u81F4\uFF0C\u5EFA\u8868\u6D4B\u8BD5\u6210\u529F\u3002

discoverByTableName1.notAnyTable=\u6267\u884CdiscoverSchema\u4E4B\u540E\uFF0C\u81F3\u5C11\u8FD4\u56DE\u4E00\u5F20\u8868,\u4F46\u662F\u5B9E\u9645\u672A\u8FD4\u56DE\u4EFB\u4F55\u8868\uFF0C\u672C\u6B21DiscoverSchema\u8017\u65F6%sms\u3002
discoverByTableName1.succeed=\u6267\u884CdiscoverSchema\u4E4B\u540E\uFF0C\u81F3\u5C11\u8FD4\u56DE\u4E00\u5F20\u8868,\u8FD4\u56DE\u6570\u76EE%s\uFF0C\u83B7\u53D6\u6210\u529F\uFF0C\u672C\u6B21DiscoverSchema\u8017\u65F6%sms\u3002
discoverByTableName1.notAnyTableAfter=\u901A\u8FC7\u6307\u5B9A\u7B2C\u4E00\u5F20\u8868\u4E4B\u540E\u7684\u4EFB\u610F\u4E00\u5F20\u8868\u540D\uFF0C\u5F53\u524D\u6307\u5B9A\u7B2C%s\u5F20\u8868\uFF0C\u8868\u540D\u79F0\u4E3A%s\uFF0C\u4F46\u672A\u5F97\u5230\u67E5\u8BE2\u7ED3\u679C\uFF0C\u4E0D\u7B26\u5408\u9884\u671F\uFF0C\u672C\u6B21DiscoverSchema\u8017\u65F6%sms\u3002
discoverByTableName1.succeedAfter=\u901A\u8FC7\u6307\u5B9A\u7B2C\u4E00\u5F20\u8868\u4E4B\u540E\u7684\u4EFB\u610F\u4E00\u5F20\u8868\u540D\uFF0C\u5F53\u524D\u6307\u5B9A\u7B2C%s\u5F20\u8868\uFF0C\u8868\u540D\u79F0\u4E3A%s\uFF0C\u67E5\u8BE2\u7ED3\u679C\u6709%s\u5F20\u8868\uFF0C\u67E5\u8BE2\u6570\u76EE\u5728\u9884\u671F\u5185\uFF0C\u672C\u6B21DiscoverSchema\u8017\u65F6%sms\u3002
#discoverByTableName1.notTable=\u901A\u8FC7\u6307\u5B9A\u7B2C\u4E00\u5F20\u8868\u4E4B\u540E\u7684\u4EFB\u610F\u4E00\u5F20\u8868\u540D\uFF0C\u5F53\u524D\u6307\u5B9A\u7B2C%s\u5F20\u8868\uFF0C\u8868\u540D\u79F0\u4E3A%s\uFF0C\u67E5\u8BE2\u7ED3\u679C\u6709%s\u5F20\u8868\uFF0C\u6570\u76EE\u5927\u4E8E1\uFF0C\u67E5\u8BE2\u6570\u76EE\u5728\u9884\u671F\u5185\u3002
#discoverByTableName1.succeedTable=\u901A\u8FC7\u6307\u5B9A\u7B2C\u4E00\u5F20\u8868\u4E4B\u540E\u7684\u4EFB\u610F\u4E00\u5F20\u8868\u540D\uFF0C\u5F53\u524D\u6307\u5B9A\u7B2C%s\u5F20\u8868\uFF0C\u8868\u540D\u79F0\u4E3A%s\uFF0C\u67E5\u8BE2\u7ED3\u679C\u6709%s\u5F20\u8868\uFF0C\u6570\u76EE\u7B49\u4E8E1\uFF0C\u6D4B\u8BD5\u901A\u8FC7\u3002

discoverByTableName2.notAnyTable=\u521B\u5EFA\u7684\u8868%s\u4E0E\u67E5\u8BE2\u540E\u7684\u7ED3\u679C\u5BF9\u6BD4\u53D1\u73B0\u4E0D\u7B26\u5408\u9884\u671F\uFF0C\u67E5\u8BE2\u7ED3\u679C\u663E\u793A\u8868\u6570\u76EE\u4E3A%s\uFF0C\u672C\u6B21DiscoverSchema\u8017\u65F6%sms\u3002
discoverByTableName2.succeed=\u521B\u5EFA\u7684\u8868%s\u4E0E\u67E5\u8BE2\u540E\u7684\u7ED3\u679C\u5BF9\u6BD4\u53D1\u73B0\u7B26\u5408\u9884\u671F\uFF0C\u67E5\u8BE2\u7ED3\u679C\u663E\u793A\u8868\u6570\u76EE\u4E3A%s\uFF0C\u672C\u6B21DiscoverSchema\u8017\u65F6%sms\u3002
discoverByTableName2.notEqualsTable=\u521B\u5EFA\u7684\u8868%s\u4E0E\u67E5\u8BE2\u540E\u7684\u7ED3\u679C\u5BF9\u6BD4\u53D1\u73B0\u4E0D\u7B26\u5408\u9884\u671F\uFF0C\u67E5\u8BE2\u7ED3\u679C\u8868\u540D\u79F0\u4E3A%,\u540D\u79F0\u4E0D\u76F8\u540C\uFF0C\u672C\u6B21DiscoverSchema\u8017\u65F6%sms\u3002
discoverByTableName2.equalsTable=\u521B\u5EFA\u7684\u8868%s\u4E0E\u67E5\u8BE2\u540E\u7684\u7ED3\u679C\u5BF9\u6BD4\u53D1\u73B0\u7B26\u5408\u9884\u671F\uFF0C\u67E5\u8BE2\u7ED3\u679C\u8868\u540D\u79F0\u4E3A%\uFF0C\u540D\u79F0\u4E00\u81F4\uFF0C\u6D4B\u8BD5\u901A\u8FC7\uFF0C\u672C\u6B21DiscoverSchema\u8017\u65F6%sms\u3002

discoverByTableCount1.notAnyTable=\u6267\u884CdiscoverSchema\u4E4B\u540E\uFF0C\u53D1\u73B0\u6709\u5C11\u4E8E1\u5F20\u8868\u7684\u8FD4\u56DE\uFF0C\u4E0D\u7B26\u5408\u9884\u671F\uFF0C\u672C\u6B21DiscoverSchema\u8017\u65F6%sms\u3002
discoverByTableCount1.succeed=\u6267\u884CdiscoverSchema\u4E4B\u540E\uFF0C\u53D1\u73B0\u6709\u5927\u4E8E1\u5F20\u8868\u7684\u8FD4\u56DE\uFF0C\u7B26\u5408\u9884\u671F\uFF0C\u672C\u6B21DiscoverSchema\u8017\u65F6%sms\u3002
discoverByTableCount1.notTable=\u901A\u8FC7int tableSize\u53C2\u6570\u6307\u5B9A\u4E3A%s\uFF0C\u518D\u6B21\u6267\u884CdiscoverSchema\u4E4B\u540E\uFF0C\u8FD4\u56DE\u8868\u6570\u76EE\u5E94\u4E3A%s\uFF0C\u5B9E\u9645\u53D1\u73B0\u8FD4\u56DE\u8868\u6570\u76EE\u4E3A%s\uFF0C\u4E0D\u7B26\u5408\u9884\u671F\uFF0C\u672C\u6B21DiscoverSchema\u8017\u65F6%sms\u3002
discoverByTableCount1.succeedTable=\u901A\u8FC7int tableSize\u53C2\u6570\u6307\u5B9A\u4E3A%s\uFF0C\u518D\u6B21\u6267\u884CdiscoverSchema\u4E4B\u540E\uFF0C\u53D1\u73B0\u8FD4\u56DE\u8868\u6570\u76EE\u4E5F\u4E3A%s\uFF0C\u7B26\u5408\u9884\u671F\uFF0C\u672C\u6B21DiscoverSchema\u8017\u65F6%sms\u3002
discoverByTableCount1.consumer.error=\u6267\u884CdiscoverSchema\u4E4B\u540E\uFF0C\u53D1\u73B0\u6709\u5927\u4E8E1\u5F20\u8868\u7684\u8FD4\u56DE\uFF0C\u603B\u8BA1\u8FD4\u56DE\u8868\u6570%s\uFF0C\u901A\u8FC7int tableSize\u53C2\u6570\u6307\u5B9A\u4E3A%s\uFF0C\u9884\u671F\u6BCF\u6279\u6700\u591A\u63A5\u6536%s\u4E2A\u8868\uFF0C\u901A\u8FC7Consumer<List<TapTable>> consumer\u6BCF\u6B21\u8FD4\u6700\u591A\u56DE\u4E86%s\u5F20\u8868\u4E3A\u6210\u529F\u3002\u5B9E\u9645\u7ED3\u679C\u663E\u793A\u4E0D\u7B26\u5408\u9884\u671F\uFF0C\u7B2C%s\u6279\u8FD4\u56DE\u4E86%s\u5F20\u8868\uFF0C\u672C\u6B21DiscoverSchema\u8017\u65F6%sms\u3002
discoverByTableCount1.consumer.succeed=\u6267\u884CdiscoverSchema\u4E4B\u540E\uFF0C\u53D1\u73B0\u6709\u5927\u4E8E1\u5F20\u8868\u7684\u8FD4\u56DE\uFF0C\u603B\u8BA1\u8FD4\u56DE\u8868\u6570%s\uFF0C\u901A\u8FC7int tableSize\u53C2\u6570\u6307\u5B9A\u4E3A%s\uFF0C\u9884\u671F\u6BCF\u6279\u6700\u591A\u63A5\u6536%s\u4E2A\u8868\uFF0C\u901A\u8FC7Consumer<List<TapTable>> consumer\u6BCF\u6B21\u6700\u591A\u8FD4\u56DE\u4E86%s\u5F20\u8868\u4E3A\u6210\u529F\u3002\u5B9E\u9645\u7ED3\u679C\u663E\u793A\u7B26\u5408\u9884\u671F\uFF0C\u5408\u8BA1\u63A5\u6536%s\u6279\uFF0C\u6BCF\u6279\u5747\u4E3A%s\u5F20\u8868\uFF0C\u672C\u6B21DiscoverSchema\u8017\u65F6%sms\u3002


discoverByTableCount2.consumer.error=\u901A\u8FC7CreateTableFunction\u53E6\u5916\u521B\u5EFA%s\u5F20\u8868\uFF0C\u8868\u540D\u79F0\uFF1A%s\uFF0C\u901A\u8FC7int tableSize\u53C2\u6570\u6307\u5B9A\u4E3A%s\uFF0C\u5219\u901A\u8FC7Consumer<List<TapTable>> consumer\u6BCF\u6279\u8FD4\u56DE\u4E86%s\u5F20\u8868\u4E3A\u6210\u529F\u3002\u5B9E\u9645\u7ED3\u679C\u663E\u793A\u4E0D\u7B26\u5408\u9884\u671F\uFF0C\u867D\u7136\u603B\u8BA1\u8FD4\u56DE%s\u5F20\u8868\uFF0C\u4F46\u662F\u5176\u4E2D\u7B2C%s\u6279\u8FD4\u56DE\u4E86%s\u5F20\u8868\uFF0C\u672C\u6B21DiscoverSchema\u8017\u65F6%sms\u3002
discoverByTableCount2.consumer.succeed=\u901A\u8FC7CreateTableFunction\u53E6\u5916\u521B\u5EFA%s\u5F20\u8868\uFF0C\u8868\u540D\u79F0\uFF1A%s\uFF0C\u901A\u8FC7int tableSize\u53C2\u6570\u6307\u5B9A\u4E3A%s\uFF0C\u5219\u901A\u8FC7Consumer<List<TapTable>> consumer\u6BCF\u6279\u8FD4\u56DE\u4E86%s\u5F20\u8868\u4E3A\u6210\u529F\u3002\u5B9E\u9645\u7ED3\u679C\u663E\u793A\u7B26\u5408\u9884\u671F\uFF0C\u5408\u8BA1\u63A5\u6536%s\u6279\uFF0C\u6BCF\u6279\u5747\u4E3A%s\u5F20\u8868\uFF0C\u672C\u6B21DiscoverSchema\u8017\u65F6%sms\u3002
discoverByTableCount2.error=\u901A\u8FC7CreateTableFunction\u53E6\u5916\u521B\u5EFA\u4E00\u5F20\u8868\uFF08\u8868ID\uFF1A%s\uFF09\u540E\uFF0C\u901A\u8FC7int tableSize\u53C2\u6570\u6307\u5B9A\u4E3A%s\uFF0C\u6267\u884CdiscoverSchema\u4E4B\u540E\uFF0C\u53D1\u73B0\u8FD4\u56DE\u8868\u6570\u76EE\u4E0D\u4E3A%s\uFF0C\u4E0D\u7B26\u5408\u9884\u671F\uFF0C\u672C\u6B21DiscoverSchema\u8017\u65F6%sms\u3002
discoverByTableCount2.succeed=\u901A\u8FC7CreateTableFunction\u53E6\u5916\u521B\u5EFA\u4E00\u5F20\u8868\uFF08\u8868ID\uFF1A%s\uFF09\u540E\uFF0C\u901A\u8FC7int tableSize\u53C2\u6570\u6307\u5B9A\u4E3A%s\uFF0C\u6267\u884CdiscoverSchema\u4E4B\u540E\uFF0C\u53D1\u73B0\u8FD4\u56DE\u8868\u6570\u76EE\u4E3A%s\uFF0C\u7B26\u5408\u9884\u671F\uFF0C\u672C\u6B21DiscoverSchema\u8017\u65F6%sms\u3002
#discoverByTableCount2.canNotStop=\u901A\u8FC7CreateTableFunction\u53E6\u5916\u521B\u5EFA\u4E00\u5F20\u8868\uFF08\u8868ID\uFF1A%s\uFF09\u540E\uFF0C\u901A\u8FC7int tableSize\u53C2\u6570\u6307\u5B9A\u4E3A%s\uFF0C\u6267\u884CdiscoverSchema\u4E4B\u540E\uFF0C\u53D1\u73B0\u8FD4\u56DE\u8868\u6570\u76EE\u4E3A%s,\u4F46\u662FdiscoverSchema\u8FD8\u5728\u7EE7\u7EED\uFF0C\u9519\u8BEF\u7684\u7ED3\u679C\u3002

getTableNames.test=GetTableNamesFunction\u83B7\u5F97\u8868\u540D\u5217\u8868
getTableNames.discover=\u7528\u4F8B1\uFF0C\u53D1\u73B0\u8868
getTableNames.afterCreate=\u7528\u4F8B2\uFF0C\u5EFA\u8868\u4E4B\u540E\u80FD\u53D1\u73B0\u8868\uFF08\u4F9D\u8D56CreateTableFunction\uFF09
getTableNames.byCount=\u7528\u4F8B3\uFF0C\u901A\u8FC7\u6307\u5B9A\u8868\u6570\u91CF\u52A0\u8F7D\u56FA\u5B9A\u6570\u91CF\u7684\u8868\uFF08\u4F9D\u8D56\u5DF2\u7ECF\u5B58\u5728\u591A\u8868\uFF09
getTableNames.discover.notAnyTable=\u6307\u5B9AGetTableNamesFunction\u65B9\u6CD5\u4E4B\u540E\uFF0C\u81F3\u5C11\u8FD4\u56DE\u4E00\u5F20\u8868\uFF0C\u4F46\u5B9E\u9645\u672A\u8FD4\u56DE\u4EFB\u4F55\u8868\uFF0C\u4E0D\u7B26\u5408\u9884\u671F\u3002
getTableNames.discover.succeed=\u6307\u5B9AGetTableNamesFunction\u65B9\u6CD5\u4E4B\u540E\uFF0C\u81F3\u5C11\u8FD4\u56DE\u4E00\u5F20\u8868\uFF0C\u4F46\u5B9E\u9645\u8FD4\u56DE\u8868%s\u5F20\uFF0C\u7B26\u5408\u9884\u671F\u3002

timestamp.test=TimestampToStreamOffsetFunction\u57FA\u4E8E\u65F6\u95F4\u6233\u8FD4\u56DE\u589E\u91CF\u65AD\u70B9
timestamp.backStreamOffset=\u7528\u4F8B1\uFF0C\u901A\u8FC7\u65F6\u95F4\u6233\u80FD\u8FD4\u56DE\u589E\u91CF\u65AD\u70B9
timestamp.backStreamOffsetWithNull.error=\u65B9\u6CD5\u53C2\u6570Long time\u4F20null\u7684\u65F6\u5019\u4E0D\u80FD\u8FD4\u56DE\u5F53\u524D\u65F6\u95F4\u7684\u589E\u91CF\u65AD\u70B9\uFF0C\u8FD4\u56DE\u4E3A\u7A7A\u3002
timestamp.backStreamOffsetWithNull.succeed=\u65B9\u6CD5\u53C2\u6570Long time\u4F20null\u7684\u65F6\u5019\u80FD\u8FD4\u56DE\u5F53\u524D\u65F6\u95F4\u7684\u589E\u91CF\u65AD\u70B9\uFF0C\u8FD4\u56DE\u7684\u589E\u91CF\u65F6\u95F4\u65AD\u70B9\u4E3A\uFF1A%s\uFF0C\u7B26\u5408\u9884\u671F\u3002
timestamp.backStreamOffsetWith.succeed=\u65B9\u6CD5\u53C2\u6570Long time\u4F20\u8DDD\u79BB\u5F53\u524D\u65F6\u95F4%s\u4E2A\u5C0F\u65F6\u524D\u7684\u65F6\u5019\u80FD\u8FD4\u56DE\u90A3\u4E2A\u65F6\u95F4\u7684\u589E\u91CF\u65AD\u70B9\uFF0C\u8FD4\u56DE\u7684\u589E\u91CF\u65F6\u95F4\u65AD\u70B9\u4E3A\uFF1A%s\uFF0C\u7B26\u5408\u9884\u671F\u3002
timestamp.backStreamOffsetWith.error=\u65B9\u6CD5\u53C2\u6570Long time\u4F20\u8DDD\u79BB\u5F53\u524D\u65F6\u95F4%s\u4E2A\u5C0F\u65F6\u524D\u7684\u65F6\u5019\u4E0D\u80FD\u80FD\u8FD4\u56DE\u90A3\u4E2A\u65F6\u95F4\u7684\u589E\u91CF\u65AD\u70B9\uFF0C\u8FD4\u56DE\u4E3A\u7A7A\u3002
timestamp.backStreamOffsetWith.throwable=\u65B9\u6CD5\u53C2\u6570Long time\u4F20\u8DDD\u79BB\u5F53\u524D\u65F6\u95F4%s\u4E2A\u5C0F\u65F6\u524D\u7684\u65F6\u5019\u672A\u80FD\u8FD4\u56DE\u90A3\u4E2A\u65F6\u95F4\u7684\u589E\u91CF\u65AD\u70B9\uFF0C\u6267\u884C\u8FC7\u7A0B\u6355\u83B7\u5230\u4E86\u5F02\u5E38\uFF0C\u8BF7\u68C0\u67E5\u65B9\u6CD5\u5B9E\u73B0\u8FC7\u7A0B\uFF0C\u5F02\u5E38\u7EBF\u7D22\u4E3A\uFF1A%s\u3002


createTableTest.test=CreateTableFunction/CreateTableV2Function\u5EFA\u8868
createTableV2=\u7528\u4F8B1\uFF0CCreateTableFunction\u5DF2\u8FC7\u671F\uFF0C\u5E94\u4F7F\u7528CreateTableV2Function
allTapType=\u7528\u4F8B2\uFF0C\u4F7F\u7528TapType\u5168\u7C7B\u578B11\u4E2A\u7C7B\u578B\u63A8\u6F14\u5EFA\u8868\u6D4B\u8BD5
addIndex=\u7528\u4F8B3\uFF0C\u5EFA\u8868\u65F6\u589E\u52A0\u7D22\u5F15\u4FE1\u606F\u8FDB\u884C\u6D4B\u8BD5
tableIfExist=\u7528\u4F8B4\uFF0C\u5EFA\u8868\u65F6\u8868\u662F\u5426\u5B58\u5728\u7684\u6D4B\u8BD5
createTable.null=\u6570\u636E\u6E90\u672A\u5B9E\u73B0CreateTableFunction\uFF0C\u65E0\u6CD5\u901A\u8FC7CreateTableFunction\u5EFA\u8868\uFF0C\u5F85\u5EFA\u8868\u540D\u79F0\u4E3A\uFF1A%s\u3002
createTable.notNull=\u6570\u636E\u6E90\u5B9E\u73B0\u4E86CreateTableFunction\uFF0C\u53EF\u4EE5\u901A\u8FC7CreateTableFunction\u5EFA\u8868\uFF0C\u5F85\u5EFA\u8868\u540D\u79F0\u4E3A\uFF1A%s\u3002
createTable.v2Null=\u6570\u636E\u6E90\u672A\u5B9E\u73B0CreateTableV2Function\uFF0C\u65E0\u6CD5\u901A\u8FC7CreateTableV2Function\u5EFA\u8868\uFF0C\u5F85\u5EFA\u8868\u540D\u79F0\u4E3A\uFF1A%s\u3002
createTable.v2NotNull=\u6570\u636E\u6E90\u5B9E\u73B0\u4E86CreateTableFunction\uFF0C\u53EF\u4EE5\u901A\u8FC7CreateTableV2Function\u5EFA\u8868\uFF0C\u5F85\u5EFA\u8868\u540D\u79F0\u4E3A\uFF1A%s\u3002
verifyTableIsCreated.error=\u4F7F\u7528DiscoverSchema\u6307\u5B9A\u8868\u540D\u79F0%s\u67E5\u8BE2\u8868\uFF0C\u67E5\u8BE2\u7ED3\u679C%s\uFF0C\u9884\u671F\u5927\u4E8E0\uFF0C\u4E0D\u7B26\u5408\u9884\u671F\u503C\u3002\u901A\u8FC7%s\u65B9\u6CD5\u5EFA\u8868\u5931\u8D25\uFF0C\u9700\u8981\u521B\u5EFA\u8868\u7684\u8868\u540D\u79F0\u4E3A\uFF1A%s\u3002
verifyTableIsCreated.succeed=\u4F7F\u7528DiscoverSchema\u6307\u5B9A\u8868\u540D\u79F0%s\u67E5\u8BE2\u8868\uFF0C\u67E5\u8BE2\u7ED3\u679C%s\uFF0C\u9884\u671F\u5927\u4E8E0\uFF0C\u7B26\u5408\u9884\u671F\u503C\u3002\u901A\u8FC7%s\u65B9\u6CD5\u5EFA\u8868\u6210\u529F\uFF0C\u5DF2\u521B\u5EFA\u8868\u7684\u8868\u540D\u79F0\u4E3A\uFF1A%s\u3002
tableIfExists.error=\u968F\u673A\u751F\u6210\u8868\u540D%s\uFF0C\u8C03\u7528CreateTableV2Function\u65B9\u6CD5\u8FDB\u884C\u5EFA\u8868\uFF0C\u8FD4\u56DE\u7684CreateTableOptions#tableExists\u5E94\u8BE5\u662F\u4E3Afalse\uFF0C\u4F46\u5B9E\u9645\u4E0D\u4E3Afalse\u3002
tableIfExists.succeed=\u968F\u673A\u751F\u6210\u8868\u540D%s\uFF0C\u8C03\u7528CreateTableV2Function\u65B9\u6CD5\u8FDB\u884C\u5EFA\u8868\uFF0C\u8FD4\u56DE\u7684CreateTableOptions#tableExists\u5E94\u8BE5\u662F\u4E3Afalse\uFF0C\u4E14\u4E3Afalse\u3002
tableIfExists.again.error=\u518D\u6B21\u4F7F\u7528\u76F8\u540C\u8868\u540D%s\u8FDB\u884C\u5EFA\u8868\uFF0C\u8FD4\u56DE\u7684CreateTableOptions#tableExists\u5E94\u8BE5\u4E3Atrue\uFF0C\u4F46\u5B9E\u9645\u4E0D\u4E3Atrue\u3002
tableIfExists.again.succeed=\u518D\u6B21\u4F7F\u7528\u76F8\u540C\u8868\u540D%s\u8FDB\u884C\u5EFA\u8868\uFF0C\u8FD4\u56DE\u7684CreateTableOptions#tableExists\u5E94\u8BE5\u4E3Atrue\uFF0C\u4E14\u4E3Atrue\u3002
createIndex.notFieldMap=\u6A21\u578B\u63A8\u6F14\u5931\u8D25\uFF0C\u672A\u751F\u6210\u8868\u540D\u79F0\u4E3A%s\u8868\u7684TapTable\uFF0C\u5B57\u6BB5\u96C6\u5408\u4E3A\u7A7A\u3002
createIndex.notSuchField=\u6A21\u578B\u63A8\u6F14\u5931\u8D25\uFF0C\u672A\u5728\u8868%s\u4E2D\u627E\u5230\u5C5E\u6027%s\uFF0C\u63A8\u6F14\u7ED3\u679C\u4E0D\u7B26\u5408\u9884\u671F\u3002
createIndex.noiImplement.createIndexFun=\u6570\u636E\u6E90\u672A\u5B9E\u73B0CreateIndexFunction\uFF0C\u6D4B\u8BD5\u6D41\u7A0B\u65E0\u6CD5\u7EE7\u7EED\uFF0C\u6D4B\u8BD5\u5DF2\u7EC8\u6B62\u3002
createIndex.succeed=\u7D22\u5F15\u521B\u5EFA\u5DF2\u6267\u884C\u6210\u529F\uFF08%s\uFF09\uFF0C\u76EE\u6807\u8868\uFF1A%s\uFF0C\u4E0B\u4E00\u6B65\u68C0\u67E5\u7D22\u5F15\u662F\u5426\u5339\u914D\u3002
createIndex.error=\u7D22\u5F15\u521B\u5EFA\u6267\u884C\u5931\u8D25\uFF08%s\uFF09\uFF0C\u6267\u884C\u8FC7\u7A0B\u629B\u51FA\u5F02\u5E38\uFF0C\u8BF7\u68C0\u67E5\u6570\u636E\u6E90CreateIndexFunction\u7684\u5B9E\u73B0\u6B65\u9AA4\u91CD\u65B0\u6D4B\u8BD5\uFF0C\u76EE\u6807\u8868\uFF1A%s\u3002
createIndex.discoverSchema.error=discoverSchema\u6267\u884C\u5931\u8D25\uFF0C\u672A\u68C0\u67E5\u5230\u521B\u5EFA\u8868\u7684\u8868\u4FE1\u606F\uFF0C\u5F85\u521B\u5EFA\u8868\u4E3A%s\u3002
createIndex.discoverSchema.tooMany.error=discoverSchema\u6267\u884C\u5931\u8D25\uFF0C\u9700\u8981\u83B7\u53D6%s\u5F20\u8868\uFF0C\u5B9E\u9645\u83B7\u53D6%s\u5F20\u8868\uFF0C\u83B7\u53D6\u7ED3\u679C\u4E0D\u4E00\u81F4\uFF0C\u5F85\u521B\u5EFA\u8868\u4E3A%s\u3002
base.indexCreate.error=\u68C0\u67E5\u7D22\u5F15\u662F\u5426\u5339\u914D\uFF1A\u4F7F\u7528DiscoverSchema\u83B7\u53D6\u8868\u7ED3\u6784\u540E\u5206\u6790\uFF0C\u7D22\u5F15\u521B\u5EFA\u5931\u8D25\uFF0C\u5931\u8D25\u7684\u7D22\u5F15\uFF08%s\uFF09\uFF0C\u5339\u914D\u4E0D\u76F8\u7B49\u7684\u7D22\u5F15\uFF08%s\uFF09\uFF0C\u76EE\u6807\u8868\uFF1A%s\u3002
base.succeed.createIndex=\u68C0\u67E5\u7D22\u5F15\u662F\u5426\u5339\u914D\uFF1A\u4F7F\u7528DiscoverSchema\u83B7\u53D6\u8868\u7ED3\u6784\u540E\u5206\u6790\uFF0C\u7D22\u5F15\u5339\u914D\u4E00\u81F4\uFF0C\u76EE\u6807\u8868%s\u7684\u7D22\u5F15\u521B\u5EFA\u6210\u529F\u3002
base.checkIndex.after.error=\u68C0\u67E5\u7D22\u5F15\u662F\u5426\u5339\u914D\uFF1A\u4F7F\u7528DiscoverSchema\u83B7\u53D6\u8868\u7ED3\u6784\u540E\u5206\u6790\uFF0C\u8868%s\u521B\u5EFA\u540E\u672A\u83B7\u53D6\u5230\u7D22\u5F15\u5217\u8868\uFF0C\u8BF7\u68C0\u67E5discoverSchema\u662F\u5426\u8FD4\u56DE\u4E86\u7D22\u5F15\u5217\u8868\u3002
base.field.contrast.error=\u68C0\u67E5\u5B57\u6BB5\u5C5E\u6027\uFF1A\u4F7F\u7528DiscoverSchema\u83B7\u53D6\u8868\u7ED3\u6784\u540E\u5206\u6790\uFF0C\u6A21\u578B\u63A8\u6F14\u7ED3\u679C\u4E0E\u5EFA\u8868\u540E\u7684\u7ED3\u679C\u4E0D\u4E00\u81F4\uFF0C\u6E90\u8868\u7ECF\u8FC7\u6A21\u578B\u63A8\u6F14\u540E\u7684\u5B57\u6BB5\u5C5E\u6027%s\uFF0C\u4E0E\u901A\u8FC7DiscoverSchema\u83B7\u53D6\u5230\u7684\u76EE\u6807\u8868\u4E2D\u7684\u5B57\u6BB5\u5C5E\u6027%s\u3002\u5B58\u5728\u4E0D\u4E00\u81F4\u95EE\u9898\uFF0C\u5EFA\u8868\u4E0D\u7B26\u5408\u9884\u671F\uFF0C\u6D4B\u8BD5\u672A\u901A\u8FC7\uFF0C\u8868\u540D\u79F0\uFF1A%s\u3002
base.field.contrast.succeed=\u68C0\u67E5\u5B57\u6BB5\u5C5E\u6027\uFF1A\u4F7F\u7528DiscoverSchema\u83B7\u53D6\u8868\u7ED3\u6784\u540E\u5206\u6790\uFF0C\u6A21\u578B\u63A8\u6F14\u7ED3\u679C\u4E0E\u5EFA\u8868\u540E\u7684\u7ED3\u679C\u4E00\u81F4\uFF0C\u6D4B\u8BD5\u901A\u8FC7\uFF0C\u6D4B\u8BD5\u8868%s\u3002
base.target.fieldDataType.null=\u68C0\u67E5\u5B57\u6BB5\u5C5E\u6027\uFF1A\u53D1\u73B0discoverSchema\u540E\u83B7\u53D6\u7684\u7684%s\u8868\u7684\u5B57\u6BB5%s\u7684\u6570\u636E\u7C7B\u578B\u672A\u77E5\uFF0C\u5EFA\u8868\u8FC7\u7A0B\u6709\u8BEF\u6216\u6A21\u578B\u63A8\u6F14\u4E0D\u6B63\u786E\uFF01\u8BF7\u68C0\u67E5\u64CD\u4F5C\u6D41\u7A0B\u3002
base.source.fieldDataType.null=\u68C0\u67E5\u5B57\u6BB5\u5C5E\u6027\uFF1A\u4F7F\u7528DiscoverSchema\u83B7\u53D6\u8868\u7ED3\u6784\u540E\u5206\u6790\uFF0C\u53D1\u73B0\u6A21\u578B\u63A8\u6F14\u540E\u7684%s\u8868\u7684\u5B57\u6BB5%s\u7684\u6570\u636E\u7C7B\u578B\u672A\u77E5\uFF0C\u6A21\u578B\u63A8\u6F14\u5931\u8D25\u6216\u8005\u8868\u5C5E\u6027\u5B9A\u4E49\u5931\u8D25\uFF01\u8BF7\u68C0\u67E5\u64CD\u4F5C\u6D41\u7A0B\u3002
base.targetSource.countNotEquals=\u68C0\u67E5\u5B57\u6BB5\u5C5E\u6027\uFF1A\u4F7F\u7528DiscoverSchema\u83B7\u53D6\u8868\u7ED3\u6784\u540E\u5206\u6790\uFF0C\u6E90\u8868\u5B57\u6BB5%s\u4E2A\uFF0C\u751F\u6210\u7684\u8868\u5B57\u6BB5%s\u4E2A\uFF0C\u6E90\u8868\u5B57\u6BB5\u6570\u5E94\u8BE5\u5C0F\u4E8E\u7B49\u4E8E\u751F\u6210\u7684\u8868\uFF0C\u524D\u540E\u5B57\u6BB5\u6570\u91CF\u4E0D\u7B26\u5408\u9884\u671F\uFF0C\u8868\u540D\u79F0\uFF1A%s\u3002
base.targetFields.empty=\u68C0\u67E5\u5B57\u6BB5\u5C5E\u6027\uFF1AdiscoverSchema\u67E5\u8BE2\u7ED3\u679C\u663E\u793A\uFF0C\u751F\u6210\u7684\u8868\u5B57\u6BB5\u5217\u8868\u4E3A\u7A7A\uFF0C\u4E0D\u7B26\u5408\u9884\u671F\uFF0C\u8868\u540D\u79F0\uFF1A%s\u3002
base.sourceFields.empty=\u68C0\u67E5\u5B57\u6BB5\u5C5E\u6027\uFF1A\u6E90\u8868\u5B57\u6BB5\u5217\u8868\u4E3A\u7A7A\uFF0C\u4E0D\u7B26\u5408\u9884\u671F\uFF0C\u8868\u540D\u79F0\uFF1A%s\u3002
createTable.allTapType.discoverSchema.error=\u91C7\u7528\u968F\u673A\u8868\u540D\u5EFA\u8868\uFF0C\u5EFA\u8868\u6210\u529F\u4E4B\u540E\uFF0C\u83B7\u53D6\u8868\u5931\u8D25\uFF0CdiscoverSchema\u65E0\u6CD5\u83B7\u53D6\u5230\u521B\u5EFA\u7684\u8868%s\u3002
createTable.allTapType.discoverSchema.succeed=\u91C7\u7528\u968F\u673A\u8868\u540D\u5EFA\u8868\uFF0C\u5EFA\u8868\u6210\u529F\u4E4B\u540E\uFF0C\u83B7\u53D6\u8868\u6210\u529F\uFF0CdiscoverSchema\u65E0\u6CD5\u83B7\u53D6\u5230\u521B\u5EFA\u7684\u8868%s\u3002
base.notSupportDropTable=\u6570\u636E\u6E90\u4E0D\u652F\u6301DropTableFunction\uFF0C\u5220\u9664\u8868\u64CD\u4F5C\u672A\u6267\u884C\uFF0C\u8BF7\u624B\u52A8\u5220\u9664\u6D4B\u8BD5\u8868\uFF0C\u8868\u540D\u79F0\uFF1A%s\u3002

batchCountTest=BatchCountFunction\u5168\u91CF\u8BB0\u5F55\u6570\uFF08\u4F9D\u8D56WriteRecordFunction\uFF09
batchCountTest.afterInsert=\u7528\u4F8B1\uFF0C\u63D2\u5165\u6570\u636E\u67E5\u8BE2\u8BB0\u5F55\u6570
batchCountTest.insert.error=\u4F7F\u7528WriteRecordFunction\u5199\u5165%s\u6761\u6570\u636E,\u5B9E\u9645\u63D2\u5165%s\u6761\u6570\u636E\uFF0C\u63D2\u5165\u4E0D\u7B26\u5408\u9884\u671F\u7ED3\u679C\uFF0C\u64CD\u4F5C\u5931\u8D25\u3002
batchCountTest.insert=\u4F7F\u7528WriteRecordFunction\u5199\u5165%s\u6761\u6570\u636E\uFF0C\u5B9E\u9645\u63D2\u5165%s\u6761\u6570\u636E\uFF0C\u63D2\u5165\u7B26\u5408\u9884\u671F\u7ED3\u679C\uFF0C\u63D2\u5165\u6210\u529F\u3002
batchCount.afterInsert.error=\u4F7F\u7528WriteRecordFunction\u5199\u5165%s\u6761\u6570\u636E\u540E\uFF0C\u4F7F\u7528BatchCountFunction\u67E5\u8BE2\u8BB0\u5F55\u6570\u4E3A%s\uFF0C\u8FD4\u56DE\u7ED3\u679C\u4E0D\u4E00\u81F4\u3002
batchCount.afterInsert.succeed=\u4F7F\u7528WriteRecordFunction\u5199\u51652\u6761\u6570\u636E\uFF0C\u4F7F\u7528BatchCountFunction\u67E5\u8BE2\u8BB0\u5F55\u6570\u4E3A%s\uFF0C\u8FD4\u56DE\u7ED3\u679C\u4E00\u81F4\uFF0C\u6D4B\u8BD5\u901A\u8FC7\u3002

dropTable=DropTableFunction\u5220\u9664\u8868\uFF08\u4F9D\u8D56CreateTableFunction\u6216\u8005WriteRecordFunction\uFF09
dropTable.test=\u7528\u4F8B1\uFF0C\u5220\u9664\u8868\u6D4B\u8BD5
dropTable.error=\u8868\u540D\u79F0\u4E3A\uFF1A%s \u7684\u8868\u79FB\u9664\u5931\u8D25\u3002
dropTable.succeed=\u8868\u540D\u79F0\u4E3A\uFF1A%s \u7684\u8868\u5DF2\u79FB\u9664\u6210\u529F\u3002

clearTable=ClearTableFunction\u6E05\u9664\u8868\u6570\u636E
clearTable.test=\u7528\u4F8B1\uFF0C\u5199\u5165\u6570\u636E\u4E4B\u540E\u518D\u6E05\u9664\u8868
clearTable.insert.error=\u4F7F\u7528ClearTableFunction\u6E05\u9664\u8868\u6570\u636E\uFF0C\u5148WriteRecordFunction\u5199\u5165%s\u6761\u6570\u636E\uFF0C\u4F46\u662F\u5B9E\u9645\u4E0A\u5199\u5165\u4E86%s\u6761\u8BB0\u5F55\uFF0C\u5199\u5165\u4E0D\u7B26\u5408\u9884\u671F\u3002
clearTable.insert.succeed=\u4F7F\u7528ClearTableFunction\u6E05\u9664\u8868\u6570\u636E\uFF0C\u5148WriteRecordFunction\u5199\u5165%s\u6761\u6570\u636E\uFF0C\u5199\u5165\u7B26\u5408\u9884\u671F\u3002
clearTable.clean=\u5148WriteRecordFunction\u5199\u5165%s\u6761\u6570\u636E\u540E\uFF0C\u6267\u884C\u4E86\u4E00\u6B21ClearTableFunction\u6E05\u9664\u8868\u6570\u636E\u3002
clearTable.verifyBatchCountFunction.error=\u6570\u636E\u6E90\u5B9E\u73B0\u4E86BatchCountFunction\u65B9\u6CD5\uFF0C\u8C03\u7528BatchCountFunction\u65B9\u6CD5\u67E5\u770B\u8BE5\u8868\u662F\u5426\u4E3A%s,\u7ED3\u679C\u663E\u793A\u4E3A%s\uFF0C\u8868\u660E\u6E05\u9664\u8868\u6570\u636E\u5931\u8D25\uFF1B
clearTable.verifyBatchCountFunction.succeed=\u6570\u636E\u6E90\u5B9E\u73B0\u4E86BatchCountFunction\u65B9\u6CD5\uFF0C\u8C03\u7528BatchCountFunction\u65B9\u6CD5\u67E5\u770B\u8BE5\u8868\u662F\u5426\u4E3A0,\u7ED3\u679C\u663E\u793A\u4E3A%s\uFF0C\u8868\u660E\u6E05\u9664\u8868\u6570\u636E\u6210\u529F\uFF1B
clearTable.verifyQueryByAdvanceFilterFunction.succeed=\u5B9E\u73B0\u4E86QueryByAdvanceFilter\uFF0C\u67E5\u8BE2\u63D2\u5165\u7684%s\u6761\u6570\u636E\uFF0C\u5E94\u8BE5\u67E5\u8BE2\u4E0D\u5230\u7684,\u67E5\u8BE2\u4E0D\u5230\u7684\u7ED3\u679C\uFF0C\u7B26\u5408\u9884\u671F\u3002
clearTable.verifyQueryByAdvanceFilterFunction.error=\u5B9E\u73B0\u4E86QueryByAdvanceFilter\uFF0C\u67E5\u8BE2\u63D2\u5165\u7684%s\u6761\u6570\u636E\uFF0C\u4F46\u5B9E\u9645\u4E0A\u5374\u67E5\u5230\u4E86\uFF0C\u4E0D\u7B26\u5408\u9884\u671F\u3002
clearTable.verifyQueryByFilterFunction.succeed=\u5B9E\u73B0\u4E86QueryByFilter\uFF0C\u67E5\u8BE2\u63D2\u5165\u7684%s\u6761\u6570\u636E\uFF0C\u67E5\u8BE2\u4E0D\u5230\u7684\u7ED3\u679C\uFF0C\u7B26\u5408\u9884\u671F\u3002
clearTable.verifyQueryByFilterFunction.error=\u5B9E\u73B0\u4E86QueryByFilter\uFF0C\u67E5\u8BE2\u63D2\u5165\u7684%s\u6761\u6570\u636E\uFF0C\u4F46\u5B9E\u9645\u4E0A\u5374\u67E5\u5230\u4E86\uFF0C\u4E0D\u7B26\u5408\u9884\u671F\u3002


batchRead=BatchReadFunction\u5168\u91CF\u8BFB\u6570\u636E\uFF08\u4F9D\u8D56WriteRecordFunction\uFF09
batchRead.afterInsert=\u7528\u4F8B1\uFF0C\u5199\u51651\u6761\u6570\u636E\u518D\u8BFB\u51FA1\u6761\u6570\u636E
clearTable.byBatch=\u7528\u4F8B2\uFF0C\u5199\u51653\u6761\u6570\u636E\u9A8C\u8BC1\u5206\u6279\u9650\u5236\u662F\u6709\u6548\u7684
batchRead.insert.error=\u4F7F\u7528WriteRecordFunction\u63D2\u5165%s\u6761\u5168\u7C7B\u578B\uFF08\u8986\u76D6TapType\u768411\u4E2D\u7C7B\u578B\u6570\u636E\uFF09\u6570\u636E\uFF0C\u5B9E\u9645\u63D2\u5165%s\u6761\uFF0C\u4E0D\u7B26\u5408\u9884\u671F\u3002
batchRead.insert.succeed=\u4F7F\u7528WriteRecordFunction\u63D2\u5165%s\u6761\u5168\u7C7B\u578B\uFF08\u8986\u76D6TapType\u768411\u4E2D\u7C7B\u578B\u6570\u636E\uFF09\u6570\u636E\uFF0C\u5B9E\u9645\u63D2\u5165%s\u6761\uFF0C\u7B26\u5408\u9884\u671F\u3002
batchRead.batchRead.succeed=\u4F7F\u7528WriteRecordFunction\u63D2\u5165%s\u6761\u5168\u7C7B\u578B\uFF08\u8986\u76D6TapType\u768411\u4E2D\u7C7B\u578B\u6570\u636E\uFF09\u540E\uFF0C\u6B64\u65F6\u4F7F\u7528BatchReadFunction\u83B7\u53D6\u6570\u636E\uFF08batchSize\u4E3A%s\u8BFB\u51FA\u6240\u6709\u6570\u636E\uFF09\uFF0C\u6570\u636E\u6761\u76EE\u6570\u9700\u8981\u7B49\u4E8E%s\uFF0C\u53EA\u8981\u80FD\u67E5\u51FA\u6765\u6570\u636E\u5C31\u7B97\u662F\u6B63\u786E\u3002\u5DF2\u8BFB\u53D6\u5230%s\u6761\uFF0C\u7B26\u5408\u9884\u671F\u3002
batchRead.batchRead.error=\u4F7F\u7528WriteRecordFunction\u63D2\u5165%s\u6761\u5168\u7C7B\u578B\uFF08\u8986\u76D6TapType\u768411\u4E2D\u7C7B\u578B\u6570\u636E\uFF09\u540E\uFF0C\u6B64\u65F6\u4F7F\u7528BatchReadFunction\u83B7\u53D6\u6570\u636E\uFF08batchSize\u4E3A%s\u8BFB\u51FA\u6240\u6709\u6570\u636E\uFF09\uFF0C\u6570\u636E\u6761\u76EE\u6570\u9700\u8981\u7B49\u4E8E%s\uFF0C\u53EA\u8981\u80FD\u67E5\u51FA\u6765\u6570\u636E\u5C31\u7B97\u662F\u6B63\u786E\u3002\u4F46\u662F\u5DF2\u8BFB\u53D6\u5230%s\u6761\uFF0C\u4E0D\u7B26\u5408\u7B26\u5408\u9884\u671F\u3002
exact.match.filter.error=Error occurred while queryByFilter %s error %s.
exact.match.filter.result.null=Result should not be null\uFF0Cas the record has been inserted\u3002
exact.match.filter.null=The filter %s can not get any result\u3002Please make sure writeRecord method update record correctly and queryByFilter/queryByAdvanceFilter can query it out for verification.
exact.equals.failed=\u4F7F\u7528WriteRecordFunction\u63D2\u5165\u7684%s\u6761\u5168\u7C7B\u578B\u6570\u636E\u5DF2\u901A\u8FC7BatchReadFunction\u65B9\u6CD5\u67E5\u51FA\uFF0C\u4F46\u662F\u672A\u901A\u8FC7\u6570\u503C\u5339\u914D,\u5339\u914D\u7ED3\u679C\uFF08Left\u6307BatchRead\u83B7\u53D6\u524D\u7684\u7ED3\u679C\uFF0CRight\u6307BatchRead\u83B7\u53D6\u540E\u7684\u7ED3\u679C\uFF09\uFF1A\n\t%s
exact.equals.succeed=\u4F7F\u7528WriteRecordFunction\u63D2\u5165\u7684%s\u6761\u5168\u7C7B\u578B\u6570\u636E\u5DF2\u901A\u8FC7BatchReadFunction\u65B9\u6CD5\u67E5\u51FA\uFF0C\u5DF2\u901A\u8FC7\u6570\u503C\u5339\u914D\uFF0C\u6D4B\u8BD5\u901A\u8FC7\u3002
exact.match.failed=\u4F7F\u7528WriteRecordFunction\u63D2\u5165\u7684%s\u6761\u5168\u7C7B\u578B\u6570\u636E\u5DF2\u901A\u8FC7BatchReadFunction\u65B9\u6CD5\u67E5\u51FA\uFF0C\u65E2\u672A\u901A\u8FC7\u7CBE\u786E\u5339\u914D\u53C8\u672A\u901A\u8FC7\u6A21\u7CCA\u5339\u914D\uFF0C\u6D4B\u8BD5\u4E0D\u7B26\u5408\u9884\u671F\u3002\n\u7CBE\u786E\u5339\u914D\u7ED3\u679C\u5982\u4E0B\uFF08Left\u6307BatchRead\u83B7\u53D6\u524D\u7684\u7ED3\u679C\uFF0CRight\u6307BatchRead\u83B7\u53D6\u540E\u7684\u7ED3\u679C\uFF09\uFF1A\n\t%s\u3002
exact.match.succeed=\u4F7F\u7528WriteRecordFunction\u63D2\u5165\u7684%s\u6761\u5168\u7C7B\u578B\u6570\u636E\u5DF2\u901A\u8FC7BatchReadFunction\u65B9\u6CD5\u67E5\u51FA\uFF0C\u672A\u901A\u8FC7\u7CBE\u786E\u5339\u914D\u4F46\u53EF\u4EE5\u901A\u8FC7\u6A21\u7CCA\u5339\u914D\uFF0C\u6D4B\u8BD5\u7B26\u5408\u9884\u671F\u3002\u7CBE\u786E\u5339\u914D\u7ED3\u679C\u5982\u4E0B\uFF08Left\u6307BatchRead\u83B7\u53D6\u524D\u7684\u7ED3\u679C\uFF0CRight\u6307BatchRead\u83B7\u53D6\u540E\u7684\u7ED3\u679C\uFF09\uFF1A\n\t%s\u3002
batchRead.tapInsertRecordEvent.null=\u8BFB\u51FA\u7684TapInsertRecordEvent\u4E0D\u80FD\u4E3A\u7A7A\uFF0C\u4E0D\u7B26\u5408\u9884\u671F\u3002
batchRead.tapInsertRecordEvent.notNull=\u8BFB\u51FA\u7684TapInsertRecordEvent\u4E0D\u4E3A\u7A7A\uFF0C\u7B26\u5408\u9884\u671F\u3002
batchRead.table.null=\u8BFB\u51FA\u7684table\u4E0D\u80FD\u4E3A\u7A7A\uFF0C\u4E0D\u7B26\u5408\u9884\u671F\u3002
batchRead.table.notNull=\u8BFB\u51FA\u7684table\u4E0D\u4E3A\u7A7A\uFF0C\u7B26\u5408\u9884\u671F\u3002
batchRead.time.null=\u8BFB\u51FA\u7684time\u4E0D\u80FD\u4E3A\u7A7A\uFF0C\u4E0D\u7B26\u5408\u9884\u671F\u3002
batchRead.time.notNull=\u8BFB\u51FA\u7684time\u4E0D\u4E3A\u7A7A\uFF0C\u7B26\u5408\u9884\u671F\u3002
batchRead.after.null=\u8BFB\u51FA\u7684after\u4E0D\u80FD\u4E3A\u7A7A\uFF0C\u4E0D\u7B26\u5408\u9884\u671F\u3002
batchRead.after.notNull=\u8BFB\u51FA\u7684after\u4E0D\u4E3A\u7A7A\uFF0C\u7B26\u5408\u9884\u671F\u3002

batchRead.batchCount.error=\u4F7F\u7528WriteRecordFunction\u63D2\u5165%s\u6761\uFF0C\u7136\u540E\u4F7F\u7528BatchReadFunction\uFF08batchSize\u4E3A%s\uFF09\u8BFB\u51FA\u6570\u636E\uFF0C\u8FD4\u56DE\u6570\u636E\u6761\u76EE\u6570\u7B2C%s\u6279\u5E94\u8BE5\u4E3A%s\uFF0C\u4F46\u5B9E\u9645\u4E0A\u4E3A%s\uFF0C\u4E0D\u7B26\u5408\u9884\u671F\u3002
batchRead.batchCount.succeed=\u4F7F\u7528WriteRecordFunction\u63D2\u5165%s\u6761\uFF0C\u7136\u540E\u4F7F\u7528BatchReadFunction\uFF08batchSize\u4E3A%s\uFF09\u8BFB\u51FA\u6570\u636E\uFF0C\u8FD4\u56DE\u6570\u636E\u6761\u76EE\u6570\u7B2C%s\u6279\u9884\u671F\u4E3A%s\uFF0C\u5B9E\u9645\u7ED3\u679C\u4E3A%s\uFF0C\u7B26\u5408\u9884\u671F\u3002
batchRead.final.error=\u4F7F\u7528WriteRecordFunction\u63D2\u5165\u7684%s\u6761\u5168\u7C7B\u578B\u6570\u636E\u5DF2\u901A\u8FC7BatchReadFunction\u65B9\u6CD5\u67E5\u51FA\uFF0C\u4F46\u662F\u63D2\u5165\u7684%s\u6761\u6570\u636E\u672A\u4FDD\u6301\u987A\u5E8F\u6216\u4E3B\u952E\u76F8\u540C\u3002
batchRead.final.succeed=\u4F7F\u7528WriteRecordFunction\u63D2\u5165\u7684%s\u6761\u5168\u7C7B\u578B\u6570\u636E\u5DF2\u901A\u8FC7BatchReadFunction\u65B9\u6CD5\u67E5\u51FA\uFF0C\u63D2\u5165\u7684%s\u6761\u6570\u636E\u4FDD\u6301\u987A\u5E8F\u5E76\u4E14\u4E3B\u952E\u76F8\u540C\u3002


