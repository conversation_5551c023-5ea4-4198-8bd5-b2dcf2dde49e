<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright 2020 Hazelcast Inc.
  ~
  ~ Licensed under the Hazelcast Community License (the "License"); you may not use
  ~ this file except in compliance with the License. You may obtain a copy of the
  ~ License at
  ~
  ~ http://hazelcast.com/hazelcast-community-license
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
  ~ WARRANTIES OF ANY KIND, either express or implied. See the License for the
  ~ specific language governing permissions and limitations under the License.
  -->

<!--
    The default Hazelcast configuration. This is used when no hazelcast.xml is present.
    Please see the schema for how to configure Hazelcast at https://hazelcast.com/schema/config/hazelcast-config-5.2.xsd
    or the documentation at https://hazelcast.org/documentation/
-->
<!--suppress XmlDefaultAttributeValue -->
<hazelcast xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xmlns="http://www.hazelcast.com/schema/config"
           xsi:schemaLocation="http://www.hazelcast.com/schema/config
           http://www.hazelcast.com/schema/config/hazelcast-config-5.2.xsd">

    <cluster-name>hz-xml-configured-cluster</cluster-name>
</hazelcast>
