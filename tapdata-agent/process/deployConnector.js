const {
    Worker, isMainThread, parentPort, workerData,
} = require('worker_threads');
const {deploy} = require("./tapdataManagement");
const fs = require("fs");
const path = require("path");

if (isMainThread) {
    const deployResult = {
        startAt: undefined,
        endAt: undefined,
        running: undefined,
        message: [],
        exitCode: undefined
    }
    let worker;
    const deployConnector = function(conf, args, connect) {

        if (deployResult.running) {
            const msg = `Deploy connector worker is already running, start at ${deployResult.startAt.toLocaleString()}`
            console.log(msg);
            connect.write(msg)
            return
        }

        Object.assign(deployResult, {
            startAt: new Date(),
            endAt: undefined,
            running: true,
            message: [],
            exitCode: undefined
        })

        worker = new Worker(__filename, {
            workerData: {conf, args},
        });
        worker.on('message', (msg) => {
            deployResult.message.push(msg)
        });
        worker.on('messageerror', (err) => {
            deployResult.message.push(`Received messageerror from deploy connector worker: ${err}`)
            console.error('Received messageerror from deploy connector worker:', err);
        });
        worker.on('error', (err) => {
            deployResult.message.push(`Received error from deploy connector worker: ${err}`)
            console.error('Received error from deploy connector worker:', err);
        });
        worker.on('exit', (code) => {
            deployResult.running = false;
            deployResult.exitCode = code;
            deployResult.endAt = new Date();
            deployResult.message.push('Deploy connector worker has stopped.')

            if (code !== '0') {
                console.error(`Deploy connector worker stopped with exit code ${code}`)
            } else {
                console.log(`Deploy connector worker exit ${code}`)
            }
            worker.unref()
            worker = null
        });
        connect.write('Deploy connector worker has running in background. You can execute `tapdata status` command to get the progress.')
    }
    const stopDeploy = (conf, connect) => {
        if (worker) {
            deployResult.message.push('Stop deploy connector worker.')
            worker.postMessage('stop')
            worker.terminate()
            connect.write('Deploy connector worker has stopped.')
        } else {
            connect.write('Deploy connector worker is not started.')
        }
    }
    const deployStatus = (conf, connect) => {
        if (deployResult.running === undefined) {
            return true;
        }
        const end = deployResult.endAt || new Date()
        const times = Number((end.getTime() - deployResult.startAt.getTime())/60000).toFixed(2);
        let message = []
        message.push(`Deploy connector worker is ${deployResult.running ? 'running' : 'stopped'}, total times ${times}m.`);
        message = message.concat(deployResult.message).join('\r\n')
        connect.write(message)
    }

    module.exports = {
        deployResult,
        deployConnector,
        stopDeploy,
        deployStatus
    };
    return;
}


//console.log('worker received workerData: ' + workerData)
async function worker() {
    const { deploy } = require('./tapdataManagement');
    try {
        const start = Date.now();
        await deploy(workerData.conf, {
            write: (msg) => parentPort.postMessage(msg)
        }, workerData.args);
        const msg = `Deploy connector done ${(Date.now() - start)/ 1000}s.`
        parentPort.postMessage(msg)
        console.log(msg)
    } catch (e) {
        console.error('Deploy connector error', e)
        parentPort.postMessage(`Execute deploy connector error ${e.toString()}`);
    } finally {
        parentPort.unref();
    }
}
worker().then(console.log)
