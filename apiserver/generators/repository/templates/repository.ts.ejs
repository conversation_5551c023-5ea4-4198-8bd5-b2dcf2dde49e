import {<%= repositoryTypeClass %>, juggler, Options, DataObject} from '@loopback/repository';
import {<%= modelName %>} from '../models';
import {<%= dataSourceClassName %>} from '../datasources';
import {inject} from '@loopback/core';

export class <%= className %>Repository extends <%= repositoryTypeClass %><
  <%= modelName %>,
  typeof <%= modelName %>.prototype.<%= idProperty %>
> {
  constructor(
    @inject('datasources.<%= dataSourceName %>') dataSource: <%= dataSourceClassName %>,
  ) {
    super(<%= modelName %>, dataSource);
  }
}
