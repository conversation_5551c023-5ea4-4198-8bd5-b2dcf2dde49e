/**
 * @title: FileProperty
 * @description:
 * <AUTHOR>
 * @date 2021/9/6
 */
package com.tapdata.tm.dataflow.dto;

public class FileProperty {

	private String include_filename;

	private String exclude_filename;

	private String file_type;

	private String file_schema;

	private String sheet_start;

	private String sheet_end;

	private String excel_header_type;

	private String excel_header_start;

	private String excel_header_end;

	private String excel_value_start;

	private String excel_value_end;

	private String excel_header_concat_char;

	private String excel_password;

	private String seperate;

	private String data_content_xpath;

	private String json_type;

	private String file_upload_mode;

	private String gridfs_header_type;

	private String gridfs_header_config;

	public String getInclude_filename() {
		return include_filename;
	}

	public String getExclude_filename() {
		return exclude_filename;
	}

	public String getFile_type() {
		return file_type;
	}

	public String getFile_schema() {
		return file_schema;
	}

	public String getSheet_start() {
		return sheet_start;
	}

	public String getSheet_end() {
		return sheet_end;
	}

	public String getExcel_header_type() {
		return excel_header_type;
	}

	public String getExcel_header_start() {
		return excel_header_start;
	}

	public String getExcel_header_end() {
		return excel_header_end;
	}

	public String getExcel_value_start() {
		return excel_value_start;
	}

	public String getExcel_value_end() {
		return excel_value_end;
	}

	public String getExcel_header_concat_char() {
		return excel_header_concat_char;
	}

	public String getExcel_password() {
		return excel_password;
	}

	public String getSeperate() {
		return seperate;
	}

	public String getData_content_xpath() {
		return data_content_xpath;
	}

	public String getJson_type() {
		return json_type;
	}

	public String getFile_upload_mode() {
		return file_upload_mode;
	}

	public String getGridfs_header_type() {
		return gridfs_header_type;
	}

	public String getGridfs_header_config() {
		return gridfs_header_config;
	}

	public void setInclude_filename(String include_filename) {
		this.include_filename = include_filename;
	}

	public void setExclude_filename(String exclude_filename) {
		this.exclude_filename = exclude_filename;
	}

	public void setFile_type(String file_type) {
		this.file_type = file_type;
	}

	public void setFile_schema(String file_schema) {
		this.file_schema = file_schema;
	}

	public void setSheet_start(String sheet_start) {
		this.sheet_start = sheet_start;
	}

	public void setSheet_end(String sheet_end) {
		this.sheet_end = sheet_end;
	}

	public void setExcel_header_type(String excel_header_type) {
		this.excel_header_type = excel_header_type;
	}

	public void setExcel_header_start(String excel_header_start) {
		this.excel_header_start = excel_header_start;
	}

	public void setExcel_header_end(String excel_header_end) {
		this.excel_header_end = excel_header_end;
	}

	public void setExcel_value_start(String excel_value_start) {
		this.excel_value_start = excel_value_start;
	}

	public void setExcel_value_end(String excel_value_end) {
		this.excel_value_end = excel_value_end;
	}

	public void setExcel_header_concat_char(String excel_header_concat_char) {
		this.excel_header_concat_char = excel_header_concat_char;
	}

	public void setExcel_password(String excel_password) {
		this.excel_password = excel_password;
	}

	public void setSeperate(String seperate) {
		this.seperate = seperate;
	}

	public void setData_content_xpath(String data_content_xpath) {
		this.data_content_xpath = data_content_xpath;
	}

	public void setJson_type(String json_type) {
		this.json_type = json_type;
	}

	public void setFile_upload_mode(String file_upload_mode) {
		this.file_upload_mode = file_upload_mode;
	}

	public void setGridfs_header_type(String gridfs_header_type) {
		this.gridfs_header_type = gridfs_header_type;
	}

	public void setGridfs_header_config(String gridfs_header_config) {
		this.gridfs_header_config = gridfs_header_config;
	}
}
