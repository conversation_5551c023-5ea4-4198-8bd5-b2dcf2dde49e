package io.tapdata.connector.db2.cdc;

import com.google.protobuf.ByteString;
import io.tapdata.common.cdc.NormalRedo;
import io.tapdata.common.ddl.DDLFactory;
import io.tapdata.connector.db2.Db2JdbcContext;
import io.tapdata.connector.db2.cdc.parser.ColumnInfo;
import io.tapdata.connector.db2.cdc.parser.DB297Parser;
import io.tapdata.connector.db2.cdc.parser.DefaultDB2LogBytesConverter;
import io.tapdata.connector.db2.cdc.parser.ParseColumnBytesResult;
import io.tapdata.connector.db2.config.Db2Config;
import io.tapdata.entity.event.TapEvent;
import io.tapdata.entity.event.ddl.TapDDLEvent;
import io.tapdata.entity.logger.Log;
import io.tapdata.kit.EmptyKit;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.PrintWriter;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;

public class Db2LogMinerTest {
    @Nested
    class CreateEvent{
        class TestDb2LogMiner extends Db2LogMiner{

            protected TestDb2LogMiner(Db2JdbcContext db2JdbcContext, String connectorId, Log tapLogger) {
                super(db2JdbcContext, connectorId, tapLogger);
            }

            @Override
            public void startMiner() throws Throwable {

            }

            @Override
            protected void ddlFlush() throws Throwable {

            }
        }
        TestDb2LogMiner testDb2LogMiner = new TestDb2LogMiner(mock(Db2JdbcContext.class),"test",mock(Log.class));
        @Test
        void Test(){
            String except = "ALTER TABLE \"C##TAPDATA\".\"TT_DDL\" \n" +
                    "ADD (\"TT\" VARCHAR2(255));";
            NormalRedo normalRedo = new NormalRedo();
            normalRedo.setOperation("DDL");
            normalRedo.setTimestamp(1000L);
            normalRedo.setSqlRedo(except);
            AtomicReference<List<TapEvent>> events = new AtomicReference<>();
            events.set(new ArrayList<>());
            testDb2LogMiner.createEvent(normalRedo,events,1000L);
            TapDDLEvent result = (TapDDLEvent)events.get().get(0);
            Assertions.assertEquals(except,result.getOriginDDL());
        }

        @Test
        void testUnknownEvent(){
            String except = "Unknown DDL";
            NormalRedo normalRedo = new NormalRedo();
            normalRedo.setOperation("DDL");
            normalRedo.setTimestamp(1000L);
            normalRedo.setSqlRedo(except);
            AtomicReference<List<TapEvent>> events = new AtomicReference<>();
            events.set(new ArrayList<>());
            try(MockedStatic<DDLFactory> ddlFactoryMockedStatic = Mockito.mockStatic(DDLFactory.class)) {
                ddlFactoryMockedStatic.when(() -> DDLFactory.ddlToTapDDLEvent(any(), any(), any(), any(), any())).thenThrow(new Throwable("ERROR"));
                testDb2LogMiner.createEvent(normalRedo,events,1000L);
                TapDDLEvent result = (TapDDLEvent)events.get().get(0);
                Assertions.assertEquals(except,result.getOriginDDL());
            }
        }

//        @Test
        void testDb2RawData() throws Exception {
            Db2Config db2Config = new Db2Config();
            db2Config.setHost("*************");
            db2Config.setPort(50000);
            db2Config.setDatabase("SAMPLE");
            db2Config.setUser("db2inst1");
            db2Config.setPassword("Gotapd888");

            DB297Parser parser = new DB297Parser();
            DefaultDB2LogBytesConverter converter = new DefaultDB2LogBytesConverter();
            converter.setNeedReverse(true);
            converter.setCharset(Charset.forName("GBK"));
//            File file = new File("/Users/<USER>/Downloads/testdb2.txt");

            File file = new File("/Users/<USER>/Downloads/log_2024-12-10.log");
            try(
                    Db2JdbcContext db2JdbcContext = new Db2JdbcContext(db2Config);
                    PrintWriter printWriter = new PrintWriter("/Users/<USER>/Downloads/kk.txt");
                    BufferedReader reader = new BufferedReader(new FileReader(file))
                    )
            {
                List<ColumnInfo> columnInfos = new ArrayList<>();
                db2JdbcContext.query("SELECT\n" +
                        "  C.TABNAME,\n" +
                        "  C.COLNAME,\n" +
                        "  C.COLNO,\n" +
                        "  C.TYPENAME,\n" +
                        "  C.LENGTH,\n" +
                        "  C.SCALE,\n" +
                        "  C.NULLS,\n" +
                        "  T.TABLEID,\n" +
                        "  T.TBSPACEID\n" +
                        "FROM SYSCAT.TABLES AS T,\n" +
                        "  SYSCAT.COLUMNS AS C\n" +
                        "WHERE C.TABNAME = 'TBID02' AND T.TABSCHEMA = 'DB2INST1'\n" +
                        "      AND C.TABSCHEMA = T.TABSCHEMA\n" +
                        "      AND C.TABNAME = T.TABNAME\n" +
                        "ORDER BY C.COLNO", resultSet -> {
                    while(resultSet.next()) {
                        columnInfos.add(new ColumnInfo(resultSet));
                    }
                });
                while(true) {
                    String line = reader.readLine();
                    if (line == null) {
                        break;
                    }
                    //[DB2ReadLogC139751126669568] [debug] /home/<USER>/cpp/DB2PlugInDataSource/server/logv10/utilrecov.C:
                    if(line.contains("tableid:280,") || line.contains("tableid:5,")
//                            || line.contains("tableid:146,")
                    ) {
                        String[] split = line.split(",");
                        boolean needPrint = false;
                        Map<String, Object> before = null;
                        Map<String, Object> after = null;
                        for(String s : split) {
                            if(s.contains("after:")) {
                                String[] data = s.split(":");
                                if(data.length < 2 || EmptyKit.isBlank(data[1])) {
                                    continue;
                                }
                                try {
                                    List<ParseColumnBytesResult> results = parser.parse(columnInfos, ByteString.copyFrom(decode(data[1])).toByteArray(), true);
                                    after = convert(results, converter);
                                    if("C404980027015".equals(after.get("ID"))) {
                                        needPrint = true;
                                    }
                                }
                                catch (Exception e) {
                                    printWriter.println(line);
                                }
                            } else if(s.contains("before:")){
                                String[] data = s.split(":");
                                if(data.length < 2 || EmptyKit.isBlank(data[1])) {
                                    continue;
                                }
                                try {
                                    List<ParseColumnBytesResult> results = parser.parse(columnInfos, ByteString.copyFrom(decode(data[1])).toByteArray(), true);
                                    before = convert(results, converter);
                                    if("C404980027015".equals(before.get("ID"))) {
                                        needPrint = true;
                                    }
                                }
                                catch (Exception e) {
                                    printWriter.println("【data】:" + line);
                                }
                            }
                        }
                        if(needPrint) {
                            printWriter.println(line);
                            printWriter.println("【before】:" + before);
                            printWriter.println("【after】:" + after);
                        }
                    }
                }
            }
        }

        private Map<String, Object> convert(List<ParseColumnBytesResult> results, DefaultDB2LogBytesConverter converter) {
            Map<String, Object> data = new HashMap<>();
            results.forEach(r -> {
                ColumnInfo columnInfo = r.getColumnInfo();
                if (columnInfo.getDataType().contains("LOB")) {
                    return;
                }
                try {
                    Object obj = converter.convert(columnInfo, r.getColumnBytes(), 0);
                    data.put(columnInfo.getColumn(), obj);
                } catch (Exception e) {
                    String dataType = columnInfo.getDataType();
                    String base64Value = Base64.getEncoder().encodeToString(r.getColumnBytes());
                    String errorMessage = String.format("Convert '%s'.'%s' of type '%s' value is '%s', error: %s"
                            , columnInfo.getTableName(), columnInfo.getColumn(), dataType, base64Value, e.getMessage()
                    );
                    throw new RuntimeException(errorMessage, e);
                }
            });
            return data;
        }

        //base64 decode
        private byte[] decode(String str) {
            return java.util.Base64.getDecoder().decode(str);
        }

    }
}
