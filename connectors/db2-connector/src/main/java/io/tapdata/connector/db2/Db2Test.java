package io.tapdata.connector.db2;

import io.grpc.ManagedChannel;
import io.grpc.netty.shaded.io.grpc.netty.NegotiationType;
import io.grpc.netty.shaded.io.grpc.netty.NettyChannelBuilder;
import io.tapdata.common.CommonDbTest;
import io.tapdata.connector.db2.config.Db2Config;
import io.tapdata.connector.db2.util.Db2OffsetUtils;
import io.tapdata.data.db2.*;
import io.tapdata.kit.EmptyKit;
import io.tapdata.pdk.apis.entity.ConnectionOptions;
import io.tapdata.pdk.apis.entity.TestItem;
import io.tapdata.pdk.apis.exception.testItem.TapTestReadPrivilegeEx;

import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;

import static io.tapdata.base.ConnectorBase.testItem;

public class Db2Test extends CommonDbTest {
    protected ConnectionOptions connectionOptions;

    public Db2Test(Db2Config db2Config, Consumer<TestItem> consumer, ConnectionOptions connectionOptions) {
        super(db2Config, consumer);
        jdbcContext = new Db2JdbcContext(db2Config);
        this.connectionOptions = connectionOptions;
    }

    @Override
    public Boolean testReadPrivilege() {
        try {
            AtomicInteger tableSelectPrivileges = new AtomicInteger();
            jdbcContext.queryWithNext(String.format(DB2_SELECT_TABLE_NUM, commonDbConfig.getUser().toUpperCase(), commonDbConfig.getSchema()),
                    resultSet -> tableSelectPrivileges.set(resultSet.getInt(1)));
            if (tableSelectPrivileges.get() >= jdbcContext.queryAllTables(null).size()) {
                consumer.accept(testItem(TestItem.ITEM_READ, TestItem.RESULT_SUCCESSFULLY, "All tables can be selected"));
            } else {
                consumer.accept(testItem(TestItem.ITEM_READ, TestItem.RESULT_SUCCESSFULLY_WITH_WARN,
                        "Current user may have no read privilege for some tables, Check it"));
            }
            return true;
        } catch (Throwable e) {
            consumer.accept(new TestItem(TestItem.ITEM_READ, new TapTestReadPrivilegeEx(e), TestItem.RESULT_FAILED));
            return false;
        }
    }

    @Override
    public Boolean testStreamRead() {
        ManagedChannel channel = NettyChannelBuilder.forAddress(((Db2Config) commonDbConfig).getRawLogServerHost(), ((Db2Config) commonDbConfig).getRawLogServerPort())
                .maxInboundMessageSize(Integer.MAX_VALUE)
                .negotiationType(NegotiationType.PLAINTEXT).build();
        try {
            DB2ReadLogServerGrpc.DB2ReadLogServerBlockingStub blockingStub = DB2ReadLogServerGrpc.newBlockingStub(channel);
            PingResponse response = blockingStub.ping(PingRequest.getDefaultInstance());
            if ("ok".equals(response.getMsg())) {
                consumer.accept(testItem(TestItem.ITEM_READ_LOG, TestItem.RESULT_SUCCESSFULLY, "Raw log server is available"));
            } else {
                consumer.accept(testItem(TestItem.ITEM_READ_LOG, TestItem.RESULT_SUCCESSFULLY_WITH_WARN, "Raw log server is not available"));
            }
            cacheLri(channel);
        } catch (Throwable e) {
            consumer.accept(testItem(TestItem.ITEM_READ_LOG, TestItem.RESULT_SUCCESSFULLY_WITH_WARN, "Raw log server is not available"));
        } finally {
            channel.shutdown();
        }
        return true;
    }

    @Override
    protected Boolean testDatasourceInstanceInfo() {
        buildDatasourceInstanceInfo(connectionOptions);
        return true;
    }

    private void cacheLri(ManagedChannel channel) throws Throwable {
        DB2ReadLogServerGrpc.DB2ReadLogServerBlockingStub blockingStub = DB2ReadLogServerGrpc.newBlockingStub(channel);
        TaskHandleRequest taskHandleRequest = TaskHandleRequest.newBuilder().setHeader(MESSAGE_HEADER).setId("cache_lri").build();
        ReaderSource.Builder sourceBuilder = ReaderSource.newBuilder().setDatabaseName(commonDbConfig.getDatabase())
                .setDatabaseHostname(commonDbConfig.getHost())
                .setDatabaseServiceName(String.valueOf(commonDbConfig.getPort()))
                .setDatabaseUsername(commonDbConfig.getUser())
                .setDatabasePassword(commonDbConfig.getPassword());
        boolean isBigEndian = getEndian();
        ReadLogRequest.Builder requestBuilder = ReadLogRequest.newBuilder()
                .setHeader(MESSAGE_HEADER)
                .setBigEndian(isBigEndian)
                .setId("cache_lri")
                .setSource(sourceBuilder.build())
                .setCacheLri(true);
        if (EmptyKit.isBlank(((Db2Config) commonDbConfig).getInitialLri())) {
            jdbcContext.queryWithNext("select LOG_CHAIN_ID, CURRENT_LSN from table(MON_GET_TRANSACTION_LOG(0))", resultSet -> {
                requestBuilder.setScn(resultSet.getString(1) + ",0," + resultSet.getString(2));
            });
        } else {
            requestBuilder.setScn(Db2OffsetUtils.flsn2Lri(((Db2Config) commonDbConfig).getInitialLri(), isBigEndian));
        }
        ReadLogRequest readLogRequest = requestBuilder.setTarget(WriterTarget.newBuilder().setType(WriterType.GRPC).build()).build();
        ControlResponse response = blockingStub.createReadLogTask(readLogRequest);
    }

    private boolean getEndian() throws Throwable {
        AtomicBoolean isBigEndian = new AtomicBoolean(false);
        jdbcContext.query("SELECT HEX(1) FROM SYSIBM.SYSDUMMY1", resultSet -> {
            if (resultSet.next()) {
                isBigEndian.set(!resultSet.getString(1).equals("01000000"));
            }
        });
        return isBigEndian.get();
    }

    private static final MessageHeader MESSAGE_HEADER = MessageHeader.newBuilder().setProtocolVersion(1).build();
    private static final String DB2_SELECT_TABLE_NUM = "SELECT COUNT(*) FROM SYSCAT.TABAUTH WHERE GRANTEE='%s' AND TABSCHEMA='%s'";
}
