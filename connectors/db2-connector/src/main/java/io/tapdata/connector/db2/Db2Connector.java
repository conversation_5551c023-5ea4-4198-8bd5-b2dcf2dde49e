package io.tapdata.connector.db2;

import com.ibm.db2.jcc.DB2Blob;
import com.ibm.db2.jcc.DB2Clob;
import io.tapdata.common.CommonDbConnector;
import io.tapdata.common.CommonSqlMaker;
import io.tapdata.common.SqlExecuteCommandFunction;
import io.tapdata.connector.db2.bean.Db2Column;
import io.tapdata.connector.db2.cdc.Db2CdcRunner;
import io.tapdata.connector.db2.cdc.offset.Db2Offset;
import io.tapdata.connector.db2.cdc.util.BytesUtil;
import io.tapdata.connector.db2.cdc.util.NumberConvertUtil;
import io.tapdata.connector.db2.config.Db2Config;
import io.tapdata.connector.db2.ddl.Db2DDLSqlGenerator;
import io.tapdata.connector.db2.dml.Db2RecordWriterV2;
import io.tapdata.connector.db2.util.Db2OffsetUtils;
import io.tapdata.connector.db2.util.SshUtil;
import io.tapdata.entity.codec.TapCodecsRegistry;
import io.tapdata.entity.event.TapEvent;
import io.tapdata.entity.event.ddl.table.*;
import io.tapdata.entity.event.dml.TapRecordEvent;
import io.tapdata.entity.schema.TapField;
import io.tapdata.entity.schema.TapIndex;
import io.tapdata.entity.schema.TapIndexField;
import io.tapdata.entity.schema.TapTable;
import io.tapdata.entity.schema.value.*;
import io.tapdata.entity.simplify.TapSimplify;
import io.tapdata.entity.simplify.pretty.BiClassHandlers;
import io.tapdata.entity.utils.DataMap;
import io.tapdata.entity.utils.cache.Entry;
import io.tapdata.entity.utils.cache.Iterator;
import io.tapdata.kit.DbKit;
import io.tapdata.kit.EmptyKit;
import io.tapdata.pdk.apis.annotations.TapConnectorClass;
import io.tapdata.pdk.apis.consumer.StreamReadConsumer;
import io.tapdata.pdk.apis.context.TapConnectionContext;
import io.tapdata.pdk.apis.context.TapConnectorContext;
import io.tapdata.pdk.apis.entity.ConnectionOptions;
import io.tapdata.pdk.apis.entity.TestItem;
import io.tapdata.pdk.apis.entity.WriteListResult;
import io.tapdata.pdk.apis.functions.ConnectorFunctions;
import io.tapdata.pdk.apis.functions.connection.TableInfo;
import org.springframework.util.FileSystemUtils;

import java.io.*;
import java.nio.ByteOrder;
import java.sql.*;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatterBuilder;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.BiConsumer;
import java.util.function.BiFunction;
import java.util.function.Consumer;
import java.util.function.Supplier;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@TapConnectorClass("spec_db2.json")
public class Db2Connector extends CommonDbConnector {

    private Db2Config db2Config;
    private Db2JdbcContext db2JdbcContext;
    private Db2CdcRunner cdcRunner;
    private String version;

    private void initConnection(TapConnectionContext connectionContext) {
        isConnectorStarted(connectionContext, connectorContext -> {
            firstConnectorId = (String) connectorContext.getStateMap().get("firstConnectorId");
            if (EmptyKit.isNull(firstConnectorId)) {
                firstConnectorId = connectionContext.getId();
                connectorContext.getStateMap().put("firstConnectorId", firstConnectorId);
            }
        });
        db2Config = (Db2Config) new Db2Config().load(connectionContext.getConnectionConfig());
        db2Config.load(connectionContext.getNodeConfig());
        db2JdbcContext = new Db2JdbcContext(db2Config);
        commonDbConfig = db2Config;
        jdbcContext = db2JdbcContext;
        try {
            version = db2JdbcContext.queryVersion();
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        commonSqlMaker = new CommonSqlMaker();
        ddlSqlGenerator = new Db2DDLSqlGenerator();
        tapLogger = connectionContext.getLog();
        fieldDDLHandlers = new BiClassHandlers<>();
        fieldDDLHandlers.register(TapNewFieldEvent.class, this::newField);
        fieldDDLHandlers.register(TapAlterFieldAttributesEvent.class, this::alterFieldAttr);
        fieldDDLHandlers.register(TapAlterFieldNameEvent.class, this::alterFieldName);
        fieldDDLHandlers.register(TapDropFieldEvent.class, this::dropField);
    }

    @Override
    public void onStart(TapConnectionContext connectionContext) {
        initConnection(connectionContext);
        clearLogCache();
    }

    @Override
    public void onStop(TapConnectionContext connectionContext) throws Throwable {
        if (EmptyKit.isNotNull(cdcRunner)) {
            cdcRunner.closeCdcRunner();
        }
        if (EmptyKit.isNotNull(db2JdbcContext)) {
            db2JdbcContext.close();
        }
        clearLogCache();
    }

    private void onDestroy(TapConnectorContext connectorContext) {
        clearLogCache();
    }

    private void clearLogCache() {
        File cacheDir = new File("cacheTransaction" + File.separator + firstConnectorId);
        if (cacheDir.exists()) {
            FileSystemUtils.deleteRecursively(cacheDir);
        }
    }

    @Override
    public void registerCapabilities(ConnectorFunctions connectorFunctions, TapCodecsRegistry codecRegistry) {
        //test
        connectorFunctions.supportErrorHandleFunction(this::errorHandle);
        connectorFunctions.supportReleaseExternalFunction(this::onDestroy);
        //target
        connectorFunctions.supportWriteRecord(this::writeRecord);
        connectorFunctions.supportCreateTableV2(this::createTableV2);
        connectorFunctions.supportClearTable(this::clearTable);
        connectorFunctions.supportDropTable(this::dropTable);
        //source
        connectorFunctions.supportBatchCount(this::batchCount);
        connectorFunctions.supportBatchRead(this::batchReadWithoutOffset);
        connectorFunctions.supportStreamRead(this::streamRead);
        connectorFunctions.supportTimestampToStreamOffset(this::timestampToStreamOffset);
        //query
        connectorFunctions.supportQueryByFilter(this::queryByFilter);
        connectorFunctions.supportQueryByAdvanceFilter(this::queryByAdvanceFilterWithOffsetV2);
        connectorFunctions.supportCountByPartitionFilterFunction(this::countByAdvanceFilterV2);
        connectorFunctions.supportGetTableNamesFunction(this::getTableNames);
        //ddl
        connectorFunctions.supportNewFieldFunction(this::fieldDDLHandler);
        connectorFunctions.supportAlterFieldNameFunction(this::fieldDDLHandler);
        connectorFunctions.supportAlterFieldAttributesFunction(this::fieldDDLHandler);
        connectorFunctions.supportDropFieldFunction(this::fieldDDLHandler);
        connectorFunctions.supportExecuteCommandFunction((a, b, c) -> SqlExecuteCommandFunction.executeCommand(a, b, () -> db2JdbcContext.getConnection(), this::isAlive, c));
        connectorFunctions.supportRunRawCommandFunction(this::runRawCommand);

        codecRegistry.registerFromTapValue(TapRawValue.class, "CLOB", tapRawValue -> {
            if (tapRawValue != null && tapRawValue.getValue() != null) return tapRawValue.getValue().toString();
            return "null";
        });
        codecRegistry.registerFromTapValue(TapMapValue.class, "CLOB", tapMapValue -> {
            if (tapMapValue != null && tapMapValue.getValue() != null) return toJson(tapMapValue.getValue());
            return "null";
        });
        codecRegistry.registerFromTapValue(TapArrayValue.class, "CLOB", tapValue -> {
            if (tapValue != null && tapValue.getValue() != null) return toJson(tapValue.getValue());
            return "null";
        });
//        codecRegistry.registerToTapValue(c9.class, (value, tapType) -> new TapStringValue(DbKit.clobToString((c9) value)));
//        codecRegistry.registerToTapValue(DB2Blob.class, (value, tapType) -> new TapBinaryValue(DbKit.BlobToBytes((DB2Blob) value)));
        codecRegistry.registerFromTapValue(TapTimeValue.class, tapTimeValue -> tapTimeValue.getValue().toInstant().atZone(db2Config.getZoneId()).toLocalDateTime().format(new DateTimeFormatterBuilder().appendPattern("HH:mm:ss").toFormatter()));
        codecRegistry.registerFromTapValue(TapDateTimeValue.class, tapDateTimeValue -> tapDateTimeValue.getValue().toInstant().atZone(db2Config.getZoneId()).toLocalDateTime().format(new DateTimeFormatterBuilder().appendPattern("yyyy-MM-dd HH:mm:ss.SSSSSS").toFormatter()));
        codecRegistry.registerFromTapValue(TapDateValue.class, tapDateValue -> tapDateValue.getValue().toSqlDate());
        codecRegistry.registerFromTapValue(TapYearValue.class, "CHARACTER(4)", TapValue::getOriginValue);
        connectorFunctions.supportGetTableInfoFunction(this::getTableInfo);
        connectorFunctions.supportTransactionBeginFunction(this::beginTransaction);
        connectorFunctions.supportTransactionCommitFunction(this::commitTransaction);
        connectorFunctions.supportTransactionRollbackFunction(this::rollbackTransaction);

    }

    @Override
    public ConnectionOptions connectionTest(TapConnectionContext connectionContext, Consumer<TestItem> consumer) {
        db2Config = (Db2Config) new Db2Config().load(connectionContext.getConnectionConfig());
        ConnectionOptions connectionOptions = ConnectionOptions.create();
        connectionOptions.connectionString(db2Config.getConnectionString());
        try (
                Db2Test db2Test = new Db2Test(db2Config, consumer, connectionOptions)
        ) {
            db2Test.testOneByOne();
        }
        return connectionOptions;
    }

    private void writeRecord(TapConnectorContext connectorContext, List<TapRecordEvent> tapRecordEvents, TapTable tapTable, Consumer<WriteListResult<TapRecordEvent>> writeListResultConsumer) throws SQLException {
        String insertDmlPolicy = connectorContext.getConnectorCapabilities().getCapabilityAlternative(ConnectionOptions.DML_INSERT_POLICY);
        if (insertDmlPolicy == null) {
            insertDmlPolicy = ConnectionOptions.DML_INSERT_POLICY_UPDATE_ON_EXISTS;
        }
        String updateDmlPolicy = connectorContext.getConnectorCapabilities().getCapabilityAlternative(ConnectionOptions.DML_UPDATE_POLICY);
        if (updateDmlPolicy == null) {
            updateDmlPolicy = ConnectionOptions.DML_UPDATE_POLICY_IGNORE_ON_NON_EXISTS;
        }
        if (isTransaction) {
            String threadName = Thread.currentThread().getName();
            Connection connection;
            if (transactionConnectionMap.containsKey(threadName)) {
                connection = transactionConnectionMap.get(threadName);
            } else {
                connection = db2JdbcContext.getConnection();
                transactionConnectionMap.put(threadName, connection);
            }
            new Db2RecordWriterV2(db2JdbcContext, connection, tapTable)
                    .setInsertPolicy(insertDmlPolicy)
                    .setUpdatePolicy(updateDmlPolicy)
                    .setTapLogger(tapLogger)
                    .write(tapRecordEvents, writeListResultConsumer, this::isAlive);
        } else {
            new Db2RecordWriterV2(db2JdbcContext, tapTable)
                    .setInsertPolicy(insertDmlPolicy)
                    .setUpdatePolicy(updateDmlPolicy)
                    .setTapLogger(tapLogger)
                    .write(tapRecordEvents, writeListResultConsumer, this::isAlive);
        }

    }

    private void batchRead(TapConnectorContext tapConnectorContext, TapTable tapTable, Object offsetState, int eventBatchSize, BiConsumer<List<TapEvent>, Object> eventsOffsetConsumer) throws Throwable {
        Db2Offset db2Offset;
        //beginning
        if (null == offsetState) {
            db2Offset = new Db2Offset(new CommonSqlMaker().getOrderByUniqueKey(tapTable), 0L);
        }
        //with offset
        else {
            db2Offset = (Db2Offset) offsetState;
        }

        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM (");
        sql.append("SELECT ");
        for (String fieldName : tapTable.getNameFieldMap().keySet()) {
            sql.append("\"").append(fieldName).append("\",");
        }
        sql.append("ROW_NUMBER() OVER(").append(db2Offset.getSortString()).append(") as row_no");
        sql.append(" FROM \"").append(db2Config.getSchema()).append("\".\"").append(tapTable.getId()).append("\" a");
        sql.append(") WHERE row_no>").append(db2Offset.getOffsetValue());

        db2JdbcContext.query(sql.toString(), resultSet -> {
            List<TapEvent> tapEvents = list();
            //get all column names
            List<String> columnNames = DbKit.getColumnsFromResultSet(resultSet);
            //db2 need to get BLOB,CLOB value first
            List<String> columnTypeNames = DbKit.getColumnTypesFromResultSet(resultSet);
            while (isAlive() && resultSet.next()) {
                DataMap dataMap = DbKit.getRowFromResultSet(resultSet, columnNames);
                for (int i = 0; i < columnNames.size(); i++) {
                    String columnName = columnNames.get(i);
                    String columnTypeName = columnTypeNames.get(i);
                    if ("CLOB".equals(columnTypeName)) {
                        dataMap.put(columnName, DbKit.clobToString(resultSet.getClob(columnName)));
                    } else if ("BLOB".equals(columnTypeName)) {
                        dataMap.put(columnName, DbKit.blobToBytes(resultSet.getBlob(columnName)));
                    } else if ("XML".equals(columnTypeName)) {
                        dataMap.put(columnName, resultSet.getSQLXML(columnName).getString());
                    }
                }
                dataMap.remove("ROW_NO");
                tapEvents.add(insertRecordEvent(dataMap, tapTable.getId()));
                if (tapEvents.size() == eventBatchSize) {
                    db2Offset.setOffsetValue(db2Offset.getOffsetValue() + eventBatchSize);
                    eventsOffsetConsumer.accept(tapEvents, db2Offset);
                    tapEvents = list();
                }
            }
            //last events those less than eventBatchSize
            if (EmptyKit.isNotEmpty(tapEvents)) {
                db2Offset.setOffsetValue(db2Offset.getOffsetValue() + tapEvents.size());
                eventsOffsetConsumer.accept(tapEvents, db2Offset);
            }
        });

    }

    private void streamRead(TapConnectorContext nodeContext, List<String> tableList, Object offsetState, int recordSize, StreamReadConsumer consumer) throws Throwable {
        if (needReverse() && offsetState instanceof Db2Offset) {
            Db2Offset db2Offset = (Db2Offset) offsetState;
            BiFunction<String, String, String> scnReverseFn = (tag, scn) -> {
                String newScn = scn;
                if (null == newScn) return newScn;

                String[] scnArr = scn.split(",");
                if (3 != scnArr.length) {
                    tapLogger.warn("Scn split length is {}, can not to parse.", scnArr.length);
                    return newScn;
                }
                //刚启动任务时，仅有第三个参数时，不转换
                if (Long.parseLong(scnArr[1]) == 0) {
                    return newScn;
                }
                if (Integer.MAX_VALUE > Long.parseLong(scnArr[0])) {
                    newScn = String.format("%s,%s,%s"
                            , Long.reverseBytes(Long.parseLong(scnArr[0]))
                            , Long.reverseBytes(Long.parseLong(scnArr[1]))
                            , Long.reverseBytes(Long.parseLong(scnArr[2]))
                    );
                    tapLogger.info("Reverse {} from '{}' to '{}'", tag, scn, newScn);
                }
                return newScn;
            };
            db2Offset.setLastScn(scnReverseFn.apply("last scn", db2Offset.getLastScn()));
            db2Offset.setPendingScn(scnReverseFn.apply("pending scn", db2Offset.getPendingScn()));
        }
        cdcRunner = new Db2CdcRunner(db2JdbcContext, firstConnectorId, tapLogger).init(
                tableList,
                nodeContext.getTableMap(),
                offsetState,
                recordSize,
                consumer
        );
        cdcRunner.startCdcRunner();
    }

    private Object timestampToStreamOffset(TapConnectorContext connectorContext, Long offsetStartTime) throws Throwable {
        Db2Offset db2Offset = new Db2Offset();
        //open cdc
        List<String> tableList = list();
        List<String> openTableList = list();
        Iterator<Entry<TapTable>> iterator = connectorContext.getTableMap().iterator();
        while (iterator.hasNext()) {
            Entry<TapTable> entry = iterator.next();
            tableList.add(entry.getKey());
        }
        db2JdbcContext.query(String.format("select TABNAME from SYSCAT.TABLES where TABSCHEMA='%s' and TABNAME in ('%s') and DATACAPTURE in ('Y','L')", db2Config.getSchema(), String.join("','", tableList)), resultSet -> {
            while (resultSet.next()) {
                openTableList.add(resultSet.getString(1));
            }
        });
        List<String> notOpenTableList = tableList.stream().filter(v -> !openTableList.contains(v)).collect(Collectors.toList());
        if (EmptyKit.isNotEmpty(notOpenTableList)) {
            tapLogger.warn("Table {} not open CDC, auto open them", notOpenTableList);
            db2JdbcContext.batchExecute(notOpenTableList.stream().map(v -> String.format("ALTER TABLE \"%s\".\"%s\" DATA CAPTURE CHANGES", db2Config.getSchema(), v)).collect(Collectors.toList()));
        }
        if (EmptyKit.isNotNull(offsetStartTime)) {
            if (db2Config.getArchived()) {
                Instant startInstant = Instant.ofEpochMilli(offsetStartTime);
                tapLogger.info("Start CDC by date: {}", startInstant);
                String lri = getLriWithDate(startInstant, () -> !isAlive());
                if (null == lri) {
                    tapLogger.warn("Not found LRI in logs by date.");
                    db2Offset.setTimestamp(offsetStartTime);
                } else {
                    db2Offset.setLastScn(lri);
                }
            } else {
                db2Offset.setTimestamp(offsetStartTime);
            }
        } else {
            if (db2Config.getAutoLri()) {
                db2Offset.setTimestamp(System.currentTimeMillis());
//                db2JdbcContext.queryWithNext("select LOG_CHAIN_ID, CURRENT_LSN from table(MON_GET_TRANSACTION_LOG(0))", resultSet -> {
//                    db2Offset.setLastScn(resultSet.getString(1) + ",0," + resultSet.getString(2));
//                });
            } else {
                db2Offset.setLastScn(getCurrentScnStr());
            }
        }
        return db2Offset;
    }

    protected TapField makeTapField(DataMap dataMap) {
        return new Db2Column(dataMap).getTapField();
    }

    protected void makePrimaryKeyAndIndex(List<DataMap> indexList, String table, List<String> primaryKey, List<TapIndex> tapIndexList) {
        indexList.stream().filter(idx -> table.equals(idx.getString("tableName"))).forEach(idx -> {
            TapIndex index = new TapIndex();
            index.setName(idx.getString("indexName"));
            List<TapIndexField> fieldList = TapSimplify.list();
            index.setPrimary("1".equals(idx.getString("isPk")));
            index.setUnique("1".equals(idx.getString("isUnique")));
            Arrays.stream(idx.getString("columnName").substring(1).split("\\+")).forEach(v -> {
                TapIndexField field = new TapIndexField();
                field.setFieldAsc(true);
                field.setName(v);
                fieldList.add(field);
                if (index.isPrimary()) {
                    primaryKey.add(v);
                }
            });
            index.setIndexFields(fieldList);
            tapIndexList.add(index);
        });
    }

    protected void fieldDDLHandler(TapConnectorContext tapConnectorContext, TapFieldBaseEvent tapFieldBaseEvent) throws SQLException {
        List<String> sqlList = fieldDDLHandlers.handle(tapFieldBaseEvent, tapConnectorContext);
        if (null == sqlList) {
            return;
        }
        db2JdbcContext.batchExecute(sqlList);
        db2JdbcContext.flushTable(tapFieldBaseEvent.getTableId());
    }

    private String getCurrentScnStr() throws Throwable {
        AtomicBoolean needParse = new AtomicBoolean(true);
        AtomicReference<String> res = new AtomicReference<>();
        db2JdbcContext.queryWithNext(DB2_CURRENT_ACHIEVE_LOG, resultSet -> {
            String currentLog = resultSet.getString(1);
            tapLogger.info("current log: {}", currentLog);
            String lriStr = null;
            int retry = 5;
            if (db2Config.getArchived()) {
                tapLogger.warn("starting archive log ...");
                executeDb2Command(String.format(DB2_ARCHIVE_COMMAND, db2Config.getDb2Profile(), db2Config.getDatabase()));
                tapLogger.info("archive complete");

                Instant pendingFirstDate = Db2OffsetUtils.queryPendingFirstDate(db2JdbcContext);
                if (null != pendingFirstDate) {
                    tapLogger.info("Has pending transaction, first date is: {}", pendingFirstDate);
                    String lri = getLriWithDate(pendingFirstDate, () -> !isAlive());
                    if (null == lri) {
                        tapLogger.warn("Has pending transaction and not found LRI in logs");
                    } else {
                        res.set(lri);
                        needParse.set(false);
                        return;
                    }
                }

                String command = String.format(DB2_CURRENT_LRI, db2Config.getDb2Profile(), db2Config.getSqlCtlDir(), currentLog, currentLog, db2Config.getArchiveDir());
                tapLogger.info("get lri command: {}", command);
                while (isAlive() && retry > 0) {
                    lriStr = executeDb2Command(command);
                    if (Pattern.matches(".*: has LRI range [^ ]* to [^ ]*", lriStr)) {
                        break;
                    }
                    retry--;
                    TapSimplify.sleep(5000);
                }
            } else {
                tapLogger.info("starting query online log ...");
                while (isAlive() && retry > 0 && !(lriStr = executeDb2Command(String.format(DB2_ONLINE_CURRENT_LRI, db2Config.getDb2Profile(), db2Config.getDatabase(), currentLog, currentLog))).contains("has LRI range")) {
                    retry--;
                    TapSimplify.sleep(5000);
                }
            }
            if (lriStr != null && lriStr.contains("has LRI range")) {
                res.set(lriStr);
            } else {
                throw new RuntimeException("db2 command to get LRI error");
            }
        });

        String commandRes = res.get();
        if (!needParse.get()) return commandRes;

        try {
            String part1 = commandRes.substring(commandRes.length() - 16);
            String part2 = commandRes.substring(commandRes.length() - 32, commandRes.length() - 16);
            String part3 = commandRes.substring(commandRes.length() - 48, commandRes.length() - 32);
            long scn = NumberConvertUtil.bytesToLong(BytesUtil.hexStringToByteArray(part1), 0, false);
            long transactionId = NumberConvertUtil.bytesToLong(BytesUtil.hexStringToByteArray(part2), 0, false);
            long chainId = NumberConvertUtil.bytesToLong(BytesUtil.hexStringToByteArray(part3), 0, false);
            tapLogger.info("current lri to C: {}", chainId + "," + transactionId + "," + scn);
            return chainId + "," + transactionId + "," + scn;
        } catch (Throwable e) {
            throw new RuntimeException(String.format("current lri from '%s' to C failed: %s", commandRes, e.getMessage()), e);
        }
    }

    private String executeDb2Command(String command) {
        return SshUtil.executeCommand(db2Config.getHost(), db2Config.getSshPort(), db2Config.getUser().toLowerCase(), db2Config.getPassword(), command).replaceAll("\n", "");
    }

    private boolean needReverse() throws Throwable {
        AtomicBoolean isBigEndian = new AtomicBoolean(false);
        db2JdbcContext.query("SELECT HEX(1) FROM SYSIBM.SYSDUMMY1", resultSet -> {
            if (resultSet.next()) {
                isBigEndian.set(!resultSet.getString(1).equals("01000000"));
            }
        });
        ByteOrder db2ByteOrder = isBigEndian.get() ? ByteOrder.BIG_ENDIAN : ByteOrder.LITTLE_ENDIAN;
        return db2ByteOrder != ByteOrder.nativeOrder();
    }

    private final static String DB2_CURRENT_ACHIEVE_LOG = "select CURRENT_ACTIVE_LOG from SYSIBMADM.SNAPDETAILLOG";
    private final static String DB2_CURRENT_LRI = ". %s && cd %s && db2flsn -lrirange -startlog %s -endlog %s -logpath2 %s";
    private final static String DB2_ARCHIVE_COMMAND = ". %s && db2 archive log for db %s";
    private final static String DB2_ONLINE_CURRENT_LRI = ". %s && db2flsn -db %s -startlog %s -endlog %s -lrirange";

    private TableInfo getTableInfo(TapConnectionContext tapConnectorContext, String tableName) throws Throwable {
        DataMap dataMap = db2JdbcContext.getTableInfo(tableName);
        TableInfo tableInfo = TableInfo.create();
        long rows = 0L;
        if (dataMap.getString("CARD") != null && !"-1".equals(dataMap.getString("CARD"))) {
            rows = Long.parseLong(dataMap.getString("CARD"));
        }
        tableInfo.setNumOfRows(rows);
        tableInfo.setStorageSize(Long.parseLong(dataMap.getString("AVGROWSIZE")) * rows);
        return tableInfo;
    }

    private String getLriWithDate(Instant beginDate, Supplier<Boolean> stopSupplier) {
        if (null == beginDate) return null;

        // query log file by date
        String logFileName = Db2OffsetUtils.findLogFileWithDate(db2Config, beginDate, stopSupplier);
        if (null != logFileName) {
            tapLogger.info("Found log name '{}' by date {}", logFileName, beginDate);
            logFileName = logFileName.trim();

            String logFileNumStr = logFileName.substring(1, logFileName.length() - 4);
            int logFileNum = Integer.parseInt(logFileNumStr);

            return Db2OffsetUtils.getEndLriByFlsn(db2Config, logFileNum, stopSupplier);
        }
        return null;
    }

    @Override
    protected void processDataMap(DataMap dataMap, TapTable tapTable) {
        for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
            Object value = entry.getValue();
            if (value instanceof Timestamp) {
                if (!tapTable.getNameFieldMap().containsKey(entry.getKey())) {
                    continue;
                }
                entry.setValue(((Timestamp) value).toLocalDateTime().minusHours(db2Config.getZoneOffsetHour()));
            } else if (value instanceof Date) {
                entry.setValue(Instant.ofEpochMilli(((Date) value).getTime()).atZone(ZoneId.systemDefault()).toLocalDateTime());
            } else if (value instanceof Time) {
                if (!tapTable.getNameFieldMap().containsKey(entry.getKey())) {
                    continue;
                }
                entry.setValue(Instant.ofEpochMilli(((Time) value).getTime()).atZone(ZoneId.systemDefault()).toLocalDateTime().minusHours(db2Config.getZoneOffsetHour()));
            } else if (value instanceof DB2Clob) {
                try (
                        Reader reader = ((DB2Clob) value).getCharacterStream();
                        BufferedReader br = new BufferedReader(reader)
                ) {
                    StringBuilder sb = new StringBuilder();
                    String line;
                    while ((line = br.readLine()) != null) {
                        sb.append(line);
                    }
                    entry.setValue(sb.toString());
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            } else if (value instanceof DB2Blob) {
                try (
                        InputStream inputStream = ((DB2Blob) value).getBinaryStream();
                        ByteArrayOutputStream outputStream = new ByteArrayOutputStream()
                ) {
                    byte[] buffer = new byte[4096];
                    int bytesRead;
                    while ((bytesRead = inputStream.read(buffer)) != -1) {
                        outputStream.write(buffer, 0, bytesRead);
                    }
                    entry.setValue(outputStream.toByteArray());
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            }
        }
    }

    protected String getHashSplitStringSql(TapTable tapTable) {
        return "HEX(HEX(RIGHT(HASH(ROWID, 0), 4)))";
    }

    protected String getHashSplitModConditions(TapTable tapTable, int maxSplit, int currentSplit) {
        String majorVersion = Optional.ofNullable(version).map(version -> version.split("\\.")[0]).orElse(null);
        if (Integer.parseInt(majorVersion) > 10) {
            return super.getHashSplitModConditions(tapTable, maxSplit, currentSplit);
        } else {
            return String.format("DBMS_UTILITY.GET_HASH_VALUE(ROWID,0,%d) = %d", maxSplit, currentSplit);
        }
    }
}
