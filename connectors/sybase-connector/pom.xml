<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>connectors</artifactId>
        <groupId>io.tapdata</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>sybase-connector</artifactId>
    <version>1.0-SNAPSHOT</version>
    <packaging>jar</packaging>

    <properties>
        <java.version>8</java.version>
        <tapdata.pdk.api.version>2.0.0-SNAPSHOT</tapdata.pdk.api.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>io.tapdata</groupId>
            <artifactId>tapdata-pdk-api</artifactId>
            <version>${tapdata.pdk.api.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>io.tapdata</groupId>
            <artifactId>tapdata-api</artifactId>
            <version>${tapdata.pdk.api.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>io.tapdata</groupId>
            <artifactId>logminer-core</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>io.tapdata</groupId>
            <artifactId>read-partition</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <version>5.8.1</version>
            <scope>test</scope>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>io.tapdata</groupId>-->
<!--            <artifactId>mysql-core</artifactId>-->
<!--            <version>1.0-SNAPSHOT</version>-->
<!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    <groupId>org.slf4j</groupId>-->
<!--                    <artifactId>slf4j-api</artifactId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>io.tapdata</groupId>-->
<!--            <artifactId>jdbc-sybase</artifactId>-->
<!--            <version>1.3.1</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>io.tapdata</groupId>
            <artifactId>jconn42_PL12_ebf30318</artifactId>
            <version>1.7.0</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>io.tapdata</groupId>
            <artifactId>pdk-error-code</artifactId>
        </dependency>

        <dependency>
            <groupId>org.yaml</groupId>
            <artifactId>snakeyaml</artifactId>
            <version>1.23</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>org.jyaml</groupId>-->
<!--            <artifactId>jyaml</artifactId>-->
<!--            <version>1.3</version>-->
<!--        </dependency>-->
        <!--        <dependency>-->
<!--            <groupId>com.amihaiemil.web</groupId>-->
<!--            <artifactId>eo-yaml</artifactId>-->
<!--            <version>7.0.8</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-compress</artifactId>
            <version>1.21</version>
        </dependency>
        <dependency>
            <groupId>net.sf.opencsv</groupId>
            <artifactId>opencsv</artifactId>
            <version>2.3</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-compress</artifactId>
            <version>1.19</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.11.0</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-core</artifactId>
            <version>5.7.15</version>
            <scope>compile</scope>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>org.postgresql</groupId>-->
<!--            <artifactId>postgresql</artifactId>-->
<!--            <version>42.3.4</version>-->
<!--        </dependency>-->
    </dependencies>
    <repositories>
        <repository>
            <id>tapdata-tapdata-maven</id>
            <name>maven</name>
            <url>https://tapdata-maven.pkg.coding.net/repository/tapdata/maven/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>zohodesksdk</id>
            <url>https://maven.zohodl.com</url>
        </repository>
    </repositories>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-assembly-plugin</artifactId>
                <version>3.1.1</version>

                <configuration>
                    <finalName>${connector.file.name}</finalName>
                    <descriptorRefs>
                        <descriptorRef>jar-with-dependencies</descriptorRef>
                    </descriptorRefs>
                    <archive>
                        <manifestEntries>
                            <Implementation-Title>${project.artifactId}</Implementation-Title>
                            <Implementation-Version>${project.version}</Implementation-Version>
                            <Implementation-Vendor>${project.groupId}</Implementation-Vendor>

                            <Build-OS>${os.name} ${os.version}</Build-OS>
                            <Build-Java>Java ${java.version}</Build-Java>
                            <PDK-Runner-Version>${tapdata.pdk.runner.version}</PDK-Runner-Version>
                            <PDK-API-Version>${tapdata.pdk.api.version}</PDK-API-Version>
                            <Tapdata-API-Version>${tapdata.pdk.api.version}</Tapdata-API-Version>
                            <Version>${project.version}</Version>
                            <Authentication>GA</Authentication>
                            <PayMode>${pay.mode}</PayMode>
                            <Git-Build-Time>${git.build.time}</Git-Build-Time>
                            <Git-Branch>${git.branch}</Git-Branch>
                            <Git-Commit-Id>${git.commit.id}</Git-Commit-Id>
                            <Git-Build-User-Name>${git.build.user.name}</Git-Build-User-Name>
                            <Git-Build-User-Email>${git.build.user.email}</Git-Build-User-Email>
                        </manifestEntries>
                    </archive>
                    <appendAssemblyId>false</appendAssemblyId>
                </configuration>

                <executions>
                    <execution>
                        <id>make-assembly</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                    </execution>
                </executions>

            </plugin>
            <plugin>
                <groupId>pl.project13.maven</groupId>
                <artifactId>git-commit-id-plugin</artifactId>
                <version>2.2.3</version>
                <executions>
                    <execution>
                        <id>get-the-git-infos</id>
                        <goals>
                            <goal>revision</goal>
                        </goals>
                        <phase>validate</phase>
                    </execution>
                </executions>
                <configuration>
                    <verbose>true</verbose>
                    <generateGitPropertiesFile>true</generateGitPropertiesFile>
                    <failOnNoGitDirectory>true</failOnNoGitDirectory>
                    <injectAllReactorProjects>true</injectAllReactorProjects>
                    <dotGitDirectory>${project.basedir}/../../.git</dotGitDirectory>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.1.0</version>
                <executions>
                    <execution>
                        <id>copy-resource-one</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>

                        <configuration>
                            <outputDirectory>../dist</outputDirectory>
                            <resources>
                                <resource>
                                    <directory>${project.basedir}/target/</directory>
                                    <includes>
                                        <include>${connector.file.name}.jar</include>
                                    </includes>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-bundle-plugin</artifactId>
                <version>4.2.1</version>
                <extensions>true</extensions>
            </plugin>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.7.0</version>
                <configuration>
                    <source>8</source>
                    <target>8</target>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <distributionManagement>
        <repository>
            <!--必须与 settings.xml 的 id 一致-->
            <id>tapdata-tapdata-maven</id>
            <name>maven</name>
            <url>https://tapdata-maven.pkg.coding.net/repository/tapdata/maven/</url>
        </repository>
    </distributionManagement>
</project>
