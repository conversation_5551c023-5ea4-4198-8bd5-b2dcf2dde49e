package io.tapdata.oceanbase.bean;

import io.tapdata.connector.oracle.config.OracleConfig;
import io.tapdata.kit.EmptyKit;

import java.io.Serializable;
import java.util.Map;

public class OceanbaseOracleConfig extends OracleConfig implements Serializable {
    private static final long serialVersionUID = 1L;

    private String tenant;
    private String rootServerList;
    private String cdcUser;
    private String cdcPassword;

    //customize
    public OceanbaseOracleConfig() {
        setEscapeChar('"');
        setDbType("oceanbase");
        setJdbcDriver("com.oceanbase.jdbc.Driver");
    }

    public OceanbaseOracleConfig load(Map<String, Object> map) {
        OceanbaseOracleConfig config = (OceanbaseOracleConfig) super.load(map);
        properties.put("rewriteBatchedStatements", "true");
        setSchema(getDatabase());
        return config;
    }

    public String getDatabaseUrlPattern() {
        // last %s reserved for extend params
        return "jdbc:" + getDbType() + "://%s:%d/%s%s";
    }

    public String getConnectionString() {
        String connectionString = getHost() + ":" + getPort() + "/" + getDatabase();
        if (EmptyKit.isNotBlank(getSchema())) {
            connectionString += "/" + getSchema();
        }
        return connectionString;
    }

    //deal with extend params no matter there is ?
    public String getDatabaseUrl() {
        if (EmptyKit.isNull(this.getExtParams())) {
            this.setExtParams("");
        }
        if (EmptyKit.isNotEmpty(this.getExtParams()) && !this.getExtParams().startsWith("?") && !this.getExtParams().startsWith(":")) {
            this.setExtParams("?" + this.getExtParams());
        }
        return String.format(this.getDatabaseUrlPattern(), this.getHost(), this.getPort(), this.getDatabase(), this.getExtParams());
    }

    public String getTenant() {
        return tenant;
    }

    public void setTenant(String tenant) {
        this.tenant = tenant;
    }

    public String getRootServerList() {
        return rootServerList;
    }

    public void setRootServerList(String rootServerList) {
        this.rootServerList = rootServerList;
    }

    public String getCdcUser() {
        return cdcUser;
    }

    public void setCdcUser(String cdcUser) {
        this.cdcUser = cdcUser;
    }

    public String getCdcPassword() {
        return cdcPassword;
    }

    public void setCdcPassword(String cdcPassword) {
        this.cdcPassword = cdcPassword;
    }
}
