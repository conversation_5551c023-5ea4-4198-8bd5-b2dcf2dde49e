{"properties": {"name": "Tablestore", "icon": "icons/tablestore.png", "doc": "${doc}", "id": "tablestore", "tags": ["Database"]}, "configOptions": {"connection": {"type": "object", "properties": {"endpoint": {"required": true, "type": "string", "title": "${endpoint}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "database_endpoint", "x-index": 1}, "instance": {"required": true, "type": "string", "title": "${instance}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "database_instance", "x-index": 2}, "accessKeyId": {"required": true, "type": "string", "title": "${accessKeyId}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "database_id", "x-index": 3}, "accessKeySecret": {"required": true, "type": "string", "title": "${accessKeySecret}", "x-decorator": "FormItem", "x-component": "Password", "apiServerKey": "database_key", "x-index": 4}, "accessKeyToken": {"required": false, "type": "string", "title": "${accessKeyToken}", "x-decorator": "FormItem", "x-component": "Password", "apiServerKey": "database_token", "x-index": 5}}}}, "messages": {"default": "en_US", "en_US": {"doc": "docs/tablestore_en_US.md", "endpoint": "endpoint", "instance": "instance", "accessKeyId": "AccessKey ID", "accessKeySecret": "<PERSON><PERSON><PERSON>", "accessKeyToken": "Access<PERSON>ey <PERSON>"}, "zh_CN": {"doc": "docs/tablestore_zh_CN.md", "endpoint": "服务地址", "instance": "实例名称", "accessKeyId": "AccessKey ID", "accessKeySecret": "<PERSON><PERSON><PERSON>", "accessKeyToken": "Access<PERSON>ey <PERSON>"}, "zh_TW": {"doc": "docs/tablestore_zh_TW.md", "endpoint": "服务地址", "instance": "实例名称", "accessKeyId": "AccessKey ID", "accessKeySecret": "<PERSON><PERSON><PERSON>", "accessKeyToken": "Access<PERSON>ey <PERSON>"}}, "dataTypes": {"STRING": {"byte": "16m", "to": "TapString"}, "INTEGER": {"bit": 64, "priority": 3, "value": [-9223372036854775808, 9223372036854775807], "to": "TapNumber"}, "DOUBLE": {"precision": [1, 17], "preferPrecision": 11, "scale": [0, 17], "preferScale": 4, "fixed": false, "to": "TapNumber"}, "BOOLEAN": {"to": "TapBoolean"}, "BINARY": {"to": "TapBinary"}}}