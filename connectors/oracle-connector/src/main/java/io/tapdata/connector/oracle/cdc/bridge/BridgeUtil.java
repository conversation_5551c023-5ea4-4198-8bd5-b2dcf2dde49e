package io.tapdata.connector.oracle.cdc.bridge;

import java.io.InputStream;
import java.io.OutputStream;
import java.net.Socket;

@Deprecated
public class BridgeUtil {

    public static boolean checkService(String host, int port) {
        try (
                Socket skt = new Socket(host, port);
                InputStream is = skt.getInputStream();
                OutputStream os = skt.getOutputStream()
        ) {
            // start service
            byte[] cmd = new byte[1];
            cmd[0] = 'B';
            os.write(cmd);

            byte[] response = new byte[3];
            int read = is.read(response);
            if (-1 != read) {
                return new String(response).equals("BOK");
            }
        } catch (Exception e) {
            return false;
        }
        return false;
    }

    public static int checkCurrentFno(String host, int port) {
        try (
                Socket skt = new Socket(host, port);
                InputStream is = skt.getInputStream();
                OutputStream os = skt.getOutputStream()
        ) {
            byte[] cmd = new byte[1];
            cmd[0] = 'N';
            os.write(cmd);

            byte[] response = new byte[48];
            int iread = is.read(response);
            if (-1 != iread) {
                String strFnoMax = new String(response).trim();
                return Integer.parseInt(strFnoMax);
            }
        } catch (Exception e) {
            return 0;
        }
        return 0;
    }

    public static boolean updateConfig(String host, int port) {
        try (
                Socket skt = new Socket(host, port);
                InputStream is = skt.getInputStream();
                OutputStream os = skt.getOutputStream()
        ) {
            byte[] cmd = new byte[1];

            // update config
            cmd[0] = 'U';
            os.write(cmd);

            byte[] response = new byte[3];
            int read = is.read(response);
            if (-1 != read) {
                return new String(response).equals("UOK");
            }
        } catch (Exception e) {
            return false;
        }
        return false;
    }


}
