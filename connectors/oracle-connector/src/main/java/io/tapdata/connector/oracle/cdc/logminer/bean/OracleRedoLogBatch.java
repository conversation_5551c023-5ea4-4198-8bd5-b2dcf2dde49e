package io.tapdata.connector.oracle.cdc.logminer.bean;

import io.tapdata.kit.EmptyKit;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class OracleRedoLogBatch {

    /**
     * key: instance thread
     * value: redo logs list
     */
    private Map<Long, List<RedoLog>> redoLogMap;

    private boolean isOnlineRedo;

    private List<RedoLog> redoLogs;

    private Long stopMineSCN;

    private Map<Long, ThreadRedoLog> startScnRedoLogMap;

    private boolean isAllArchived;

    public OracleRedoLogBatch() {
        redoLogs = new ArrayList<>();
    }

    public OracleRedoLogBatch(Map<Long, List<RedoLog>> redoLogMap, boolean isOnlineRedo) {
        if (EmptyKit.isNotEmpty(redoLogMap)) {
            for (List<RedoLog> redoLogs : redoLogMap.values()) {
                if (EmptyKit.isNotEmpty(redoLogs)) {
                    Collections.sort(redoLogs);
                }
            }
        }
        this.redoLogMap = redoLogMap;
        this.isOnlineRedo = isOnlineRedo;
    }

    public Map<Long, List<RedoLog>> getRedoLogMap() {
        return redoLogMap;
    }

    public void setRedoLogMap(Map<Long, List<RedoLog>> redoLogMap) {
        this.redoLogMap = redoLogMap;
    }

    public boolean isOnlineRedo() {
        return isOnlineRedo;
    }

    public void setOnlineRedo(boolean onlineRedo) {
        isOnlineRedo = onlineRedo;
    }

    public List<RedoLog> getRedoLogs() {
        return redoLogs;
    }

    public void setRedoLogs(List<RedoLog> redoLogs) {
        this.redoLogs = redoLogs;
    }

    public Long getStopMineSCN() {
        return stopMineSCN;
    }

    public void setStopMineSCN(Long stopMineSCN) {
        this.stopMineSCN = stopMineSCN;
    }

    public Map<Long, ThreadRedoLog> getStartScnRedoLogMap() {
        return startScnRedoLogMap;
    }

    public void setStartScnRedoLogMap(Map<Long, ThreadRedoLog> startScnRedoLogMap) {
        this.startScnRedoLogMap = startScnRedoLogMap;
    }

    public boolean isAllArchived() {
        return isAllArchived;
    }

    public void setAllArchived(boolean allArchived) {
        isAllArchived = allArchived;
    }

    @Override
    public String toString() {
        if (EmptyKit.isNotEmpty(redoLogs)) {
            return "OracleRedoLogBatch{" + "redoLogs=[" + redoLogs.stream().map(RedoLog::toString).collect(Collectors.joining(",")) +
                    "], stopMineSCN=" + stopMineSCN +
                    '}';
        } else {
            return "OracleRedoLogBatch{" + "redoLogMap=" + redoLogMap +
                    ", isOnlineRedo=" + isOnlineRedo +
                    '}';
        }
    }
}
