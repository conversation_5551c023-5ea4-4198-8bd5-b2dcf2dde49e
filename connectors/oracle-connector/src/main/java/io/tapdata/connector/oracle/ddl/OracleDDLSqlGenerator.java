package io.tapdata.connector.oracle.ddl;

import io.tapdata.common.CommonDbConfig;
import io.tapdata.common.ddl.DDLSqlGenerator;
import io.tapdata.entity.event.ddl.entity.ValueChange;
import io.tapdata.entity.event.ddl.table.TapAlterFieldAttributesEvent;
import io.tapdata.entity.event.ddl.table.TapAlterFieldNameEvent;
import io.tapdata.entity.event.ddl.table.TapDropFieldEvent;
import io.tapdata.entity.event.ddl.table.TapNewFieldEvent;
import io.tapdata.entity.logger.TapLogger;
import io.tapdata.entity.schema.TapField;
import io.tapdata.kit.EmptyKit;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class OracleDDLSqlGenerator implements DDLSqlGenerator {

    private final static String TABLE_NAME_FORMAT = "\"%s\".\"%s\"";
    private final static String ALTER_TABLE_PREFIX = "ALTER TABLE " + TABLE_NAME_FORMAT;
    private final static String COLUMN_NAME_FORMAT = "\"%s\"";

    public List<String> addColumn(CommonDbConfig config, TapNewFieldEvent tapNewFieldEvent) {
        List<String> sqls = new ArrayList<>();
        if (null == tapNewFieldEvent) {
            return null;
        }
        List<TapField> newFields = tapNewFieldEvent.getNewFields();
        if (null == newFields) {
            return null;
        }
        String schema = config.getSchema();
        String tableId = tapNewFieldEvent.getTableId();
        if (EmptyKit.isBlank(tableId)) {
            throw new RuntimeException("Append add column ddl sql failed, table name is blank");
        }
        for (TapField newField : newFields) {
            StringBuilder addFieldSql = new StringBuilder(String.format(ALTER_TABLE_PREFIX, schema, tableId)).append(" ADD");
            String fieldName = newField.getName();
            if (EmptyKit.isNotBlank(fieldName)) {
                addFieldSql.append(" ").append(String.format(COLUMN_NAME_FORMAT, fieldName));
            } else {
                throw new RuntimeException("Append add column ddl sql failed, field name is blank");
            }
            String dataType = newField.getDataType();
            if (EmptyKit.isNotBlank(dataType)) {
                addFieldSql.append(" ").append(dataType);
            } else {
                throw new RuntimeException("Append add column ddl sql failed, data type is blank");
            }
            Object defaultValue = newField.getDefaultValue();
            if (null != defaultValue) {
                addFieldSql.append(" DEFAULT '").append(defaultValue).append("'");
            }
            Boolean nullable = newField.getNullable();
            if (null != nullable) {
                if (nullable) {
                    addFieldSql.append(" NULL");
                } else {
                    addFieldSql.append(" NOT NULL");
                }
            }
            sqls.add(addFieldSql.toString());

            String comment = newField.getComment();
            if (EmptyKit.isNotBlank(comment)) {
                sqls.add("COMMENT ON COLUMN " + String.format(TABLE_NAME_FORMAT, schema, tableId) + "." + String.format(COLUMN_NAME_FORMAT, fieldName) + " IS '" + comment + "'");
            }

            Boolean primaryKey = newField.getPrimaryKey();
            if (null != primaryKey && primaryKey) {
                TapLogger.warn(OracleDDLSqlGenerator.class.getSimpleName(), "Alter oracle table's primary key does not supported, please do it manually");
            }
        }
        return sqls;
    }

    @Override
    public List<String> alterColumnAttr(CommonDbConfig config, TapAlterFieldAttributesEvent tapAlterFieldAttributesEvent) {
        List<String> sqls = new ArrayList<>();
        if (null == tapAlterFieldAttributesEvent) {
            return null;
        }
        String schema = config.getSchema();
        String tableId = tapAlterFieldAttributesEvent.getTableId();
        if (EmptyKit.isBlank(tableId)) {
            throw new RuntimeException("Append alter column attr ddl sql failed, table name is blank");
        }
        StringBuilder sql = new StringBuilder(String.format(ALTER_TABLE_PREFIX, schema, tableId)).append(" MODIFY");
        String fieldName = tapAlterFieldAttributesEvent.getFieldName();
        if (EmptyKit.isNotBlank(fieldName)) {
            sql.append(" ").append(String.format(COLUMN_NAME_FORMAT, fieldName));
        } else {
            throw new RuntimeException("Append alter column attr ddl sql failed, field name is blank");
        }
        ValueChange<String> dataTypeChange = tapAlterFieldAttributesEvent.getDataTypeChange();
        if (EmptyKit.isNotBlank(dataTypeChange.getAfter())) {
            sql.append(" ").append(dataTypeChange.getAfter());
        }
        ValueChange<Boolean> nullableChange = tapAlterFieldAttributesEvent.getNullableChange();
        if (null != nullableChange && null != nullableChange.getAfter()) {
            if (!nullableChange.getAfter()) {
                sql.append(" NOT NULL");
            }
        }
        sqls.add(sql.toString());
        ValueChange<String> commentChange = tapAlterFieldAttributesEvent.getCommentChange();
        if (null != commentChange && EmptyKit.isNotBlank(commentChange.getAfter())) {
            sqls.add("COMMENT ON COLUMN " + String.format(TABLE_NAME_FORMAT, schema, tableId) + "." + String.format(COLUMN_NAME_FORMAT, fieldName) + " IS '" + commentChange.getAfter() + "'");
        }
        ValueChange<Integer> primaryChange = tapAlterFieldAttributesEvent.getPrimaryChange();
        if (null != primaryChange && null != primaryChange.getAfter() && primaryChange.getAfter() > 0) {
            TapLogger.warn(OracleDDLSqlGenerator.class.getSimpleName(), "Alter oracle table's primary key does not supported, please do it manually");
        }
        return sqls;
    }

    @Override
    public List<String> alterColumnName(CommonDbConfig config, TapAlterFieldNameEvent tapAlterFieldNameEvent) {
        if (null == tapAlterFieldNameEvent) {
            return null;
        }
        String schema = config.getSchema();
        String tableId = tapAlterFieldNameEvent.getTableId();
        if (EmptyKit.isBlank(tableId)) {
            throw new RuntimeException("Append alter column name ddl sql failed, table name is blank");
        }
        ValueChange<String> nameChange = tapAlterFieldNameEvent.getNameChange();
        if (null == nameChange) {
            throw new RuntimeException("Append alter column name ddl sql failed, change name object is null");
        }
        String before = nameChange.getBefore();
        String after = nameChange.getAfter();
        if (EmptyKit.isBlank(before)) {
            throw new RuntimeException("Append alter column name ddl sql failed, old column name is blank");
        }
        if (EmptyKit.isBlank(after)) {
            throw new RuntimeException("Append alter column name ddl sql failed, new column name is blank");
        }
        return Collections.singletonList(String.format(ALTER_TABLE_PREFIX, schema, tableId) + " RENAME COLUMN " + String.format(COLUMN_NAME_FORMAT, before) + " TO " + String.format(COLUMN_NAME_FORMAT, after));
    }

    @Override
    public List<String> dropColumn(CommonDbConfig config, TapDropFieldEvent tapDropFieldEvent) {
        if (null == tapDropFieldEvent) {
            return null;
        }
        String schema = config.getSchema();
        String tableId = tapDropFieldEvent.getTableId();
        if (EmptyKit.isBlank(tableId)) {
            throw new RuntimeException("Append drop column ddl sql failed, table name is blank");
        }
        String fieldName = tapDropFieldEvent.getFieldName();
        if (EmptyKit.isBlank(fieldName)) {
            throw new RuntimeException("Append drop column ddl sql failed, field name is blank");
        }
        return Collections.singletonList(String.format(ALTER_TABLE_PREFIX, schema, tableId) + " DROP COLUMN " + String.format(COLUMN_NAME_FORMAT, fieldName));
    }
}
