export default {
  // 通用按钮
  button_reload: '重新加載schema',
  button_button: '新增',
  // 通用消息
  message_loading: '正在加載',
  message_network_connected: '網絡已恢復',
  //頁面標題
  page_title_overview: '概覽',
  page_title_dashboard: '工作台',
  page_title_connections: '連接管理',
  page_title_connections_create: '創建連接',
  page_title_connections_edit: '編輯連接',
  page_title_data_pipeline: '數據管道',
  page_title_advanced_features: '高級功能',
  page_title_data_copy: '數據複製',
  page_title_task_edit: '編輯任務',
  page_title_task_details: '任務詳情',
  page_title_task_stat: '任務統計',
  page_title_run_monitor: '運行監控',
  page_title_data_develop: '數據轉換',
  page_title_data_verify: '數據校驗',
  page_title_data_difference_details: '差異詳情',
  page_title_data_verification_result: '校驗結果',
  page_title_diff_verification_history: '差異校驗歷史',
  page_title_diff_verification_details: '差異校驗詳情',
  page_title_shared_mining: '共享挖掘',
  page_title_heartbeat_table: '心跳任務',
  page_title_shared_mining_details: '挖掘詳情',
  page_title_function: '函數管理',
  page_title_function_import: '導入函數',
  page_title_function_create: '創建函數',
  page_title_function_edit: '編輯函數',
  page_title_function_details: '函數詳情',
  page_title_shared_cache: '共享緩存',
  page_title_shared_cache_create: '創建緩存',
  page_title_shared_cache_edit: '編輯緩存',
  page_title_data_discovery: '數據發現',
  page_title_data_object: '數據對象',
  page_title_data_catalogue: '數據目錄',
  page_title_data_service: '數據服務',
  page_title_data_server_list: '服務管理',
  page_title_api_application: '應用管理',
  page_title_api_client: '客戶端',
  page_title_api_servers: '服務器',
  page_title_api_audit: '服務審計',
  page_title_api_audit_details: '審計詳情',
  page_title_api_monitor: '服務監控',
  page_title_system: '系統管理',
  page_title_data_metadata: '元數據管理',
  page_title_cluster: '集群管理',
  page_title_user: '用戶管理',
  page_title_role: '角色管理',
  page_title_setting: '系統設置',
  page_title_webhook_alerts: 'Webhook 告警',
  page_title_license: 'License管理',
  page_title_back_menu: '返回菜单',
  page_title_custom_node: '自定義節點',
  page_title_account: '個人設置',
  page_title_external_storage: '外存管理',
  page_title_verification_create: '新建校驗',
  page_title_verification_edit: '編輯校驗',
  page_title_verification_history: '校驗歷史',
  page_title_data_console: '數據面板',
  // -- 多表選擇器
  component_table_selector_candidate_label: '待覆制表',
  component_table_selector_checked_label: '已選擇表',
  component_table_selector_error_not_exit: '表不存在',
  component_table_selector_error: '所選表存在異常',
  component_table_selector_autofix: '清除異常表',
  component_table_selector_bulk_pick: '批量選表',
  component_table_selector_not_checked: '您暫時沒有選擇表',
  component_table_selector_tables_empty: '您暫時沒有表，請點擊右上角重新加載表',
  component_table_selector_clipboard_placeholder:
    '請輸入表名稱並以逗號分隔,例如：table_a,table_b',
  // app
  app_license_expire_warning: 'License剩餘 {0} 天到期',
  // Agent
  agent_check_error: 'Agent當前狀態異常無法創建連接，請檢查',
  // 控制台
  dashboard_status_paused: '已暫停',
  dashboard_status_wait_run: '啟動中',
  dashboard_all_total: '全部任務',
  dashboard_copy_total: '複製任務',
  dashboard_sync_total: '開發任務',
  dashboard_valid_total: '校驗任務',
  dashboard_current_all_total: '全部任務',
  dashboard_current_copy_total: '複製任務',
  dashboard_current_sync_total: '數據轉換任務',
  dashboard_current_valid_total: '校驗任務',
  dashboard_copy_overview_title: '複製任務概覽',
  dashboard_copy_status_title: '複製任務狀態',
  dashboard_sync_overview_title: '開發任務概覽',
  dashboard_sync_status_title: '開發任務狀態',
  dashboard_valid_title: '數據校驗概覽',
  dashboard_transfer_overview: '傳輸總覽',
  dashboard_server_title: '集群總覽',
  dashboard_total_valid: '全部校驗任務',
  dashboard_passed: '校驗一致',
  dashboard_countDiff: 'Count不一致',
  dashboard_valueDiff: '內容差異',
  dashboard_initializing: '初始化中',
  dashboard_initialized: '初始化完成',
  dashboard_cdc: '增量中',
  dashboard_Lag: '增量滯後',
  dashboard_server: '服務器',
  dashboard_management: '管理端',
  dashboard_task_transfer: '任務傳輸',
  dashboard_api_service: 'API服務',
  dashboard_starting: '啟動中',
  dashboard_running: '運行中',
  dashboard_stopping: '關閉中',
  dashboard_stopped: '已關閉',
  dashboard_restarting: '重啟中',
  dashboard_total_insert: '總插入',
  dashboard_total_update: '總更新',
  dashboard_total_delete: '總刪除',
  dashboard_public_error: '錯誤',
  dashboard_public_total: '總計',
  dashboard_total: '開啟校驗任務數',
  dashboard_diff: '校驗有差異的任務數',
  dashboard_can: '支持校驗任務數',
  dashboard_error: '校驗出錯的任務數',
  dashboard_no_data_here: '這裡沒用數據哦~',
  dashboard_no_statistics: '暫無{0}統計',
  // 元數據管理
  metadata_db: '所屬庫',
  metadata_change_name: '改名',
  metadata_name_placeholder: '請輸入表名/數據庫名',
  metadata_meta_type_directory: '目錄',
  metadata_meta_type_table: '數據表',
  metadata_form_create: '創建模型',
  metadata_form_database: '數據庫',
  metadata_form_collection: '數據集',
  metadata_form_mongo_view: 'Mongodb視圖',
  metadata_detail_original_table_name: '原表名',
  metadata_detail_original_database_name: '原庫名',
  // api發布
  modules_active: '已發布',
  modules_pending: '未發布',
  modules_create: '創建 API',
  modules_import: '導入',
  modules_api_test: 'API測試',
  modules_publish_api: '發布 API',
  modules_unpublish_api: '取消發布',
  modules_dialog_import_title: '任務導入',
  modules_dialog_condition: '條件',
  modules_dialog_overwrite_data: '覆蓋已有數據',
  modules_dialog_skip_data: '跳過已有數據',
  modules_dialog_group: '分組',
  modules_dialog_file: '文件',
  modules_dialog_upload_files: '上傳文件',
  modules_header_api_name: 'API名稱',
  modules_header_dataSource: '數據源',
  modules_header_tablename: '表名稱',
  modules_header_status: '狀態',
  modules_header_basePath: '基礎路徑',
  modules_header_classifications: '分類',
  modules_header_username: '創建者',
  modules_status_deploying: '部署中',
  modules_status_starting: '正在啟動',
  modules_status_running: '運行中',
  modules_status_restart: '更新中',
  modules_status_deploy_fail: '發布API失敗',
  modules_status_exit: '已退出',
  modules_status_stop: '已停止',
  modules_status_ready: '有效',
  modules_status_invalid: '無效',
  modules_allacancel: '批量取消',
  modules_allarelease: '批量發布',
  modules_releasefb: '你確定要批量發布以下API嗎?',
  modules_releasecancel: '你確定要批量取消以下API嗎?',
  modules_api_server_status: 'API 服務狀態',
  modules_sure: '你確定要',
  modules_cancel_failed: '取消發布API失敗',
  modules_name_placeholder: '請輸入表名/數據庫名',
  module_form_connection: '數據庫',
  module_form_tablename: '表名稱',
  module_form_default_Api: '默認CURD API',
  module_form_customer_Api: '自定義API',
  module_form_noPath: '請添加路徑',
  module_form_prefix: '前綴',
  module_form_basePath: '基礎路徑',
  module_form_path: '路徑',
  module_form_security: '權限設置',
  module_form_permission: '權限',
  module_form_choose: '選擇目錄',
  module_form_document: 'API 文檔',
  module_form_tags: '數據目錄',
  module_form_preview: '數據預覽',
  module_form_public_api: '公共的',
  module_form_available_query_field: '可用查詢字段',
  module_form_required_query_field: '必須的查詢條件',
  module_form_validator_name:
    '只能包含中文、字母、數字、下劃線和美元符號,並且數字不能開頭',
  module_form_create_a_new_record: '創建新記錄',
  module_form_get_record_by_id: '根據id獲取記錄',
  module_form_update_record_by_id: '根據id更新記錄',
  module_form_delete_record_by_id: '根據id刪除記錄',
  module_form_get_record_list_by_page_and_limit: '分頁獲取記錄',
  module_form_method: '方法',
  module_form_fields: '字段',
  module_form_datatype: '數據類型',
  module_form_condition: '過濾條件',
  module_form_get_api_uri_fail: '獲取 API Server Uri 失敗',
  module_form_name_null: '名稱不能為空',
  query_build_match_condition: '匹配條件',
  query_build_any: '任意',
  query_build_addGroup: '添加組',
  query_build_removeGroup: '刪除組',
  query_build_addCondition: '添加條件',
  query_build_removeCondition: '刪除條件',
  query_build_show_filter: '顯示過濾條件',
  query_build_queryValue: '字段值',
  query_build_add: '添加',
  // 客戶端
  application_header_id: '客戶端ID',
  application_header_client_name: '客戶端名稱',
  application_header_grant_type: '授權類型',
  application_header_client_secret: '客戶端密鑰',
  application_header_redirect_uri: '重定向URI',
  application_header_scopes: '權限範圍',
  application_generator: '生成',
  application_show_menu: '顯示到菜單',
  application_true: '是',
  application_false: '否',
  application_create: '創建客戶端',
  //api 监控
  api_monitor_total_totalCount: 'API總數',
  api_monitor_total_warningApiCount: 'API訪問總數',
  api_monitor_total_warningVisitCount: 'API訪問告警總數',
  api_monitor_total_visitTotalLine: 'API訪問總行數',
  api_monitor_total_transmitTotal: 'API傳輸總量',
  api_monitor_total_warningCount: 'API告警數',
  api_monitor_total_successCount: 'API成功數',
  api_monitor_total_columns_failed: '失敗率(%)',
  api_monitor_total_FailRate: 'API失敗率TOP排序',
  api_monitor_total_consumingTime: 'API響應時間TOP',
  api_monitor_total_rTime: '最大響應時間',
  api_monitor_total_clientName: '客戶端',
  api_monitor_total_api_list: 'API列表',
  api_monitor_total_api_list_name: 'API名稱',
  api_monitor_total_api_list_status: 'API狀態',
  api_monitor_total_api_list_visitLine: 'API訪問行數',
  api_monitor_total_api_list_visitCount: 'API訪問次數',
  api_monitor_total_api_list_transitQuantity: 'API訪問傳輸量',
  api_monitor_total_api_list_status_active: '已發布',
  api_monitor_total_api_list_status_pending: '待發布',
  api_monitor_total_api_list_status_generating: '待生成',
  api_monitor_detail_visitTotalCount: 'API訪問成功次數',
  api_monitor_detail_visitQuantity: 'API傳輸量',
  api_monitor_detail_timeConsuming: 'API訪問耗時',
  api_monitor_detail_visitTotalLine: 'API訪問行數',
  api_monitor_detail_speed: 'API傳輸速率',
  api_monitor_detail_responseTime: 'API響應時間',
  api_monitor_detail_monitoring_period: '監控週期',
  api_monitor_detail_Monitoring_conditions: '監控條件',
  // api服務器
  api_server_user: '用戶',
  api_server_create: '新建服務端',
  api_server_create_server: '創建服務器',
  api_server_process_id: 'API 服務器唯一標識',
  api_server_client_name: ' API 服務器名稱',
  api_server_client_uri: 'API 服務器訪問地址',
  api_server_download_API_Server_config: '下載API配置文件',
  api_server_no_available: '沒有可用API服務器',
  // api瀏覽
  dataExplorer_query: '查詢',
  dataExplorer_document: 'API 文檔',
  dataExplorer_query_time: '查詢使用',
  dataExplorer_render_time: '渲染使用',
  dataExplorer_tag_title: '設置標籤',
  dataExplorer_show_column: '顯示列',
  dataExplorer_new_document: '新增記錄',
  dataExplorer_timeout: '超時時間',
  dataExplorer_unauthenticated: '您無權訪問API。 ',
  dataExplorer_message_timeout:
    '連接API服務器超時,請檢查API服務器是否已啟動。 ',
  dataExplorer_publish: '在API服務器上沒有找到 {id} 的API，請檢查是否已發布。 ',
  dataExplorer_no_permissions: '您的令牌已過期，請刷新頁面重試。 ',
  dataExplorer_datetype_without_timezone: '時間類型的時區（可選）',
  dataExplorer_mysql_datetype_without_timezone: '影響的類型：DATETIME',
  dataExplorer_export: '導出文件',
  dataExplorer_add_favorite: '收藏',
  dataExplorer_add_favorite_name: 'Favorite name',
  dataExplorer_format: '格式化代碼',
  dataExplorer_apiservr: 'api服務器',
  dataExplorer_base_path: '基礎路徑',
  // api審計
  apiaudit_name: 'API名稱',
  apiaudit_access_type: '訪問類型',
  apiaudit_visitor: '訪問人員',
  apiaudit_ip: '訪問人員IP',
  apiaudit_interview_time: '訪問時間',
  apiaudit_visit_result: '訪問結果',
  apiaudit_reason_fail: '失敗原因',
  apiaudit_log_info: '日誌詳情',
  apiaudit_parameter: '參數',
  apiaudit_link: '鏈接',
  apiaudit_access_records: '訪問記錄數',
  apiaudit_average_access_rate: 'API 平均訪問速率',
  apiaudit_access_time: '訪問耗時',
  apiaudit_average_response_time: '平均響應時長',
  apiaudit_success: '成功',
  apiaudit_placeholder: '請輸入名稱/ID',
  // 連接
  connection_list_form_database_type: '數據庫類型',
  connection_list_name: '連接名',
  connection_reload_schema_confirm_title: '重新加載 schema',
  connection_reload_schema_confirm_msg:
    '如果此庫的schema過多，可能耗時較長，確定要刷新數據源的schema',
  connection_reload_schema_fail: 'Schema 加載失敗',
  // Dag
  // 緩存節點提示
  task_list_status_all: '全部狀態',
  task_list_important_reminder: '重要提醒',
  task_list_stop_confirm_message:
    '初始化類型的任務暫停後如果再次啟動，任務會從頭開始同步，確定暫停？',
  task_status_running: '已運行',
  task_status_not_running: '未運行',
  task_info_progress: '進行中',
  // 函数管理
  function_button_import_jar: '導入jar包',
  function_details: '函數詳情',
  function_tips_name_repeat: '函數名稱重複',
  function_type_label: '函數類型',
  function_type_option_custom: '自定義函數',
  function_type_option_jar: '第三方jar包',
  function_type_option_system: '系統函數',
  function_name_label: '函數名稱',
  function_name_placeholder: '請輸入函數名稱',
  function_name_repeat: '函數名稱重復',
  function_class_label: '類名',
  function_file_label: 'jar文件',
  function_button_file_upload: '點擊上傳',
  function_file_upload_tips: '請上傳jar包文件',
  function_file_upload_success: '上傳成功',
  function_file_upload_fail: '上傳失敗',
  function_parameters_describe_label: '參數說明',
  function_parameters_describe_placeholder:
    '支持輸入的參數類型以及返回參數類型的具體說明',
  function_return_value_label: '返回值',
  function_return_value_placeholder: '請輸入返回值',
  function_describe_placeholder: '請輸入描述',
  function_format: '命令格式',
  function_format_placeholder: '请输入命令格式',
  function_jar_file_label: 'jar文件',
  function_package_name_label: '包名',
  function_package_name_placeholder: '請輸入包名',
  function_class_name_label: '類名',
  function_method_name_label: '方法名',
  function_script_label: '代碼詳情',
  function_script_empty: '請輸入函數代碼',
  function_script_missing_function_name: '缺少函數名',
  function_script_missing_function_body: '缺少函數體',
  function_script_format_error: '函數格式不正確',
  function_script_only_one: '只允許創建一個函數',
  function_import_list_title: '函數列表',
  function_button_load_function: '加載函數',
  function_message_load_function_fail: '加载函數失败',
  function_dialog_setting_title: '函數设置',
  function_message_function_empty: '請上傳jar包文件並加載函數',
  function_message_delete_title: '刪除函數',
  function_message_delete_content:
    '刪除可能會導致已調用該函數的任務報錯，確定刪除該函數嗎？',
  function_tips_max_size: '最大',
  // 解決方案
  solution_customer_job_logs: '客戶任務日誌',
  solution_error_code: '錯誤碼',
  solution_select_placeholder_type: '請選擇類型',
  solution_search_result: '結果',
  solution_search_solutions: '解決方案',
  // 共享挖掘
  shared_cdc_placeholder_task_name: '請輸入挖掘任務名搜索',
  shared_cdc_placeholder_connection_name: '請輸入連接名稱搜索',
  shared_cdc_name: '請輸入挖掘名稱',
  shared_cdc_setting_select_mode: '存储模式',
  shared_cdc_setting_select_mongodb_tip: '請輸入mongodb連接',
  shared_cdc_setting_select_table_tip: '請輸入表名',
  shared_cdc_setting_select_time_tip: '請選擇日誌保存時長',
  shared_cdc_setting_message_edit_save: '保存成功，重啟任務後生效',
  share_list_name: '挖掘名稱',
  share_list_time_excavation: '挖掘所處時間點',
  share_list_time: '挖掘延遲',
  share_list_setting: '挖掘設置',
  share_list_status: '狀態',
  share_list_edit_title: '挖掘编辑',
  share_list_edit_title_start_time: '挖掘開始時間',
  share_form_setting_table_name: '存儲MongoDB表名',
  share_form_setting_log_time: '日誌保存時長',
  share_form_edit_name: '挖掘名稱',
  share_form_edit_title: '是否放棄編輯該挖掘任務',
  share_form_edit_text: '此操作不會保存已修改的內容',
  share_detail_mining_info: '挖掘信息',
  share_detail_name: '挖掘名稱',
  share_detail_log_mining_time: '日誌挖掘時間',
  share_detail_log_time: '日誌保存時長',
  share_detail_call_task: '調用任務',
  share_detail_source_time: '源庫時間點',
  share_detail_sycn_time_point: '同步時間點',
  share_detail_mining_status: '挖掘狀態',
  share_detail_button_table_info: '表詳情',
  share_detail_statistics_time: '統計時間',
  share_detail_incremental_time: '所處的時間點',
  // 设置
  setting_email_template: '郵件模板',
  setting_saveSuccess: '保存成功，一分鐘後生效',
  setting_nameserver: '服務器名稱',
  setting_Log: '日誌',
  setting_SMTP: 'SMTP',
  setting_Job: '任務',
  setting_License: 'License控制',
  setting_expiredate: '到期時間',
  setting_import: '導入',
  setting_apply: '申請 license',
  setting_license_expire_date: 'License過期時間',
  setting_Worker: '進程',
  setting_Download: '下載',
  setting_Log_level: '日誌等級',
  setting_maxCpuUsage: '最大CPU使用率(取值範圍 0.1 ~ 1)',
  setting_maxHeapMemoryUsage: '最大堆內存使用率(取值範圍 0.1 ~ 1)',
  setting_switch_insert_mode_interval:
    ' 增量模式下切換到批量插入模式間隔時間（單位：秒）',
  setting_Email_Communication_Protocol: ' 加密方式',
  setting_SMTP_Server_Port: 'SMTP 服務端口',
  setting_SMTP_Server_User: 'SMTP 服務賬號',
  setting_SMTP_Server_password: 'SMTP 服務密碼',
  setting_Email_Receivers: 'Email接收郵件地址',
  setting_Email_Send_Address: 'Email發送郵件地址',
  setting_SMTP_Server_Host: 'SMTP 服務Host',
  setting_Send_Email_Title_Prefix: '發​​送Email標題的前綴（可選）',
  setting_SMTP_Proxy_Host: 'SMTP 代理服務Host (可選）',
  setting_SMTP_Proxy_Port: 'SMTP 代理服務端口 (可選）',
  setting_Email_Template_Running: '任務啟動通知',
  setting_Email_Template_Paused: '任務停止通知',
  setting_Email_Template_Error: '任務出錯通知',
  setting_Email_Template_Draft: '任務被編輯通知',
  setting_Email_Template_CDC: '任務增量滯後通知',
  setting_Email_Template_DDL: 'DDL錯誤通知',
  setting_Clean_Message_Time: '清除消息時間',
  setting_Keep_Alive_Message: '保持在線消息',
  setting_Sample_Rate: '採樣率',
  setting_task_load_threshold: '負載閾值（百分比）',
  setting_task_load_statistics_time: '負載統計時間（分鐘）',
  setting_ApiServer: 'API分發設置',
  setting_Default_Limit: '默認查詢返回行數',
  setting_Max_Limit: '最大查詢返回行數',
  setting_Send_batch_size: '打包數據條數',
  setting_hint_Send_batch_size: '打包數據條數',
  setting_Mongodb_target_create_date: '是否在目標端數據集添加創建時間',
  setting_Mongodb_target_create_date_docs: '是否在目標端數據集添加創建時間',
  setting_System: '系統資源監控',
  setting_Collect_system_info_interval: '系統資源監控採集頻率(秒)',
  setting_Interval_to_collect_system_info:
    '系統資源信息（CPU，內存，硬盤使用率）監控採集頻率',
  setting_Job_Sync_Mode: '任務同步模式',
  setting_Worker_Threshold: '進程閾值',
  setting_Worker_Heartbeat_Expire: '進程心跳過期時間(秒)',
  setting_License_Key: '證書秘鑰',
  setting_Enter_jobs_log_level__error_warn_info_debug_trace:
    '輸入任務日誌等級: error/warn/info/debug/trace',
  setting_Email_Receivers_Multiple_separated_by_semicolons:
    '郵件接收者,可輸入多個，通過逗號分隔',
  setting_Keep_recent_n_hours_message_before_the_last_processed_message_s_time_:
    '保持最近n小時消息',
  setting_Store_full_record_as_embedded_document_in_target_collection_for_update_operations:
    '緩存一份當前整體數據，合併到目標數據集中',
  setting_Store_before_field_as_embedded_document_in_target_collection_before_update_operation:
    '緩存一份修改前的整體數據，合併到目標數據集中',
  setting_Store_job_script_processor_log_to_cloud: '是否傳輸任務日誌到雲端',
  setting_Validator_to_validate_data__s_sample_rate: '校驗數據採樣率',
  setting_Process_message_mode__consistency_fast:
    '消息處理模式 consistency/fast',
  setting_Worker_can_execute_the_nums_of_Jobs: '進程可以執行多個任務',
  setting_Worker_heartbeat_expire_time: '進程心跳過期時間',
  setting_Users: ' 用戶',
  setting_Show_Page: ' 顯示下載頁面',
  setting_User_Registery: ' 用戶註冊管理',
  setting_hint_Show_Page: '顯示下載頁面',
  setting_hint_User_Registery:
    '用戶註冊類型設置。值設為 "disabled":禁止註冊; 值設為 "self-signup" 啟用用戶自助註冊; 值設為 "manual-approval" 允用戶註冊,但需要管理員審批。 ',
  setting_DR_Rehearsal: ' 災備演習',
  setting_Mongod_path: ' Mongod 路徑',
  setting_SSH_User: ' SSH 用戶名',
  setting_SSH_Port: ' SSH 端口',
  setting_hint_Mongod_path: ' Mongod 路徑',
  setting_hint_SSH_User: ' SSH 用戶名, 用來連接Mongod的主機',
  setting_hint_SSH_Port: ' SSH 端口,用來連接Mongod的主機',
  setting_Enable_DR_Rehearsal: ' 允許災備演習',
  setting_hint_Enable_DR_Rehearsal: ' 災備演習開關,true表示開,false 表示關',
  setting_Download_Agent_Page: 'Agent 下載頁面',
  setting_Background_Analytics: '後台分析',
  setting_Data_quality_analysis_frequency: '數據質量分析間隔(秒)',
  setting_Dashboard_data_analysis_frequency: '面板數據分析間隔(秒)',
  setting_dashboard_Analysis_Interval: '面板數據分析間隔(秒)',
  setting_quality_Analysis_Interval: '數據質量分析間隔(秒)',
  setting_Log_filter_interval: '日誌過濾間隔(秒)',
  setting_Filter_the_interval_between_duplicate_logs__seconds__:
    '相同日誌在指定時間內只出現一次（1分鐘後生效）',
  setting__DK36: '文件下載',
  setting_File_Down_Base_Url: '地址',
  setting_Set_the_average_number_of_events_per_second_to_allow:
    '日誌設置每秒允許的事件平均數量',
  setting_Log_Filter_Rate: '日誌輸出頻率(行/秒)',
  setting_Connections: '連接設置',
  setting_Mongodb_Load_Schema_Sample_Size: 'Mongodb加載模型採樣記錄數(行)',
  setting_hint_Mongodb_Load_Schema_Sample_Size:
    '當MongoDB連接加載模型時，會使用該配置進行採樣加載',
  setting_Enable_API_Stats_Batch_Report: ' 啟用 API 統計',
  setting_Header: ' UDP 頭信息',
  setting_hint_Header: ' UDP 頭信息',
  setting_Size_Of_Trigger_API_Stats_Report: ' API 請求緩存最大個數',
  setting_hint_Size_Of_Trigger_API_Stats_Report:
    ' API 請求記錄數到達指定個數時批量發送到管理端',
  setting_Time_Span_Of_Trigger_API_Stats_Report: ' API 請求匯報頻率(秒)',
  setting_hint_Time_Span_Of_Trigger_API_Stats_Report:
    ' API 請求緩存到指定時間發送到管理端',
  setting_save: ' 保存成功，一分鐘後生效',
  setting_Logout_forward_to_this_url: ' 登出跳轉地址',
  setting_Check_devices: ' 重要設備檢測',
  setting_ops: ' 運維展示',
  setting_server_oversee_url: ' 運維運控URL',
  setting_system: ' 系統全局',
  setting_licenseNoticeDays: ' license 到期提醒',
  setting_license_alarm: ' license 到期提前提醒（天）',
  setting_License_expiry_email_reminder_: 'license 到期提前幾天提醒設置',
  setting_flow_engine_version: ' 流程引擎版本',
  setting_tapdata_agent_version: `${import.meta.env.VUE_APP_PAGE_TITLE}  agent版本`,
  setting_doc_base_url: ' 幫助文檔URL',
  setting_help: ' 幫助文檔',
  setting_Ip_addresses: ' Ipv4地址(多個逗號分隔)',
  setting_hint_Ip_addresses:
    ' 需要檢測的設備ipv4地址, 例如: 127.0.0.1, ***********',
  setting_PingTimeout: ' 檢測超時(毫秒)',
  setting_hint_PingTimeout: ' 當超過該設置，認為設備無法連通',
  setting_Job_field_replacement: ' 非法字符替換為',
  setting_A_replacement_for_the_invalid_field_name:
    ' 一些數據庫對於字段名稱有特殊要求，系統將非法的字符在同步時自動做替換。 MongoDB[含有".", "$"作為開頭]',
  setting_true__store_log_to_cloud__false__only_store_to_local_log_file_:
    'true：將日誌存儲到雲，false：僅存儲到本地日誌文件。 ',
  setting_When_one_document_may_be_updated_frequently_within_very_short_period_a_few_updates_within_one_second__for_instance___the_change_stream_event_received_by_downstream_processor_may_return_the__fullDocument__that_is_inconsistent_with_the_actual_version_when_the_update_was_applied_to_that_document__To_avoid_this_inconsistency__enable_this_option_to_store_the_full_document_along_with_the_update_operation__This_will_at_the_expense_of_additional_storage_and_degraded_performance_:
    '當一個文檔可能在非常短的時間內頻繁更新（例如，在一秒鐘之內進行幾次更新）時，下游處理器接收到的更改流事件可能會返回與實際版本不一致的“ fullDocument”（與實際版本不一致） 該文件。為避免這種不一致，請啟用此選項以將完整文檔與更新操作一起存儲。這將以增加存儲空間和降低性能為代價。 ',
  setting_the_before_field_contains_a_field_for_each_table_column_and_the_value_that_was_in_that_column_before_the_update_operation_:
    'before字段包含每個表列的字段以及更新操作之前該列中的值。 ',
  setting_Job_heart_timeout: '同步任務心跳超時（毫秒）',
  setting_retry_interval_second: '重試間隔(秒)',
  setting_max_retry_time_minute: '最大重試時間(分鐘)',
  setting_job_cdc_share_mode: '增量同步任務共享模式',
  setting_job_cdc_share_mode_doc:
    '在增量同步階段，會根據日誌採集任務是否可用，自動採用共享模式。影響的數據庫：Oracle',
  setting_job_cdc_share_only: '增量任務強制使用共享模式',
  setting_job_cdc_share_only_doc:
    '當增量同步任務共享模式開啟，並且無法找到一個可共享的日誌，將會停止任務',
  setting_test_email_success: '測試郵件已發送，請登錄接收郵箱查收',
  setting_test_ldap_success: '成功連接Ldap服務',
  setting_test_email_countdown: '操作太頻繁了，請稍後重試',
  setting_email_template_from: '發件人',
  setting_email_template_to: '收件人',
  setting_email_template_subject: '主題',
  setting_job_cdc_record: ' 自動保存增量事件',
  setting_job_cdc_record_doc: ' 自動保存增量事件',
  setting_job_cdc_record_ttl: ' 增量事件保存時長(天)',
  setting_job_cdc_record_ttl_doc: ' 增量事件保存時長(天)',
  setting_lagTime: '增量滯後判定時間(秒)',
  setting_connection_schema_update_hour: '數據源schema更新時間',
  setting_connection_schema_update_interval: '數據源schema更新周期（天）',
  setting_creatDuplicateSource: ' 允許創建重複數據源',
  setting_requestFailed: '請求處理失敗',
  setting_Mongodb_will_use_this_sample_size_when_load_schema:
    'Mongodb will use this sample size when load schema 當MongoDB連接加載模型時，會使用該配置進行採樣加載',
  setting_Switch_to_batch_insert_mode_interval__s__in_cdc_:
    '切換到cdc中的批量插入模式間隔。 ',
  setting_share_cdc: '共享增量',
  setting_share_cdc_persistence_mode: '共享增量存儲模式',
  setting_share_cdc_persistence_memory_size: '共享增量內存緩存行數',
  setting_share_cdc_persistence_memory_size_doc:
    '該配置控製共享增量事件，在內存緩存的行數',
  setting_share_cdc_persistence_mode_doc:
    '共享增量存儲模式。選項: InMemory, MongoDB, RocksDB',
  setting_share_cdc_persistence_mongodb_uri_db: '存儲MongoDB的連接名稱',
  setting_share_cdc_persistence_mongodb_uri_db_doc:
    '該項配置只有模式選擇MongoDB時生效，輸入創建的MongoDB連接名稱即可',
  setting_share_cdc_persistence_mongodb_collection: '存儲MongoDB的表名',
  setting_share_cdc_persistence_mongodb_collection_doc:
    '該項配置只有模式選擇MongoDB時生效，輸入存儲的表名',
  setting_share_cdc_persistence_rocksdb_path: 'RocksDB存儲的本地路徑',
  setting_share_cdc_persistence_rocksdb_path_doc:
    '該項配置只有模式選擇RocksDB時生效，RocksDB存儲的本地路徑',
  setting_task_log_file_save_time: '任務日誌留存時長(天)',
  setting_task_log_file_save_size: '任務日誌留存大小(MB)',
  setting_task_log_file_save_count: '任務日誌留存份數',
  setting_agent_log_file_save_time: '引擎日誌留存時長(天)',
  setting_agent_log_file_save_size: '引擎日誌留存大小(MB)',
  setting_agent_log_file_save_count: '引擎日誌留存份数',
  setting_INCREMENTAL_DELAY_LINE_DATA_COEFFICIENT: '增量延遲係數',
  setting_Login: '登入設置',
  setting_Login_Single_Session: '單次會話登入',
  setting_Login_Single_Session_doc: '開啟後，同一賬號只允許單個會話登入',
  setting_Login_Brief_Tips: '登錄簡要提示',
  setting_Login_Brief_Tips_doc: '開啟後，登錄提示將簡化',
  setting_LDAP: 'LDAP登入設置',
  setting_Ldap_Login_Enable: '使用LDAP登入',
  setting_Ldap_Server_Host: 'LDAP伺服器地址',
  setting_Ldap_Server_Port: 'LDAP伺服器端口',
  setting_Ldap_Base_DN: 'LDAP Base DN',
  setting_Ldap_Bind_DN: 'LDAP帳號',
  setting_Ldap_Bind_Password: 'LDAP密碼',
  setting_Ldap_SSL_Enable: '啟用SSL',
  setting_Ldap_Server_Host_doc:
    'AD的域控制器地址，範例：ldap://ad.example.com 或 ldaps://ad.example.com',
  setting_Ldap_Server_Port_doc:
    'LDAP預設使用 389 端口，LDAPS（加密連接）使用 636 端口',
  setting_Ldap_Base_DN_doc:
    'LDAP查詢的起點，用于定義在AD中的搜索範圍，多個組用分號間隔，示例：cn=Users,dc=example,dc=com;cn=Test,dc=example,dc=com',
  setting_Ldap_Bind_DN_doc:
    '用於進行身份驗證的用戶完整Distinguished Name (DN)，即登入AD伺服器的身份，範例：<EMAIL>',
  setting_Ldap_Bind_Password_doc: '與Bind DN對應的用戶密碼，用於身份驗證',
  user_list_user_name_email: '請輸入用戶名 / 郵箱',
  user_list_change_time: ' 修改時間',
  user_list_creat_user: '創建用戶',
  user_list_edit_user: '編輯用戶',
  user_list_user_name: '用戶名',
  user_list_role: '關聯角色',
  user_list_source: '來源',
  user_list_status: '狀態',
  user_list_activation: '激活',
  user_list_freeze: '凍結',
  user_list_check: '校驗',
  user_list_bulk_activation: '批量激活',
  user_list_bulk_freeze: '批量凍結',
  user_list_bulk_check: '批量校驗',
  user_list_del_user: '刪除用戶 {0} 後，此用戶將無法恢復',
  user_list_activetion_user: `激活用戶 {0} 後，此用戶將可以使用 ${import.meta.env.VUE_APP_PAGE_TITLE} 系統`,
  user_list_freeze_user: `凍結用戶 {0} 後，此用戶將不可以使用 ${import.meta.env.VUE_APP_PAGE_TITLE} 系統`,
  user_list_check_user: '通過校驗用戶 {0} 的郵箱後，此用戶可以被激活',
  user_list_activetion_success: '激活成功',
  user_list_activetion_error: '激活失敗',
  user_list_freeze_success: '凍結成功',
  user_list_freeze_error: '凍結失敗',
  user_list_check_success: '通過校驗',
  user_list_check_error: '校驗失敗',
  user_status_notVerified: '未驗證',
  user_status_notActivated: '未激活',
  user_status_activated: '已激活',
  user_status_rejected: '已拒絕',
  user_form_role: '關聯角色',
  user_form_email: '郵箱',
  user_form_email_must_valid: '請輸入有效郵箱地址',
  user_form_password_null: '請輸入密碼, 長度為 5 ~ 32 個字符',
  user_form_pass_hint: '密碼長度不能小於5大於32',
  user_form_password_not_cn: '密碼僅允許英文、數字和英文標點符號',
  user_form_activation_code: '訪問碼',
  user_form_status: '狀態',
  cluster_name: '監控名稱',
  cluster_status: '狀態',
  cluster_service_status: '服務狀態',
  cluster_cpu_usage: 'CPU使用率',
  cluster_heap_memory_usage: '堆內存使用率',
  cluster_update: '更新',
  cluster_running: '運行中',
  cluster_stopped: '已停止',
  cluster_sync_gover: '同步治理',
  cluster_manage_sys: '管理後台',
  instance_details_shujuyuanziyuan: '資料源資源下載',
  instance_details_xianchengziyuanxia: '同步治理執行緒資源下載',
  cluster_add_server_mon: '添加服務監控',
  cluster_agentSetting: 'Agent 服務器設置',
  cluster_server_name: '服務器名稱',
  cluster_placeholder_mon_server: '請輸入監控的服務名稱',
  cluster_placeholder_command: '請輸入命令',
  cluster_ip_display: '網卡IP展示',
  cluster_ip_tip: '切換網卡僅改變集群管理頁服務器下IP的展示，不影響任何功能',
  cluster_confirm_text: '確認',
  cluster_restart_server: '重啟服務',
  cluster_unbind_server: '解綁服務',
  cluster_start_server: '啟動服務',
  cluster_startup_after_add: '請啟動後添加',
  cluster_startup_after_delete: '請啟動後刪除',
  cluster_del_message: '確定刪除服務器',
  cluster_server_nickname: '服務器名稱',
  cluster_command: '命令',
  license_node_name: '節點名',
  license_node_sid: '節點sid',
  license_status: 'License狀態',
  license_expire_date: 'License到期時間',
  license_update_time: 'License更新時間',
  license_renew_dialog: '更新License',
  license_normal: '正常',
  license_expiring: '即將到期',
  license_expired: '已過期',
  license_try_out: '試用',
  license_copied_clipboard: '已復製到剪貼板',
  license_select_node: '請先選擇節點',
  license_renew_success: '更新成功, 頁面即將刷新',
  // 自定义节点
  custom_node_name: '節點名稱',
  custom_node_name_placeholder: '請輸入節點名稱搜索',
  notify_setting: '通知設置',
  notify_system_notice: '系統通知',
  notify_user_notice: '用戶通知',
  notify_view_more: '查看全部',
  notify_no_notice: '暫無通知',
  notify_view_all_notify: '查看所有通知',
  notify_user_all_notice: '全部通知',
  notify_unread_notice: '未讀消息',
  notify_mask_read: '標記本頁為已讀',
  notify_mask_read_all: '標記全部為已讀',
  notify_data_flow: '任務',
  notify_sync: '數據開發',
  notify_migration: '數據複製',
  notify_notice_type: '消息類型',
  notify_notice_level: '消息級別',
  notify_manage_sever: '管理端',
  notify_inspect: '校驗任務',
  notify_ddl_deal: 'DDL處理',
  notify_source_name: '源連接',
  notify_database_name: '數據庫名',
  notify_system: 'license到期時間',
  notify_started: '已啟動',
  notify_paused: '已暫停',
  notify_edited: '被編輯',
  notify_deleted: '被刪除',
  notify_abnormally_stopped: '意外停止',
  notify_stopped_by_error: '出錯停止',
  notify_startup_failed: '啟動失敗',
  notify_stop_failed: '停止失敗',
  notify_encounter_error_skipped: '運行中跳過一個ERROR',
  notify_cdc_lag: 'CDC滯後超時',
  notify_manage_sever_restart_failed: '管理端服務重啟失敗',
  notify_api_sever_restart_failed: 'API服務重啟失敗',
  notify_sync_sever_restart_failed: '同步治理服務重啟失敗',
  notify_connection_interrupted: '斷開連接',
  notify_manage_sever_start_failed: '管理端服務啟動失敗',
  notify_api_sever_start_failed: 'API服務啟動失敗',
  notify_sync_sever_start_failed: '同步治理服務啟動失敗',
  notify_manage_sever_stop_failed: '管理端服務停止失敗',
  notify_api_sever_stop_failed: 'API服務停止失敗',
  notify_sync_sever_stop_failed: '同步治理服務停止失敗',
  notify_api_sever_abnormally_stopped: 'API服務意外停止',
  notify_sync_sever_abnormally_stopped: '同步治理服務意外停止',
  notify_manage_sever_abnormally_Stopped: '管理端服務意外停止',
  notify_manage_sever_started_successfully: '管理端服務已啟動',
  notify_api_sever_started_successfully: 'API服務已啟動',
  notify_sync_sever_started_successfully: '同步治理服務已啟動',
  notify_manage_sever_Stopped_successfully: '管理端服務已停止',
  notify_api_sever_stopped_successfully: 'API服務已停止',
  notify_sync_sever_stopped_successfully: '同步治理服務已停止',
  notify_manage_sever_restarted_successfully: '管理端服務已重啟',
  notify_api_sever_restarted_successfully: 'API服務已重啟',
  notify_sync_sever_restarted_successfully: '同步治理服務已重啟',
  notify_new_sever_created_successfully: '新服務監控被創建',
  notify_new_sever_deleted_Successfully: '新服務監控被刪除',
  notify_database_ddl_changed: '監測到數據庫DDL變化',
  notify_inspect_verify_job_count: 'Count有差異',
  notify_inspect_verify_job_value: '內容有差異',
  notify_inspect_verify_job_delete: '被刪除',
  notify_inspect_verify_job_error: '運行error',
  notify_approaching: '剩餘 ',
  notify_system_setting: '系統設置',
  notify_tip:
    '此處通知設置為系統全局通知的設置，任務的通知設置的其優先級高於此處的全局通知設置',
  notify_job_operation_notice: '任務運行通知',
  notify_email_notice: '郵件通知',
  notify_job_started: '任務被啟動',
  notify_noticeInterval: '發送間隔',
  notify_operator: '操作人',
  role_list_select_role_name: '請輸入角色名',
  role_list_role_name: '角色名稱',
  role_list_description: '角色描述',
  role_list_associat_users: '關聯用戶',
  role_list_create: '創建角色',
  role_list_edit: '編輯角色',
  role_list_default_role: '默認角色',
  role_list_setting_permissions: '設置權限',
  role_list_delete_remind: '確認刪除角色 {0}',
  role_list_delete_success: '刪除角色成功',
  role_form_yes: '是',
  role_form_no: '否',
  role_form_selectUser: '請選擇用戶名',
  role_form_connected: '已關聯',
  role_form_already_exists: '角色名稱重複',
  role_null: '角色名稱不能為空',
  role_form_description: '角色描述不能为空',
  role_page_Dashboard_menu: '控制台',
  role_page_datasource_menu: '連接管理',
  role_page_Data_SYNC_menu: '數據複製 & 數據開發',
  role_page_Data_verify_menu: '數據校驗',
  role_page_log_collector_menu: '共享挖掘',
  role_page_SYNC_Function_management_menu: '函數管理',
  role_page_custom_node_menu: '自定義節點',
  role_page_shared_cache_menu: '共享緩存',
  role_page_data_search_menu: '數據搜索',
  role_page_data_catalog_menu: '數據目錄',
  role_page_data_quality_menu: '數據質量',
  role_page_data_rules_menu: '數據規則',
  role_page_time_to_live_menu: '數據生命週期',
  role_page_data_lineage_menu: '數據地圖',
  role_page_API_management_menu: 'API發布',
  role_page_API_data_explorer_menu: 'API瀏覽',
  role_page_API_doc_test_menu: 'API測試',
  role_page_API_stats_menu: 'API統計',
  role_page_API_clients_menu: 'API客戶端',
  role_page_API_server_menu: 'API服務器',
  role_page_data_collect_menu: '數據採集(舊版)',
  role_page_schedule_jobs_menu: '調度任務',
  role_page_Cluster_management_menu: '集群管理',
  role_page_agents_menu: '進程管理',
  role_page_user_management_menu: '用戶管理',
  role_page_role_management_menu: '角色管理',
  role_page_system_settings_menu: '系統設置',
  role_page_dictionary_menu: '字典模板管理',
  role_page_Topology_menu: '網絡拓撲',
  role_page_servers_oversee_menu: '運維運控',
  role_all_check: '全選',
  role_module_meun_Dashboard: '瀏覽控制台',
  role_module_meun_datasource: '連接管理',
  role_module_meun_Data_SYNC: '數據複製 & 數據開發',
  role_module_meun_SYNC_Function_management: '函數管理',
  role_module_meun_Data_verify: '數據校驗',
  role_module_meun_log_collector: '共享挖掘',
  role_module_meun_custom_node: '自定義節點',
  role_module_meun_shared_cache: '共享緩存',
  role_module_meun_data_search: '數據搜索',
  role_module_meun_data_government: '數據治理分類',
  role_module_meun_data_catalog: '數據目錄',
  role_module_meun_data_quality: '數據質量',
  role_module_meun_data_rules: '數據規則',
  role_module_meun_time_to_live: '數據生命週期',
  role_module_meun_data_lineage: '數據地圖',
  role_module_meun_API_management: 'API發布',
  role_module_meun_API_data_explorer: 'API瀏覽',
  role_module_meun_API_doc_test: 'API測試',
  role_module_meun_API_stats: 'API統計',
  role_module_meun_API_clients: 'API客戶端',
  role_module_meun_API_server: 'API服務器',
  role_module_meun_data_collect: '數據採集(舊版)',
  role_module_meun_schedule_jobs: '調度任務',
  role_module_meun_Cluster_management: '集群管理',
  role_module_meun_agents: '進程管理',
  role_module_meun_dictionary: '字典模板',
  role_module_meun_user_management: '用戶管理',
  role_module_meun_role_management: '角色管理',
  role_module_meun_system_settings: '系統設置',
  role_name_Dashboard: '瀏覽控制台',
  role_name_system_notice: '消息通知',
  role_name_notice_settings: '消息通知設置',
  role_name_account_operation_history: '操作歷史',
  role_name_datasource: '瀏覽連接管理',
  role_name_datasource_category_management: '連接管理分類管理',
  role_name_datasource_category_application: '連接管理分類應用',
  role_name_datasource_creation: '連接管理創建',
  role_name_datasource_delete: '連接管理刪除',
  role_name_datasource_edition: '連接管理編輯',
  role_name_data_transmission: '數據管道',
  role_name_Data_SYNC: '瀏覽複製開發任務',
  role_name_SYNC_category_management: '任務分類管理',
  role_name_SYNC_category_application: '任務分類應用',
  role_name_SYNC_job_delete: '刪除任務',
  role_name_SYNC_job_edition: '編輯任務',
  role_name_SYNC_job_operation: '任務操作',
  role_name_SYNC_job_import: '任務導入',
  role_name_SYNC_job_export: '任務導出',
  role_name_SYNC_Function_management: '瀏覽函管理',
  role_name_Data_verify: '瀏覽數據校驗',
  role_name_verify_job_creation: '創建校驗任務',
  role_name_verify_job_edition: '編輯執行校驗任務',
  role_name_verify_job_delete: '刪除校驗任務',
  role_name_verify_job_execution: '校驗任務運行',
  role_name_log_collector: '浏览共享挖掘',
  role_name_custom_node: '浏览自定义节点',
  role_name_shared_cache: '浏览共享缓存',
  role_name_data_search: '浏览数据搜索',
  role_name_data_government: '數據治理',
  role_name_data_catalog: '瀏覽數據目錄',
  role_name_data_catalog_category_management: '數據目錄分類管理',
  role_name_data_catalog_category_application: '數據目錄分類應用',
  role_name_data_catalog_edition: '編輯元數據',
  role_name_new_model_creation: '創建模型',
  role_name_meta_data_deleting: '元數據刪除',
  role_name_data_quality: '瀏覽數據質量',
  role_name_data_quality_edition: '編輯數據質量',
  role_name_data_rules: '瀏覽數據規則',
  role_name_data_rule_management: '數據規則管理',
  role_name_time_to_live: '瀏覽數據生命週期',
  role_name_time_to_live_management: '數據生命週期管理',
  role_name_data_lineage: '瀏覽數據地圖',
  role_name_data_publish: '瀏覽數據發布',
  role_name_API_management: '瀏覽API發布',
  role_name_API_category_application: 'API分類應用',
  role_name_API_category_management: 'API分類管理',
  role_name_API_creation: 'API創建',
  role_name_API_delete: 'API刪除',
  role_name_API_edition: 'API編輯',
  role_name_API_publish: '發布API',
  role_name_API_import: 'API導入',
  role_name_API_export: 'API導出',
  role_name_API_data_explorer: '瀏覽API數據瀏覽',
  role_name_API_data_explorer_export: '導出API數據',
  role_name_API_data_explorer_deleting: '刪除API數據',
  role_name_API_data_explorer_tagging: 'API數據加標籤',
  role_name_API_data_time_zone_editing: '修改時區',
  role_name_API_data_creation: '新增API數據',
  role_name_API_data_download: '下載API數據',
  role_name_API_doc_test: '瀏覽API文檔測試',
  role_name_API_stats: '瀏覽API統計分析',
  role_name_API_clients: '瀏覽API客戶端',
  role_name_API_clients_amangement: 'API客戶端管理',
  role_name_API_server: '瀏覽API服務器',
  role_name_API_server_management: 'API服務器管理',
  role_name_data_collect: '瀏覽數據採集舊版',
  role_name_data_collect_all_data: '數據採集舊版',
  role_name_system_management: '瀏覽系統管理',
  role_name_schedule_jobs: '瀏覽調度任務',
  role_name_schedule_jobs_management: '調度任務管理',
  role_name_Cluster_management: '瀏覽集群管理',
  role_name_Cluster_operation: '操作Agent服務',
  role_name_status_log: '狀態日誌',
  role_name_agents: '瀏覽進程管理',
  role_name_user_management: '瀏覽用戶管理',
  role_name_user_creation: '創建用戶',
  role_name_user_edition: '編輯用戶',
  role_name_user_delete: '刪除用戶',
  role_name_user_category_management: '用戶分類管理',
  role_name_user_category_application: '用戶分類應用',
  role_name_role_management: '瀏覽角色管理',
  role_name_role_creation: '創建角色',
  role_name_role_edition: '編輯角色',
  role_name_role_delete: '刪除角色',
  role_name_system_settings: '瀏覽系統設置',
  role_name_system_settings_modification: '修改設置',
  role_name_create_new_table_in_SYNC: '任務中創建表',
  role_name_servers_oversee: '瀏覽運維',
  role_name_dictionary: '瀏覽字典模板管理',
  role_name_Topology: '瀏覽網絡拓撲',
  milestone_list_status_waiting: '待執行',
  signin_code: '發送驗證碼',
  signin_verify_code: '請輸入驗證碼',
  signin_verify_email_invalid: '請輸入有效郵箱地址',
  signin_verify_code_success: '驗證碼發送成功',
  signin_email_require: '郵箱地址必填',
  signin_verify_code_not_empty: '验证码必填',
  signin_verify_code_not_incorrect: '验证码错误',
  signin_verify_password_invalid: '驗證碼至少5個字符',
  signin_verify_password_notCN: '密碼僅允許英文、數字和英文標點符號',
  signin_not_mailbox: 'oops~此郵箱尚未註冊',
  meta_table_default: '默認值',
  meta_table_not_null: '非空',
  new_advanced_mode: '標準模式',
  new_more_features: '更多功能',
  new_data_copy: '數據複製',
  new_data_development: '數據開發',
  new_data_copy_desc:
    '對數據庫進行跨庫複製，適用於數據遷移，容災備份，系統多活等場景',
  new_data_development_desc:
    '抽取源端數據並加工計算轉換、例如行過濾、字段處理、多表合併等',
  new_create_connection: '創建數據源',
  new_create_api: '創建API',
  new_create_connection_desc:
    '數據源是創建傳輸任務的前提，任務重所有的數據庫和表數據節點都來自數據源',
  new_create_api_desc:
    'API即數據發布，可以根據現有collection或者通過同步任務創建新的collection對外發布的API',
  object_list_name: '對象名稱',
  object_list_classification: '對象分類',
  object_list_type: '對像類型',
  object_list_source_type: '來源類型',
  object_list_source_information: '來源信息',
  datadiscovery_catalogue_ziyuanbangding: '資源綁定',
  datadiscovery_catalogue_lianjieduixiangming: '連接對象名',
  datadiscovery_catalogue_ziyuanleixing: '資源類型',
  datadiscovery_objectlist_duixiangminglaiyuan: '對象名稱/數據源',
  datadiscovery_objectlist_laiyuanfenlei: '來源分類',
  datadiscovery_previewdrawer_shujuxiang: '數據項',
  datadiscovery_previewdrawer_yewumingcheng: '業務名稱',
  datadiscovery_previewdrawer_lianjiemiaoshu: '連接描述',
  datadiscovery_previewdrawer_shujuliang: '數據量',
  datadiscovery_previewdrawer_biangengshijian: '變更時間',
  datadiscovery_previewdrawer_guanliyuan: '管理員',
  datadiscovery_previewdrawer_duixiangxiangqing: '對象詳情',
  datadiscovery_previewdrawer_yewumiaoshu: '業務描述',
  datadiscovery_previewdrawer_yewuleixing: '業務類型',
  datadiscovery_previewdrawer_suoyin: '索引',
  datadiscovery_previewdrawer_waijian: '外鍵',
  datadiscovery_previewdrawer_zhujian: '主鍵',
  // web-core
  app_document: '幫助文檔',
  app_qa: '客服',
  app_account: '個人設置',
  app_version: '系統版本',
  app_home: '官網',
  app_signOut: '登出',
  app_signOutMsg: '您確定要登出嗎？',
  app_customerService_technicalSupport: '技術支持',
  app_customerService_technicalSupportText: '在使用過程中，有任何問題，請在',
  app_customerService_technicalSupportText1:
    '留言，（用戶支持的賬戶和密碼，與cloud.tapdata.net中的相同），我們會盡快答复。',
  app_customerService_userSupport: '用戶支持',
  app_customerService_otherDmands: '其他需求',
  app_customerService_otherDmandsText: '其他需求，請掃描下方企業微信二維碼。',
  app_signIn_slogan: '像自來水一樣方便地使用您的數據',
  app_signIn_signIn: '登錄',
  app_signIn_keepSignIn: '保持登錄狀態',
  app_signIn_email_placeholder: '請輸入郵箱',
  login_email_and_ad_placeholder: '請輸入郵箱/LDAP 用戶名',
  app_signIn_inviteCode_placeholder: '邀请码',
  app_signIn_password_placeholder: '請輸入密碼',
  app_signIn_email_require: '郵箱地址必填',
  app_signIn_inviteCode_require: '邀请码必填',
  app_signIn_email_invalid: '郵箱或密碼錯誤，請檢查後重新輸入',
  app_signIn_inviteCode_invalid: '邀请码无效',
  app_signIn_password_invalid: '密碼至少5個字符',
  app_signIn_permission_denied: '沒有權限',
  app_signIn_registry: '賬號註冊',
  app_signIn_registry_tip: '我已同意',
  app_signIn_userPplicy: '用戶政策',
  app_signIn_nextStep: '下一步',
  app_signIn_haveAccpunt: '已有賬號?',
  app_signIn_backLogin: '返回登錄',
  app_signIn_email_existed: 'Email 地址已被註冊',
  app_signIn_userPplicy_message: '請選擇用戶政策',
  app_signIn_modifyPassword: '修改密碼',
  app_signIn_newPasswordTip:
    '輸入您註冊的郵箱和新密碼，我們將向您發送用於重置密碼的鏈接',
  app_signIn_newpassword_placeholder: '請設置新密碼',
  app_signIn_rememberPasswords: '想起密碼?',
  app_signIn_Registration: '註冊賬號',
  app_signIn_forgetPassword: '忘記密碼?',
  app_signIn_confirmationEmail: '賬號註冊確認郵件已發送至',
  app_signIn_mailbox: '請登錄郵箱後點擊鏈接進行確認~',
  app_signIn_receiveEmail: '沒有收到郵件？點擊',
  app_signIn_resend: '重新發送',
  app_signIn_orClick: '或點擊',
  app_signIn_accountSuccess: '已註冊成功~',
  app_signIn_clickBtn: '點擊下方按鈕開啟數據傳輸之旅吧',
  app_signIn_resetClickBtn: '點擊下方按鈕登錄吧',
  app_signIn_goLogin: '去登錄',
  app_signIn_connectionFailed: '註冊確認鏈接失敗',
  app_signIn_resetConnectionFailed: '重置密碼確認鏈接已失效',
  app_signIn_confirmEmail: '請重新',
  app_signIn_registered: '註冊',
  app_signIn_resetAccountSuccess: '的密碼已重置成功~',
  app_signIn_passwordResetText: '重置密碼確認郵件已發送至',
  app_signIn_hasMailbox: 'oops~此郵箱已經被註冊了',
  app_signIn_disableSignup: 'oops〜禁止註冊',
  app_signIn_getCode: '邀請碼獲取',
  app_signIn_qrCodeText:
    '如果想試用產品, 請掃描下方企業微信二維碼, 聯繫我們獲取',
  app_Home_initialization: '初始化中',
  app_Home_loadingFinished: '初始化完成',
  app_Home_incremental: '增量中',
  app_Home_incrementalLag: '增量滯後',
  message_cancel: '取 消',
  message_confirm: '確定',
  message_save: '保 存',
  message_clickRelatedTasks: '點擊查看相關任務',
  message_noRelatedTask: '暫無相關任務',
  cluster_start: '啟動',
  cluster_close: '關閉',
  cluster_restart: '重啟',
  cluster_syncGover: '同步治理',
  cluster_delete: '刪除',
  cluster_edit: '編輯',
  cluster_cancel: '取 消',
  cluster_confirm: '確 定',
  cluster_reduction: '還原',
  cluster_confirmText: '確認',
  cluster_time: '時間',
  cluster_saveOK: '保存成功',
  cluster_saveFail: '保存失敗',
  cluster_deleteOK: '刪除成功',
  cluster_deleteFail: '刪除失敗',
  cluster_closeSever: '關閉服務',
  cluster_restartServer: '重啟服務',
  cluster_startServer: '啟動服務',
  cluster_deleteOrNot: '是否刪除',
  cluster_selectTime: '選擇時間',
  cluster_selectDate: '選擇日期',
  cluster_serviceCluMange: '服務集群管理',
  cluster_statusLog: '狀態日誌',
  cluster_placeholderServer: '請輸入服務器名稱',
  cluster_manageSys: '管理後台',
  cluster_addServerMon: '添加服務監控',
  cluster_serverName: '服務器名稱',
  cluster_placeholderMonServer: '請輸入監控的服務名稱',
  cluster_iPDisplay: '網卡IP展示',
  cluster_ipTip: '切換網卡僅改變集群管理頁服務器下IP的展示，不影響任何功能',
  cluster_delTittle: '刪除Agent服務器',
  cluster_delMessage: '確定刪除服務器',
  cluster_startupAfter_add: '請啟動後添加',
  cluster_startupAfter_delete: '請啟動後刪除',
  cluster_noneText: '不能為空',
  cluster_hostName: '主機名',
  cluster_ipAddress: 'ip地址',
  cluster_uniqueEncode: '唯一編碼',
  cluster_logs: '日誌信息',
  cluster_serviceType: '服務類型',
  cluster_level: '級別',
  cluster_cpuUsage: 'CPU使用率',
  cluster_heapMemoryUsage: '堆內存使用率',
  button_rename: '改名',
  button_all: '全部',
  dataFlow_leave: '離開',
  dataFlow_backlistText: '返回列表頁',
  dataFlow_saveReminder:
    '此任務尚未保存，離開本頁面會導致任務配置丟失，確定要離開嗎?',
  dataFlow_saveFail: '任務保存失敗，請檢查配置信息並確保數據源狀態有效',
  dataFlow_aggregateNotDataNode: '連接聚合節點的第一個目標數據節點只能是數據集',
  dataFlow_batchSortOperation: '批量分類操作',
  dataFlow_selectRowdata: '請選擇行數據',
  dataFlow_clusterClone: '數據庫遷移',
  dataFlow_custom: '數據同步',
  dataFlow_searchNode: '查找節點',
  dataFlow_updateModel: '更新模型',
  dataFlow_loadingText: '加載中',
  dataFlow_databseMigrationHead: '數據庫遷移 -  新手引導模式',
  dataFlow_dataMigrationHead: '數據同步',
  dataFlow_databseProcessingHead: '數據處理同步',
  dataFlow_databseFreedomHead: '數據庫遷移',
  dataFlow_createNew: '新建',
  dataFlow_DissedNoAction: 'oops~ 被禁用的節點或連線不能被刪除、連入或連出',
  dataFlow_notCopy: '被禁用的節點不能被複製',
  dataFlow_guidingMode: '引導模式',
  dataFlow_advancedMode: '標準模式',
  dataFlow_freedomMode: '轉標準模式',
  dataFlow_advanceSetting: '更多高級設置',
  dataFlow_closeSetting: '收起',
  dataFlow_openPanel: '展開',
  dataFlow_execution: '開始執行',
  dataFlow_previous: '上一步',
  dataFlow_next: '下一步',
  dataFlow_sourceSetting: '設置源庫',
  dataFlow_targetSetting: '設置目標庫',
  dataFlow_advancedetting: '高級設置',
  dataFlow_simpleSceneTitle: '創建數據庫複製任務',
  dataFlow_sourceLibrarySetting: '源庫結構與對象設置',
  dataFlow_databseMigration:
    '以引導的模式幫助新手用戶快速了解數據庫之間的遷移。數據庫遷移能快速地實現數據庫之間(內置表批量過濾和改名等設置)的結構、全量和增量遷移',
  dataFlow_databsenProcessing:
    '以引導的模式幫助新手用戶快速了解表級的數據處理與同步，此功能除了能實現表級的全量或增量傳輸除功能外，更註重使用各種處理器(JS處理、字段過濾、聚合處理、行級過濾等)進行復雜的邏輯處理，以滿足用戶更高的數據處理需求',
  dataFlow_databseFreedom:
    '數據遷移功能可幫助用戶在壹個任務內輕松實現多個同構或異構數據庫、文件之間的結構遷移、初始化遷移、或增量遷移等功能。',
  dataFlow_dataFreedom:
    '數據同步聚焦在表級別的數據處理與傳輸，可實現多表合並、數據拆分、關聯映射、字段增減合並、內容過濾、聚合處理、JS處理等功能的實時數據同步。',
  dataFlow_moreFeatures: '更多功能',
  dataFlow_creatSource: '創建數據源',
  dataFlow_creatApi: '創建API',
  dataFlow_dataValidation: '數據校驗',
  dataFlow_sourceDescription:
    '數據源是創建傳輸任務的前提，任務中所有的數據庫和表等數據節點都來自數據源。數據源包含數據庫, File, GridFS, Rest API, View, Udp, Custom connection等',
  dataFlow_apiDescription:
    'API即數據發布，可以根據現有collection或者通過同步任務創建新的collection對外發布的API',
  dataFlow_datavaliDescription:
    '數據校驗可對遷移同步任務的數據源與目標之間的數據進行比對校驗，校驗功能包含快速count校驗，全表字段值校驗，關聯字段值校驗，定時自動校驗等。 ',
  dataFlow_multiError_allSelectionError: '選中的任務狀態不允許這種操作',
  dataFlow_multiError_notFound: '此任務不存在',
  dataFlow_multiError_statusError: '任務狀態不允許這種操作',
  dataFlow_multiError_otherError: '操作失敗，請重試',
  dataFlow_changeName: '改名',
  dataFlow_Enable: '啟用',
  dataFlow_Disable: '禁用',
  dataFlow_draftNotStart: '任務配置未完成，無法啟動',
  dataFlow_systemHint: '系統提示',
  dataFlow_systemText: '系統檢測出有如下任務上次操作後未保存，請問是否繼續編輯',
  dataFlow_stystemOpen: '打開',
  dataFlow_stystemOpenAll: '全部打開',
  dataFlow_stystemDeleteAll: '全部刪除',
  dataFlow_stystemLgnoreAll: '全部忽略',
  dataFlow_newTaksName: '新任務',
  dataFlow_selectNode: '請選擇節點',
  dataFlow_submitExecute: '提交執行',
  dataFlow_submitOnly: '僅提交',
  dataFlow_implementationModalities: '執行方式',
  dataFlow_submitConfirmation: '提交確認',
  dataFlow_SyncPoint: '增量採集開始時刻',
  dataFlow_cdcLabel: '數據源:',
  dataFlow_syncType: '任務類型',
  dataFlow_belongAgent: '所屬Agent',
  dataFlow_SyncInfo_localTZ:
    '當前時區傳輸時間：系統所在時區下，開始傳輸任務的時刻',
  dataFlow_SyncInfo_current: '當前時區時間：默認當前時間',
  dataFlow_SyncInfo_connTZ:
    '數據庫時區傳輸時間： 數據庫所在時區下，開始傳輸任務的時刻',
  dataFlow_SyncInfo_localTZType: '用戶瀏覽器時區',
  dataFlow_SyncInfo_currentType: '此刻',
  dataFlow_SyncInfo_connTZType: '數據庫時區',
  dataFlow_Current: '當前時間',
  dataFlow_SyncTime: '同步時間',
  dataFlow_batchDelete: '批量刪除',
  dataFlow_batchRest: '批量重置',
  dataFlow_bulkExport: '批量導出',
  dataFlow_bulkImport: '批量導入',
  dataFlow_bulkScheuled: '批量啟動',
  dataFlow_bulkStopping: '批量停止',
  dataFlow_taskBulkFx: '函数',
  dataFlow_taskBulkOperation: '批量操作',
  dataFlow_upload: '點擊上傳',
  dataFlow_chooseFile: '選擇文件',
  dataFlow_import: '任務導入',
  dataFlow_uploadOK: '上傳成功',
  dataFlow_uploadError: '上傳失敗',
  dataFlow_uploadInfo: '點擊查看詳情',
  dataFlow_view: '查看',
  dataFlow_dataFlowExport: '導出',
  dataFlow_addTag: '添加標籤',
  dataFlow_editTag: '編輯標籤',
  dataFlow_overWrite: '覆蓋已有數據',
  dataFlow_skipData: '跳過已有數據',
  dataFlow_loadingError: '加載失敗,請',
  dataFlow_dataLoading: '數據努力加載中...',
  dataFlow_loadLogTip: '運行日誌努力加載中，可能需要5~10秒，請稍等......',
  dataFlow_noLogTip: '沒有數據',
  dataFlow_clickLoadTxt: '點擊加載',
  dataFlow_average: '平均',
  dataFlow_current: '當前',
  dataFlow_allNode: '全部節點',
  dataFlow_taskName: '任務名稱',
  dataFlow_creatdor: '創建人',
  dataFlow_ownedUser: '所屬用戶',
  dataFlow_ownedLibrary: '所屬庫',
  dataFlow_creationTime: '啟動時間',
  dataFlow_state: '狀態',
  dataFlow_executionTime: '本次執行時間',
  dataFlow_finishTime: '本次結束時間',
  dataFlow_inputNumber: '本次輸入',
  dataFlow_outputNumber: '本次輸出',
  dataFlow_sourceLibrary: '源庫',
  dataFlow_targetLibrary: '目標庫',
  dataFlow_rowCount: '條數',
  dataFlow_inputOutput: '輸入輸出統計',
  dataFlow_transf: '傳輸耗時',
  dataFlow_taskDetail: '任務詳情',
  dataFlow_nodeDetail: '節點信息',
  dataFlow_dataScreening: '事件統計',
  dataFlow_timePoint: '增量所處時間點',
  dataFlow_unit: '單位',
  dataFlow_article: '條',
  dataFlow_secondUnit: '秒',
  dataFlow_second: '秒',
  dataFlow_min: '分',
  dataFlow_hour: '時',
  dataFlow_day: '日',
  dataFlow_input: '輸入',
  dataFlow_output: '輸出',
  dataFlow_totalInput: '總輸入',
  dataFlow_totalOutput: '總輸出',
  dataFlow_totalInsert: '總插入',
  dataFlow_totalUpdate: '總更新',
  dataFlow_totalDelete: '總刪除',
  dataFlow_category: '類別',
  dataFlow_replicate: '數據同步差距',
  dataFlow_throughputpop:
    '輸入輸出統計: 平均每秒源端數據採集的速度以及目標端寫入的速度，數值越大越好',
  dataFlow_transtime_pop:
    '傳輸耗時：除源節點外，事件處理完的時間減去事件的發生時間。節點間統計：事件從進入節點到輸出到所消耗的時間。任務流統計：所有節點耗時相加，數值越小越好',
  dataFlow_replicate_pop:
    '數據同步差距: 源庫和目標庫數據最後更新時間的差距，數值越小越好',
  dataFlow_status_paused: '已暫停',
  dataFlow_status_prepare: '准备中',
  dataFlow_status_cdc: '增量中',
  dataFlow_status_initializing: '初始化中',
  dataFlow_status_initialized: '初始化完成',
  dataFlow_status_Lag: '增量滯後',
  dataFlow_status_all: '全部狀態',
  dataFlow_lag: '滯後',
  dataFlow_executionStatus: '執行狀態',
  dataFlow_maxLagTime: '最大增量滯後時間',
  dataFlow_searchPlaceholder: '任務名稱/節點名/庫名稱',
  dataFlow_searchAgent: '實例名稱',
  dataFlow_dataRange: '創建日期範圍',
  dataFlow_startTime: '開始時間',
  dataFlow_endTime: '結束時間',
  dataFlow_separator: '至',
  dataFlow_dataPlaceholder: '選擇時間範圍',
  dataFlow_taskStatus: '任務狀態',
  dataFlow_taskStatusPlaceholder: '請選擇任務狀態',
  dataFlow_taskSettingPlaceholder: '請選擇任務同步類型',
  dataFlow_updateTime: '更新時間',
  dataFlow_runningSpeed: '運行速度',
  dataFlow_taskSwitch: '运行开关',
  dataFlow_operate: '操作',
  dataFlow_dataMap: '數據地圖',
  dataFlow_edit: '編輯',
  dataFlow_copy: '複製',
  dataFlow_schedule: '定時調度',
  dataFlow_run: '啟動任務',
  dataFlow_stop: '停止任務',
  dataFlow_cut: '剪切',
  dataFlow_paste: '粘貼',
  dataFlow_undo: '撤銷',
  dataFlow_redo: '重做',
  dataFlow_selectAll: '全選',
  dataFlow_amplification: '放大',
  dataFlow_zoomOut: '縮小',
  dataFlow_down: '向下',
  dataFlow_up: '向上',
  dataFlow_selectMultipleNode: '選擇多節點',
  dataFlow_mouseDrag: '鼠標拖拽',
  dataFlow_runningMonitor: '運行監控',
  dataFlow_select_source_connection: '源端連接',
  dataFlow_select_sync_mode: '同步方式',
  dataFlow_mapping: '關聯關係',
  dataFlow_select_target_connection: '目標端連接',
  dataFlow_sync_mode: '同步模式',
  dataFlow_sync_type: '同步類型',
  dataFlow_send_email: '發送郵件',
  dataFlow_stopped: ' 當任務停止',
  dataFlow_error: '當任務出錯',
  dataFlow_edited: '當任務被編輯',
  dataFlow_started: '當任務開啟',
  dataFlow_shareCdcMode: '共享增量讀取的模式',
  dataFlow_streaming: '流式讀取',
  dataFlow_polling: '輪詢讀取',
  dataFlow_drop_target_before_start: '開啟任務前是否刪除目標表',
  dataFlow_run_custom_sql: '重複運行自定義SQL',
  dataFlow_stop_on_error: '遇到錯誤停止',
  dataFlow_need_to_create_Index: '自動創建索引',
  dataFlow_transformModelVersion: '系統推演版本',
  dataFlow_noPrimaryKey: '支持無主鍵同步',
  dataFlow_is_schedule: '定期調度任務',
  dataFlow_cron_expression: '調度cron表達式',
  dataFlow_data_quality_tag: '添加數據質量標籤',
  dataFlow_notification_lag: '通知',
  dataFlow_isOpenAutoDDL: '自動處理DDL',
  dataFlow_ddlTip: '注意：自動DDL處理不支持JS處理器，分段處理器',
  dataFlow_transformerConcurrency: '目標寫入線程數',
  dataFlow_processorConcurrency: '處理器線程數',
  dataFlow_cdcEngineFilter: '啟用引擎過濾',
  dataFlow_cdcFetchSize: '增量批次讀取條數',
  dataFlow_cdcFetchSizeTip: '每次讀取的數據條數。',
  dataFlow_cdcFetchSizeTip1: '條數越小，增量實時性高，但處理速度相對較慢。',
  dataFlow_cdcFetchSizeTip2: '條數越多，實時性相對較低，但整體處理速度較快。',
  dataFlow_send_email_when_replication: '幾秒後重新發送',
  dataFlow_send_email_at_most_one_replication: '超過多少秒取消發送',
  dataFlow_read_cdc_interval: '增量同步間隔',
  dataFlow_cdc_concurrency: '增量同步並發寫入',
  dataFlow_cdcShareFilterOnServer: '共享挖掘日誌過濾',
  dataFlow_read_batch_size: '每次讀取數量',
  dataFlow_cdcDataProcess: '增量數據處理機制',
  dataFlow_batch: '批量',
  dataFlow_onebyone: '逐條',
  dataFlow_mission: '描述',
  dataFlow_yes: 'yes',
  dataFlow_no: 'no',
  dataFlow_cronExpression: '請輸入調度表達式',
  dataFlow_selectGrpupFiled: '請選擇分組字段',
  dataFlow_selectTargetField: '請選擇目標字段',
  dataFlow_aggName: '子處理名稱',
  dataFlow_nodeName: '節點名稱',
  dataFlow_nodeType: '節點類型',
  dataFlow_aggFunction: '聚合函數',
  dataFlow_aggExpression: '作用目標',
  dataFlow_filterPredicate: '過濾器',
  dataFlow_groupByExpression: '分組字段',
  dataFlow_keepAggreHistoryData: '保留聚合歷史數據',
  dataFlow_aggregation: '聚合處理',
  dataFlow_aggrCleanSecond: '清理舊版本數據時間',
  dataFlow_aggrFullSyncSecond: '全量同步時間',
  dataFlow_enterFilterTable: '請輸入過濾表內容',
  dataFlow_lagTime: '增量滯後判斷時間設定',
  dataFlow_lagTimeTip:
    '當增量任務延遲大於該值時，則認為任務增量滯後，預設值為0',
  dataFlow_aggregatePrompt:
    '提示：使用聚合處理節點後，此任務停止後再次啟動，任務將會重置',
  dataFlow_nameTip:
    '後續節點的腳本編輯需要引用此子處理的名稱進行指定的數據處理，故不同的子處理名稱不可重複。 ',
  dataFlow_button_submit: '提交執行',
  dataFlow_button_viewConfig: '查看節點配置',
  dataFlow_button_viewMonitoring: '查看監控數據',
  dataFlow_button_setting: '設置',
  dataFlow_button_logs: '運行日誌',
  dataFlow_button_milestone: '任務里程碑',
  dataFlow_button_preview: '預覽',
  dataFlow_button_capture: '數據檢視',
  dataFlow_button_stop_capture: '停止檢視',
  dataFlow_button_start: '啟動',
  dataFlow_button_stop: '停止',
  dataFlow_button_force_stop: '強制停止',
  dataFlow_button_reset: '重置',
  dataFlow_button_save: '保存',
  dataFlow_button_saveDraft: '保存草稿',
  dataFlow_button_saveing: '保存中',
  dataFlow_button_reloadSchema: '刷新schema',
  dataFlow_button_debug: 'debug測試',
  dataFlow_button_quantitative: '定量',
  dataFlow_button_increment: '增量',
  dataFlow_save_before_running: '請先保存再運行',
  dataFlow_reset_job_msg: '重置任務?',
  dataFlow_reset_job_tip: '提示',
  dataFlow_stop_job_msg: '停止任務?',
  dataFlow_stop_job_force_stop_msg: '強制停止任務?',
  dataFlow_stop_job_tip: '提示',
  dataFlow_file_preview_fields_file_name: '文件名稱',
  dataFlow_file_preview_fields_file_size_ondisk: '文件大小(Byte)',
  dataFlow_file_preview_fields_file_modify_time_ondisk: '更新時間',
  dataFlow_file_preview_fields_file_create_time_ondisk: '創建時間',
  dataFlow_file_preview_fields_file_path: '文件路徑',
  dataFlow_delete_confirm_title: '是否刪除該任務？',
  dataFlow_delete_confirm_message: '刪除任務 xxx 後，此任務將無法恢復',
  dataFlow_bulk_delete_confirm_title: '是否批量刪除任務？',
  dataFlow_bulk_delete_confirm_message: '批量刪除任務後，任務將無法恢復',
  dataFlow_stop_confirm_title: '是否暫停該任務？',
  dataFlow_stop_confirm_message:
    '暫停任務 xxx 後，任務中未完成全量同步的表再次啟動時，會重新執行全量同步',
  dataFlow_bulk_stop_confirm_title: '是否批量暫停任務？',
  dataFlow_bulk_stop_confirm_message:
    '批量暫停任務後，任務中未完成全量同步的表再次啟動時，會重新執行全量同步',
  dataFlow_force_stop_confirm_title: '是否強制停止該任務？',
  dataFlow_force_stop_confirm_message:
    '強制停止任務 xxx 將立即中斷數據傳輸強制任務快速停止，並重置該任務',
  dataFlow_bulk_force_stop_confirm_title: '是否批量強制停止任務？',
  dataFlow_bulk_force_stop_confirm_message:
    '批量強制停止任務將立即中斷數據傳輸強制任務快速停止，並重置該任務',
  dataFlow_initialize_confirm_title: '是否重置該任務？',
  dataFlow_initialize_confirm_message:
    '重置任務 xxx 將清除任務同步進度，任務將重新執行',
  dataFlow_bulk_initialize_confirm_title: '是否批量重置任務？',
  dataFlow_bulk_initialize_confirm_message:
    '批量重置任務將清除任務同步進度，任務將重新執行',
  dataFlow_importantReminder: '重要提醒',
  dataFlow_modifyEditText: '編輯任務如果修改了',
  dataFlow_nodeLayoutProcess: '節點排版流程',
  dataFlow_nodeAttributes: '節點屬性',
  dataFlow_matchingRelationship: '匹配關係',
  dataFlow_afterSubmission: '提交後必須',
  dataFlow_runNomally: '才能正常運行',
  dataFlow_editLayerTip: ' 否則可能導致異常錯誤，請問您要繼續編輯嗎?',
  dataFlow_continueEditing: '繼續編輯',
  dataFlow_numberType: '必須為數位且不能小於0',
  dataFlow_setting_distinctWriteType: '去重寫入機制',
  dataFlow_setting_intellect: '智能去重寫入',
  dataFlow_setting_compel: '強制去重寫入',
  dataFlow_setting_intellectTip:
    '智能去重寫入：對目標已有數據進行智能檢測，去重的同時能極大提升傳輸性能',
  dataFlow_setting_compelTip:
    '強制去重寫入：對目標已有數據進行強制去重檢測，嚴格保證精準度但傳輸性能較低',
  dataFlow_setting_batchTip:
    '批量：對監測到的增量數據進行批量傳輸處理，性能較高',
  dataFlow_setting_onebyoneTip:
    '逐行：對監測到的增量數據進行逐條處理，性能較差',
  dataFlow_setting_sync_type_tip:
    '關閉數據集節點的聚合設置才能修改傳輸類型，已開啟節點:',
  dataFlow_skipError_title: '跳過錯誤設置',
  dataFlow_skipError_skipErrorSettings: '任務錯誤處理',
  dataFlow_skipError_tip:
    '任務上次停止時發生了以下數據相關的錯誤，請確認這些錯誤已經被處理。如果希望跳過這些錯誤，請勾選相應的錯誤項並點擊“跳過錯誤，啟動任務” 。 ',
  dataFlow_skipError_attention:
    '注意：若導致錯誤的數​​據未被處理，跳過錯誤可能導致這條數據被丟棄。 ',
  dataFlow_skipError_startJob: '跳過錯誤，啟動任務',
  dataFlow_skipError_cancel: '取消',
  dataFlow_skipError_taskName: '任務名',
  dataFlow_skipError_errorTotal: '共XX條，已選擇',
  dataFlow_skipError_strip: '條',
  dataFlow_flowEngineVersion: '引擎版本',
  dataFlow_flowEngineV1: 'Flow Engine V1',
  dataFlow_jetFlowEngineV2: 'Jet Flow Engine V2',
  editor_cell_data_node_collection_form_collection_placeholder: '請選擇數據集',
  editor_cell_data_node_collection_form_fieldFilterType_retainedField:
    '保留字段',
  editor_cell_data_node_collection_form_fieldFilterType_deleteField: '刪除字段',
  editor_cell_data_node_collection_form_fieldFilter_placeholderKeep:
    ' 請選擇要保留的字段',
  editor_cell_data_node_collection_form_fieldFilter_placeholderDelete:
    ' 請選擇要刪除的字段',
  editor_cell_data_node_collection_form_filter_fieldFilter: '智慧模式',
  editor_cell_data_node_collection_form_filter_sqlFilter: 'SQL模式',
  editor_cell_data_node_collection_form_filter_mqlFilter: 'MQL模式',
  editor_cell_data_node_collection_form_filter_allField: '全部字段',
  editor_cell_data_node_collection_form_filter_rowLimit: '行數限制',
  editor_cell_data_node_collection_form_filter_allRows: '全部行數',
  editor_cell_data_node_collection_form_filter_oneThousandRows: '1000行',
  editor_cell_data_node_collection_form_filter_tenThousandRows: '10000行',
  editor_cell_data_node_table_form_custom_sql_placeholder: '請輸入自定義SQL',
  editor_cell_data_node_table_form_custom_sql_mplaceholder: '請輸入自定義MQL',
  editor_cell_data_node_table_form_initial_offset_label: '自定義SQL增量條件',
  editor_cell_data_node_table_form_initial_offset_placeholder:
    '請輸入自定義SQL增量條件',
  editor_ui_sidebar_setting: '任務設置',
  metadata_createModel: '創建模型',
  metadata_header_name: '表名/所屬庫',
  metadata_header_last_user_name: '更新用戶',
  metadata_metaType_database: '數據庫',
  metadata_metaType_api: '數據種類',
  metadata_metaType_job: '任務',
  metadata_metaType_collection: '數據集',
  metadata_metaType_view: '視圖',
  metadata_metaType_directory: '目錄',
  metadata_metaType_table: '數據表',
  metadata_metaType_dataflow: '任務編排',
  metadata_metaType_mongo_view: 'Mongodb視圖',
  metadata_metaType_ftp: 'FTP',
  metadata_metaType_apiendpoint: 'API連接',
  metadata_details_model: '模型',
  metadata_details_collection: '數據集',
  metadata_details_collectionName: '數據集名稱',
  metadata_details_createCollection: '創建數據集',
  metadata_details_dataDirectory: '數據目錄',
  metadata_details_dataDetails: '數據詳情',
  metadata_details_basicAttributes: '基礎屬性',
  metadata_details_businessAttributes: '業務屬性',
  metadata_details_clickAddDes: '點擊添加描述',
  metadata_details_propertyDetails: '屬性詳情',
  metadata_details_comment: '描述',
  metadata_details_originalTableName: '原表名',
  metadata_details_data_type: '數據類型',
  metadata_details_precision: '精確度',
  metadata_details_columnSize: '字段長度',
  metadata_details_scale: '數字長度',
  metadata_details_autoincrement: '自增',
  metadata_details_primary_key_position: '主鍵',
  metadata_details_foreign_key_position: '外鍵',
  metadata_details_is_nullable: '非空',
  metadata_details_unique: '唯一',
  metadata_details_owningConnection: '所屬連接',
  metadata_details_primaryKey: '主鍵',
  metadata_details_source: '來源',
  metadata_details_founder: '創建人',
  metadata_details_Modifier: '修改人',
  metadata_details_renamed: '改名',
  metadata_details_searchPlaceholder: '字段名/別名/描述',
  metadata_details_selsectSource: '選擇來源',
  metadata_details_createFiled: '新建字段',
  metadata_details_editFild: '編輯字段',
  metadata_details_prohibitOverwriting: ' 批量禁止覆蓋',
  metadata_details_batchCoverage: '批量覆蓋',
  metadata_details_refreshModel: '刷新模型',
  metadata_details_filedName: '字段名',
  metadata_details_alias: '別名',
  metadata_details_fieldType: '字段類型',
  metadata_details_allowOverwrite: '允許覆蓋',
  metadata_details_selfIncreasing: '自增',
  metadata_details_fieldLength: '字段長度',
  metadata_details_accuracy: '精準度',
  metadata_details_numberLength: '數字長度',
  metadata_details_dictionarySettings: '字典設置',
  metadata_details_initialValue: '初始值',
  metadata_details_mappedValue: '映射值',
  metadata_details_enterInitialValue: '輸入初始值',
  metadata_details_enterMappedValue: '輸入映射值',
  metadata_details_newMapping: '新增映射',
  metadata_details_chooseTemplate: '選擇模板',
  metadata_details_foreignKeySetting: '外鍵設置',
  metadata_details_associationTable: '關聯表',
  metadata_details_associationField: '關聯字段',
  metadata_details_connectionRelation: '關聯關係',
  metadata_details_oneone: '一對一',
  metadata_details_onemany: '一對多',
  metadata_details_manyone: '多對一',
  metadata_details_addRelatedTable: '新增關聯表',
  metadata_details_filedAliasName: '字段名/別名',
  metadata_details_Float: '浮點數',
  metadata_details_String: '字符串',
  metadata_details_baseObject: '對象',
  metadata_details_Array: '數組',
  metadata_details_Map: '字典對象',
  metadata_details_Short: '短整型',
  metadata_details_Long: '長整型',
  metadata_details_Double: '雙精度',
  metadata_details_Byte: '字節',
  metadata_details_Bytes: '字節數',
  metadata_details_BigDecimal: '十進制',
  metadata_details_Boolean: '布爾值',
  metadata_details_Date: '日期',
  metadata_details_Integer: '整數',
  metadata_details_dictionary_typeNo: '此字段類型不能添加字典模板',
  metadata_details_fieldNameNo: '字段名為空',
  metadata_details_moreAttributes: '更多屬性',
  metadata_details_msgFiledName: '請輸入字段名稱',
  metadata_details_success_Release: '保存成功,請手動重新發布',
  metadata_details_filedName_repeat: '字段名不能重名',
  metadata_details_filedDictionary: '字段字典',
  metadata_details_foreignKeyAssociation: '外鍵關聯',
  metadata_details_tableLayering: '表分層',
  metadata_details_theme: '主題',
  metadata_details_taskReference: '任務引用',
  metadata_details_APIReference: 'API引用',
  metadata_details_creat: '新建',
  metadata_details_businessAttrTitle: '業務屬性',
  metadata_details_attrName: '屬性名',
  metadata_details_attrKey: '屬性值',
  metadata_details_editAliasNameTitle: '編輯別名',
  metadata_details_editCommentTitle: '編輯描述',
  metadata_details_uniquelyIdentifies: '唯一標識',
  metadata_details_query: '查詢',
  metadata_details_version_version_control: '版本管理',
  metadata_details_version_version_control_required: '版本管理不能為空',
  metadata_details_version_lastVersion:
    '此元數據已是最新版本，過往保存的歷史版本記錄將保存在下面列表中',
  metadata_details_version_versionNum: '版本號',
  metadata_details_version_versionComparison: '版本比對',
  metadata_details_version_compared: '對比',
  metadata_details_version_currentVersion: '當前版本',
  metadata_details_version_operator: '操作人',
  metadata_details_version_modifyDescription: '修改說明',
  metadata_details_Modify_property: '修改屬性',
  metadata_details_Modify_field: '修改字段',
  metadata_details_Add_property: '新增屬性',
  metadata_details_Add_new_field: '新增字段',
  metadata_details_Remove_property: '移除屬性',
  metadata_details_Remove_field: '移除字段',
  metadata_details_index_title: '索引',
  metadata_details_index_name: '索引名稱',
  metadata_details_index_create: '創建索引',
  metadata_details_index_fields: '時間字段',
  metadata_details_index_unique: '唯一約束',
  metadata_details_index_status: '狀態',
  metadata_details_index_create_by: '創建用戶',
  metadata_details_index_background: '後台',
  metadata_details_index_properties: '屬性',
  metadata_details_index_definition: '字段名稱',
  metadata_details_index_options: '選項',
  metadata_details_index_build_in_background: '在後台構建索引',
  metadata_details_index_create_unique: '創建唯一索引',
  metadata_details_index_create_ttl: '創建TTL索引',
  metadata_details_index_name_exists: '索引名稱必須唯一',
  metadata_details_index_index_exists: '索引已經存在',
  metadata_details_index_create_by_user: ' 平台用戶',
  metadata_details_index_create_by_dba: ' 數據庫管理員',
  metadata_details_index_status_creating: ' 正在創建',
  metadata_details_index_status_created: ' 創建完成',
  metadata_details_index_status_creation_failed: ' 創建失敗',
  metadata_details_index_status_deleted: ' 已經刪除',
  metadata_details_index_drop_index: ' 正在刪除索引',
  metadata_details_index_unique_true: ' 唯一',
  metadata_details_index_unique_false: ' 不唯一',
  metadata_details_validation_title: '數據驗證',
  metadata_details_validation_field_name: '字段名稱',
  metadata_details_validation_rule: '規則',
  metadata_details_validation_ruleTem: '規則模板',
  metadata_details_validation_select_rule: '選擇規則',
  metadata_details_validation_ungrouped: '未分組',
  metadata_details_validation_create: '創建數據校驗',
  metadata_details_preview_title: '數據預覽',
  metadata_details_pipeline_title: '管道',
  metadata_details_pipeline_collection: '數據表',
  metadata_details_pipeline_pipeline: 'MongoDB Pipeline',
  metadata_details_pipeline_viewStatus: '視圖狀態',
  metadata_details_pipeline_FailedMessage: '失敗詳情',
  metadata_details_pipeline_penpinSave:
    '點擊下方保存按鈕僅保存到系統，點擊更新按鈕將應用到此數據所在的數據庫',
  metadata_details_pipeline_apply: '應用',
  metadata_details_pipeline_view_tip: '操作將覆蓋同名的視圖，是否創建視圖',
  metadata_details_pipeline_success: '應用成功',
  metadata_details_pipeline_failed: '應用失敗',
  metadata_metadataSearch_title: '元數據檢索',
  metadata_metadataSearch_desc:
    '元數據檢索提供對錶、字段的名稱、別名、描述等內容的搜索功能，請先選擇搜索表/字段，再輸入內容，點擊搜索按鈕進行搜索',
  metadata_metadataSearch_table: '搜索表',
  metadata_metadataSearch_column: '搜索字段',
  metadata_metadataSearch_search: '搜索',
  metadata_metadataSearch_noSearch: '請按“回車”鍵發起檢索',
  metadata_metadataSearch_noResult: '暫無搜索結果，請確認搜索關鍵字',
  metadata_metadataSearch_noMore: '無更多檢索結果',
  metadata_metadataSearch_more: '點擊加載更多',
  metadata_metadataSearch_placeholder: '請輸入搜索關鍵字',
  notification_stoppedByError: '出錯停止',
  notification_CDCLag: 'CDC滯後超時',
  notification_jobPaused: '任務被停止',
  notification_jobDeleted: '任務被刪除',
  notification_jobStateError: '任務狀態error',
  notification_jobEncounterError: '任務遇到錯誤',
  notification_noticeInterval: '發送間隔',
  notification_CDCLagTime: 'CDC滯後通知',
  notification_lagTime: '滯後時間',
  notification_DDL: '數據庫DDL變化',
  notification_agentNotice: 'Agent通知',
  notification_serverDisconnected: '服務器斷開連接',
  notification_agentStarted: 'Agent服務被啟動',
  notification_agentStopped: 'Agent服務被停止',
  notification_agentCreated: 'Agent被創建',
  notification_agentDeleted: 'Agent被刪除',
  notification_inspectCount: '校驗任務count差異',
  notification_inspectValue: '校驗任務内容差異',
  notification_inspectDelete: '校驗任務被刪除',
  notification_inspectError: '校驗任務運行error',
  notification_placeholder_user: '請選擇操作人',
  notification_placeholder_keyword: '按數據源/任務名搜索',
  notification_account: '用戶',
  notification_operation_create: '創建了',
  notification_operation_update: '更新了',
  notification_operation_delete: '刪除了',
  notification_operation_start: '啟動了',
  notification_operation_stop: '停止了',
  notification_operation_forceStop: '強制停止了',
  notification_operation_reset: '重置了',
  notification_operation_copy: '複製了',
  notification_operation_upload: ' 導入了 ',
  notification_operation_download: '下載了',
  notification_operation_login: '登錄',
  notification_operation_logout: '登出',
  notification_modular_sync: '同步任務',
  notification_modular_migration: '遷移任務',
  notification_modular_connection: '數據源',
  notification_modular_dataflow: '數據傳輸任務 ',
  notification_modular_inspect: '校驗任務',
  notification_modular_ddlDeal: 'DDL處理 ',
  notification_modular_system: '系統',
  notification_modular_user: '用戶',
  notification_modular_role: '角色',
  notification_modular_accessCode: '訪問碼',
  notification_operation_readnotification_modular_message: '',
  dialog_placeholderTable:
    '僅支持英文、數字、下劃線、點、減號，並以英文字母開頭，不允許 system 開頭',
  dialog_downAgent_ok: '好的',
  queryBuilder_addCond: '字段條件',
  account_accountSettings: '個人設置 ',
  account_systemSetting: '系統設置',
  account_email: '郵箱',
  account_userName: '用戶名',
  account_accessCode: '訪問碼',
  account_changePassword: '修改密碼',
  account_currentPassword: '請輸入當前密碼',
  account_newPassword: '請輸入新密碼',
  account_confirmPassword: '再次確認密碼',
  account_changeEmail: '修改郵箱',
  account_enterMailbox: '請輸入郵箱',
  account_enterNewMailbox: '請輸入新郵箱',
  account_changeUsername: '修改用戶名',
  account_newUsername: '請輸入新的用戶名',
  account_sendEmail: '發送驗證郵件',
  account_samePawTip: '新密碼不能與原密碼相同!',
  account_newPawInconsistent: '與新密碼不一致!',
  account_pawSaveSuccess: '密碼保存成功',
  account_currerPawErrorTip: '當前密碼錯誤，請輸入正確的密碼',
  account_nameModifySuccess: '名稱修改成功',
  account_passwordNotCN: '密碼僅允許英文、數字和英文標點符號',
  account_user_null: '用戶名不能為空',
  account_has_username: '用戶名已存在',
  account_editFail: '用戶名修改失敗',
  role_allData: '全部角色數據',
  role_functionDataPermission: '功能與數據權限',
  role_module: '模塊',
  role_choosePermissionTip:
    '請選擇此角色可用的功能和數據權限 （勾選全部角色數據表示可對全部角色的數據進行瀏覽或操作，不勾選則表示只能對自己的數據進行瀏覽或操作）',
  role_funcPermission: '功能權限',
  role_currentRole: '當前角色',
  role_pageVisible: '頁面權限',
  role_pageShowTip: '請勾選此角色可見的頁面，不勾選表示頁面不顯示',
  role_choosePage: '選擇頁面',
  role_bulkOperate: '批量操作',
  role_chooseAllFunction: '選擇全部功能',
  role_chooseAllRole: '全部角色數據',
  role_settingTitle: '設置角色權限',
  user_creatUser: '創建用戶',
  timeToLive_w: '週',
  timeToLive_mo: '月',
  timeToLive_y: '年',
  dictionary_isMappedvalue: '映射值不能為空',
  dataRule_classification: '分類',
  dataRule_rule: '規則',
  dataRule_data_type: '字段類型',
  dataRule_data_Nullable: '可為空',
  dataRule_data_Range: '範圍',
  dataRule_data_Enum: '枚舉',
  dataRule_data_Regex: '正則表達式',
  dataRule_greater_that: '大於',
  dataRule_less_that: '小於',
  dataRule_enum_required: '枚舉值不能為空',
  dataRule_gt_lt_none: '範圍邊界不能同時為',
  dataRule_data_type_required: '數據類型不能為空',
  dataRule_data_regex_required: '正則表達式不能為空',
  dataRule_correct_rules: '請輸入正確規則',
  dataRule_pleaseNum: '請輸入數字',
  dataRule_dataType_baseFloating: '浮點數',
  dataRule_dataType_baseObject: '對象',
  dataRule_dataType_baseBinarydata: '二進制數據',
  dataRule_dataType_baseString: '字符串',
  dataRule_dataType_baseArray: '數組',
  dataRule_dataType_baseUndefined: '未定義',
  dataRule_dataType_baseBoolean: '布爾值',
  dataRule_dataType_basedate: '日期',
  dataRule_dataType_baseNull: '空',
  dataRule_dataType_baseRegularexpression: '正則表達式',
  dataRule_dataType_baseShorttype: '短整型',
  dataRule_dataType_baseTimestamp: '時間戳',
  dataRule_dataType_baseLonginteger: '長整型',
  daas_notification_alarmnotification_gaojingtongzhi: '告警通知',
  daas_notification_center_xitonggaojing: '系統告警',
  daas_notification_systemalarm_guanbichenggong: '關閉成功',
  daas_notification_systemalarm_gaojingshijian: '告警時間',
  daas_notification_systemalarm_gaojingzhuangtai: '告警狀態',
  daas_notification_systemalarm_gaojingduixiang: '告警對象',
  daas_notification_systemalarm_quanbugaojing: '全部告警',
  daas_setting_alarmnotification_gaojingzhibiao: '告警指標',
  daas_setting_alarmnotification_dangjiediandeping: '當節點的平均處理耗時',
  daas_setting_alarmnotification_dangshujuyuanjie: '當數據源節點的平均處理耗時',
  daas_setting_alarmnotification_dangshujuyuanxie: '當數據源協議連接耗時',
  daas_setting_alarmnotification_dangshujuyuanwang: '當數據源網络連接耗時',
  daas_setting_alarmnotification_dangshujuwufa: '當數據無法網络連接耗時',
  daas_setting_alarmnotification_dangrenwudezeng: '當任務的增量延遲',
  daas_setting_alarmnotification_dangrenwutingzhi: '當任務停止時',
  daas_setting_alarmnotification_dangrenwuzengliang: '當任務增量开始時',
  daas_setting_alarmnotification_dangrenwuquanliang: '當任務全量完成時',
  daas_setting_alarmnotification_dangrenwujiaoyan: '當任務校驗出錯時',
  daas_setting_alarmnotification_dangrenwuyudao: '當任務遇到錯誤時',
  daas_setting_alarmnotification_dangrenwustop: 'Agent服務停止時',
  daas_setting_alarmnotification_msshigaojing: 'ms時告警',
  daas_setting_alarmnotification_gedian: '個點',
  daas_setting_alarmnotification_lianxu: '連續',
  daas_setting_alarmnotification_cichugaojinggui:
    '此處告警規則設置為系統全局告警規則設置，任務運行監控頁面的告警規則設置優先級高於系統全局設置',
  daas_setting_alarmnotification_renwumorengao: '任務默認告警規則設置',
  daas_setting_alarmnotification_morengaojinggui: '默認告警規則',
  daas_setting_alarmnotification_renwugaojingshe: '任務告警設置',
  daas_setting_setting_chulijiediande: '處理節點的平均處理耗時',
  daas_setting_setting_shujuyuanjiedian: '數據源節點的平均處理耗時',
  daas_setting_setting_shujuyuanxieyi: '數據源協議連接耗時',
  daas_setting_setting_shujuyuanwanglu: '數據源網路連接耗時',
  daas_setting_setting_renwudezengliang: '任務的增量延遲',
  daas_setting_settingcenter_gaojingshezhi: '告警設置',
  packages_nodeDesign_custom_node_name_required: '請輸入節點名稱',
  packages_nodeDesign_message_save_ok: '保存成功',
  share_detail_title: '挖掘表詳情',
  shared_cache_messge_no_table: '找不對所選表模型',
  shared_cache_placeholder_max_memory: '請輸入緩存最大內存',
  shared_cache_placeholder_external_storage: '請選擇外存配置',
  setting_newMongodbChangeStream: '111111',
  notify_schema_name: '模型名',
  dataFlow_atabseProcessingHead: '數據處理同步',
  dataFlow_databseProcessing:
    '以引導的模式幫助新手用戶快速了解表級的數據處理與同步，此功能除了能實現表級的全量或增量傳輸除功能外，更注重使用各種處理器(JS處理、字段過濾、聚合處理、行級過濾等)進行複雜的邏輯處理，以滿足用戶更高的數據處理需求。',
  modules_apiServerStatus: 'API 服務狀態',
  modules_describtion: '描述',
  modules_set_mode: '設置方式',
  role_name_API_clients_amanement: 'API客戶端管理',
  dataFlow_sharecdcmode: '共享增量閱讀模式',
  dataFlow_delete_confirm_Title: '刪除任務？',
  dataFlow_delete_confirm_Message: '刪除任務XXX後，該任務無法恢復',
  dataFlow_bulk_delete_confirm_Title: '刪除中的任務批量？',
  dataFlow_bulk_delete_confirm_Message: '批量刪除任務後，任務無法恢復',
  connection_form_agent_msg: 'Agent當前狀態異常，無法創建連接，請檢查',
  migrate_select_connection_tip:
    '如果您還沒有添加數據源，請點擊添加數據源按鈕添加。為了方便您測試，我們建議您使用數據源數量至少要2',
  app_signIn_signInFail: '郵箱和密碼無效',
  dialog_jobSchedule_runDay: '每天2點運行',
  guide_task_type_custom_tips:
    '數據同步側重於表級數據處理和傳輸，以滿足用戶需要實現多表（數據集）、多級數據之間的多表集成、數據拆分、關聯映射、字段增減合併、內容過濾、實時數據同步聚合處理案例JS處理等功能，在不影響用戶業務的情況下，滿足用戶對遠程或本地數據容災、跨實例數據同步、查詢報表分發、實時性等多種業務場景的需求數據倉庫管理員元素。 ',
  guide_btn_to_dashboard: '暫不編輯任務，先去購物',
  app_signIn_registry_sucess_wait_approval:
    '登錄前等待管理員批准，5秒後跳轉到登錄頁面',
  daas_deletefile_emptyitem_zanwushuju: '暫無數據',
  daas_components_querycond_xuanzeriqishi: '選擇日期時間',
  daas_src_main_qingqiuquanjupei: '請求全局配置(settings)失敗: ',
  daas_src_main_baocuntok: '保存token到cookie：',
  daas_api_page_apidocandtest_shouquanjiekou: '授權接口',
  daas_api_page_apidocandtest_daochudaopo: '導出到postman',
  daas_data_discovery_previewdrawer_qingshurumingcheng: '請輸入名稱',
  daas_data_server_drawer_qingshurucanshu: '請輸入參數名稱',
  daas_data_server_drawer_paixu: '排序',
  daas_data_server_drawer_meigefenyefan: '每個分頁返回的記錄數',
  daas_data_server_drawer_fenyebianhao: '分頁編號',
  daas_data_server_drawer_zidingyichaxun: '自定義查詢',
  daas_data_server_drawer_morenchaxun: '默認查詢',
  daas_data_server_drawer_qingxuanzeduixiang: '請選擇對象名稱',
  daas_data_server_drawer_qingxuanzelianjie: '請選擇連接類型',
  daas_data_server_drawer_qingshurufuwu: '請輸入服務名稱',
  daas_data_server_drawer_quanxianfanwei: '權限範圍',
  daas_data_server_drawer_selectPermissions: '請選擇權限範圍',
  daas_data_server_drawer_shilidaima: '示例代碼',
  daas_data_server_drawer_shilidaima2: '示例代碼',
  daas_data_server_drawer_fanhuijieguo: '返回結果',
  daas_data_server_drawer_diaoyongfangshi: '調用方式',
  daas_data_server_drawer_fuwufangwen: '服務訪問',
  daas_data_server_drawer_shuchujieguo: '輸出結果',
  daas_data_server_drawer_pailietiaojian: '排列條件',
  daas_data_server_drawer_shaixuantiaojian: '篩選條件',
  daas_data_server_drawer_canshuzhi: '參數值',
  daas_data_server_drawer_canshumingcheng: '參數名稱',
  daas_data_server_drawer_shurucanshu: '輸入參數',
  daas_data_server_drawer_jiekouleixing: '接口類型',
  daas_data_server_drawer_fabujiedian: '發布節點',
  daas_data_server_drawer_caozuoleixing: '操作類型',
  daas_data_server_drawer_zanwumiaoshu: '暫無描述',
  daas_data_server_drawer_tiaoshi: '調試',
  daas_data_server_drawer_peizhi: '配置',
  daas_data_server_drawer_chuangjianfuwu: '創建服務',
  daas_data_server_drawer_fuwuxiangqing: '服務詳情',
  daas_data_server_list_quedingchexiaogai: '確定撤銷該服務？',
  daas_data_server_list_quedingfabugai: '確定發布該服務？',
  daas_data_server_list_querenshanchufu: '確認刪除服務？',
  daas_data_server_list_huoqufuwuyu: '獲取服務域名失敗！',
  daas_data_server_list_fuwuzhuangtai: '服務狀態',
  daas_data_server_list_guanlianduixiang: '關聯對象',
  daas_data_server_list_fuwumingcheng: '服務名稱',
  daas_function_importform_shangchuanwenjianda: '上傳文件大小不能超過 {val1}M',
  daas_login_login_dengluchenggong: '登錄成功：',
  daas_login_passwordreset_shangweiyanzhengdian: '尚未驗證電子郵件',
  daas_login_passwordreset_zhaobudaodianzi: '找不到電子郵件',
  daas_metadata_search_yuanbiaoming: '( 原表名:',
  daas_shared_mining_detail_wajuexiangqingx: '挖掘詳情x軸：',
  daas_data_discovery_previewdrawer_jiedian: '節點',
  daas_data_discovery_previewdrawer_renwumiaoshu: '任務描述',
  daas_data_discovery_previewdrawer_yinqingmiaoshu: '引擎描述',
  daas_data_discovery_previewdrawer_yinqingmingcheng: '引擎名稱',
  daas_data_discovery_previewdrawer_jiedianshu: '節點數',
  daas_data_discovery_previewdrawer_shuchucanshu: '輸出參數',
  daas_data_discovery_previewdrawer_fuwumiaoshu: '服務描述',
  daas_data_discovery_previewdrawer_jiedianmiaoshu: '節點描述',
  daas_data_discovery_previewdrawer_shurujiedian: '输入节点',
  daas_data_discovery_previewdrawer_shuchujiedian: '输出节点',
  daas_router_routes_guanlianrenwuxiang: '關聯任務詳情',
  daas_data_server_drawer_geshicuowu: '格式錯誤',
  daas_data_server_drawer_validate:
    '只能包含中文、字母、數字、下劃線和美元符號,並且數字不能開頭',
  daas_data_server_drawer_aPI_path_Settings: '訪問路徑設置',
  daas_data_server_drawer_default_path: '默認訪問路徑',
  daas_data_server_drawer_custom_path: '自定義訪問路徑',
  daas_data_server_drawer_prefix: '前綴',
  daas_data_server_drawer_base_path: '基礎路徑',
  daas_data_server_drawer_path: '訪問路徑',
  daas_data_server_drawer_confirm_tip:
    '重新生成會導致原API訪問路徑發生改變，是否確認重新生成？ ',
  daas_notification_center_yonghucaozuo: '用戶操作',
  daas_cluster_cluster_lianjieshuliang: '連接數量',
  daas_cluster_cluster_mubiaoIPhe: '目標IP和端口',
  daas_cluster_cluster_lianjiezongshu: '連接總數',
  daas_cluster_cluster_yinqingduiwaijian: '引擎對外建立的連接數',
  daas_role_role_ninhaiweibaocun: '您還未保存設置的權限，是否要保存權限設置？ ',
  daas_role_role_quanbugongneng: ' 全部功能 ',
  daas_role_role_chakanquanbushu: '查看全部數據',
  daas_role_role_gongnengquanxian: '功能權限',
  daas_role_role_yemianquanxian: '頁面權限',
  daas_role_role_gongnengmokuai: '功能模塊',
  daas_role_role_gouxuanxiangyingmo:
    '勾選相應模塊表示此導航對當前角色下用戶可見，開啟【查看全部數據】則表示角色可以查看和操作該模塊下所有的數據，不勾選則只能查看和操作自己創建和被授權的數據。 ',
  daas_feature_unavailable: '功能暫不可用',
  daas_feature_unavailable_subtitle:
    '這個功能僅在企業版和/或雲端版本中提供。請註冊我們的雲端版本或聯繫我們獲取企業版。',
  daas_feature_unavailable_upgrade_dec: '升級版本，您將獲得：',
  daas_feature_unavailable_upgrade_dec_li1: '數據校驗（僅企業版）',
  daas_feature_unavailable_upgrade_dec_li2: '共享挖掘',
  daas_feature_unavailable_upgrade_dec_li3: '告警設置',
  daas_feature_unavailable_upgrade_dec_li4: '權限管理（僅企業版）',
  daas_feature_unavailable_upgrade_dec_li5: '更多數據源',
  daas_feature_unavailable_upgrade_dec_li1_desc: `基於自研技術，${import.meta.env.VUE_APP_PAGE_TITLE} 能最大程度保障數據一致性，還支持數據表數據校驗，以驗證和確保數據流轉正確，滿足生產環境要求。`,
  daas_feature_unavailable_upgrade_dec_li2_desc: `為減輕源端數據庫壓力，${import.meta.env.VUE_APP_PAGE_TITLE} 支持共享挖掘增量日誌緩存，開啓此功能的任務可直接從緩存中獲取增量事件，無需重復讀取源庫增量日誌。`,
  daas_feature_unavailable_upgrade_dec_li3_desc: `${import.meta.env.VUE_APP_PAGE_TITLE} 支持通過 SMTP 協議發告警郵件，讓用戶在常用郵箱及時接收異常通知，助其感知異常，保障任務運行穩定可靠。`,
  daas_feature_unavailable_upgrade_dec_li4_desc:
    '角色是權限合集，可為其授予多權限並授予用戶，用戶繼承所有權限，依此設計可先創角色再賦予用戶，無需為每個用戶配置權限，以簡化運維管理和提升安全性。',
  daas_feature_unavailable_upgrade_dec_li5_desc: '',
  daas_feature_unavailable_get_enterprise: '申請企業版',
  daas_feature_unavailable_get_cloud: '免費使用雲版',
  daas_feature_unavailable_go_to_compare: '版本功能對比',
  daas_unbind_license: '解綁 License',
  daas_cluster_cluster_view: '集群視圖',
  daas_cluster_component_view: '組件視圖',
  daas_cluster_engine_hostname: '主機名/IP',
  daas_cluster_connection_count: '連接數',
  daas_cluser_keyword_placeholder: '搜索主機名',

  webhook_alerts: 'Webhook 告警',
  webhook_alerts_detail: 'Webhook 告警詳情',
  webhook_alerts_add: '新建 Webhook',
  webhook_address: 'Webhook 地址',
  webhook_params: '參數',
  webhook_switch: '是否啓用',
  webhook_send_log: '發送記錄',
  webhook_send_log_desc: '（只保留最近 200 條記錄）',
  webhook_send_address: '發送地址',
  webhook_server_url: '服務 URL',
  webhook_server_url_empty: '請輸入服務 URL',
  webhook_server_url_error: '請輸入正確的服務 URL',
  webhook_custom_template: '自定義模版',
  webhook_custom_template_tip: `{'{'}
    "action": "TaskAlter",
    "hookId": "\${'{'}hookId{'}'}",
    "actionTime": "\${'{'}actionTime{'}'}",
    "title": "\${'{'}title{'}'}",
    "content": "\${'{'}content{'}'}",
    "actionData": {'{'}
        "status": "\${'{'}actionData.status{'}'}", //標記當前告警的狀態,ING,RECOVER,CLOESE
        "statusTxt":"\${'{'}actionData.statusTxt{'}'}", //標記當前告警的狀態文本:正在進行,已恢復,已關閉
        "level": "\${'{'}actionData.level{'}'}", //告警級別RECOVERY,NORMAL,WARNING,CRITICAL,EMERGENCY,ERROR,WARN,INFO
        "component":"\${'{'}actionData.component{'}'}",//引擎告警組件固定為: FE
        "componentTxt": "\${'{'}actionData.componentTxt{'}'}", //引擎告警組件文本值: 引擎
        "type":"\${'{'}actionData.type{'}'}",//告警類型  SYNCHRONIZATIONTASK_ALARM,SHARED_CACHE_ALARM,SHARED_MINING_ALARM,DATA_VERIFICATION_ALARM,ACCURATE_DELAY_ALARM,INSPECT_ALARM
        "typeTxt": "\${'{'}actionData.typeTxt{'}'}", //告警類型文本值,同步任務告警、共享緩存告警、共享挖掘告警、數據校驗告警、精准延遲告警
        "metric": "\${'{'}actionData.metric{'}'}",//事件類型: TASK_STATUS_STOP, TASK_STATUS_ERROR, TASK_FULL_COMPLETE, TASK_INCREMENT_START, TASK_INSPECT_ERROR, INSPECT_TASK_ERROR, DATANODE_CANNOT_CONNECT, DATANODE_TCP_CONNECT_CONSUME, DATANODE_HTTP_CONNECT_CONSUME, SYSTEM_FLOW_EGINGE_UP, SYSTEM_FLOW_EGINGE_DOWN, DATANODE_AVERAGE_HANDLE_CONSUME, TASK_INCREMENT_DELAY, PROCESSNODE_AVERAGE_HANDLE_CONSUME, INSPECT_COUNT_ERROR, INSPECT_VALUE_ERROR
        "metricTxt": "\${'{'}actionData.metricTxt{'}'}", //事件類型文本值：任務運行停止，任務運行錯誤，任務全量完成，任務增量開始，任務校驗出錯，校驗任務遇到錯誤，數據源無法連接網絡，數據源TCP連接完成，數據源連接網絡完成，引擎上線，引擎離線，數據源節點的平均處理耗時超過閥值，任務的增量延遲超過閥值，節點的平均處理耗時超過閥值，Count校驗結果的差異行數大於閾值，值校驗結果的表數據差大於閾值
        "name":"\${'{'}actionData.name{'}'}",//具體的任務名
        "node":"\${'{'}actionData.node{'}'}",//產生告警的節點名，無節點時為空;當為任務告警時，節點直接放任務名
        "currentValue": "\${'{'}actionData.currentValue{'}'}",//觸發告警的指標值
        "threshold": "\${'{'}actionData.threshold{'}'}",//觸發告警的指標閾值
        "lastOccurrenceTime": "\${'{'}actionData.lastOccurrenceTime{'}'}",//告警最近發生時間
        "tally": "\${'{'}actionData.tally{'}'}",//告警發生次數
        "summary": "\${'{'}actionData.summary{'}'}",//告警內容
        "recoveryTime": "\${'{'}actionData.recoveryTime{'}'}",//告警恢復時間
        "closeTime": "\${'{'}actionData.closeTime{'}'}",//告警關閉時間
        "closeBy": "\${'{'}actionData.closeBy{'}'}",//告警被誰關閉
        "agentId": "\${'{'}actionData.agentId{'}'}", //所屬引擎
    {'}'}
{'}'}`,
  webhook_custom_template_ph:
    '自定義模板內容，支持參數填充模板，如：${alarm.name}',
  http_header: 'HTTP 請求頭',
  http_header_ph: 'HTTP 請求頭,多個請求頭請換行輸入,示例:Accept: text/html',
  webhook_send_ping: '發送測試 PING 事件',
  webhook_event_type: '事件類型',
  webhook_event_type_empty: '請選擇事件',
  daas_licenseType: '授權類型',
  daas_licenseType_pipeline: '通道授權',
  daas_licenseType_lite: 'Lite 版本',
  daas_licenseType_service: '數據服務版',
  daas_licenseType_op: '標準',
  daas_datasourcePipeline: '數據源通道',
  daas_datasourcePipelineLimit: '數據源通道數量',
  daas_datasourcePipeUsageDetails: '通道使用詳情',
  account_accessCode_confirm: '確定刷新訪問碼?',
  account_accessCode_tip:
    '刷新訪問碼將導致當前訪問碼失效，系統將生成新的訪問碼。<b class="color-warning">您需要將新的訪問碼更新到引擎的配置文件後，重新啓動引擎，否則引擎可能會無法正常工作。請謹慎操作！</b>',
  account_accessCode_success: '刷新訪問碼成功',
}
