{"instance": {"hazelcast": {"external-data-store": {"mysql-database": {"class-name": 123, "properties": ["***********************"], "shared": "false"}}}}, "error": {"schemaLocation": "#/definitions/ExternalDataStoreConfig/additionalProperties", "pointerToViolation": "#/hazelcast/external-data-store/mysql-database", "causingExceptions": [{"schemaLocation": "#/definitions/ExternalDataStoreConfig/additionalProperties/properties/class-name", "pointerToViolation": "#/hazelcast/external-data-store/mysql-database/class-name", "causingExceptions": [], "keyword": "type", "message": "expected type: String, found: Integer"}, {"schemaLocation": "#/definitions/ExternalDataStoreConfig/additionalProperties/properties/properties", "pointerToViolation": "#/hazelcast/external-data-store/mysql-database/properties", "causingExceptions": [], "keyword": "type", "message": "expected type: JSONObject, found: JSONArray"}, {"schemaLocation": "#/definitions/ExternalDataStoreConfig/additionalProperties/properties/shared", "pointerToViolation": "#/hazelcast/external-data-store/mysql-database/shared", "causingExceptions": [], "keyword": "type", "message": "expected type: Boolean, found: String"}], "message": "3 schema violations found"}}