{"instance": {"hazelcast": {"hot-restart-persistence": {"enabled": true, "base-dir": "/mnt/hot-restart", "backup-dir": "/mnt/hot-backup", "validation-timeout-seconds": 120, "data-load-timeout-seconds": 900, "cluster-data-recovery-policy": "PARTIAL_RECOVERY_MOST_COMPLETE", "encryption-at-rest": {"enabled": true, "algorithm": "AES/CBC/PKCS5Padding", "salt": "somesalt", "key-size": 16, "secure-store": {"keystore": {"path": "/path/to/keystore.file", "type": "PKCS12", "password": "password", "current-key-alias": "current", "polling-interval": 60}}}}}}, "error": null}