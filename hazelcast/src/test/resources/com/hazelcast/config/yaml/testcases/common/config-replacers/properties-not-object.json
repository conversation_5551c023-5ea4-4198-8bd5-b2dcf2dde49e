{"instance": {"hazelcast": {"config-replacers": {"fail-if-value-missing": false, "replacers": [{"class-name": "com.hazelcast.config.replacer.EncryptionReplacer", "properties": []}]}}}, "error": {"schemaLocation": "#/definitions/ConfigReplacers/properties/replacers/items/properties/properties", "pointerToViolation": "#/hazelcast/config-replacers/replacers/0/properties", "causingExceptions": [], "keyword": "type", "message": "expected type: JSONObject, found: JSONArray"}}