{"instance": {"hazelcast": {"member-attributes": {"attr-name": 2, "attr-1-name": {"type": null}}}}, "error": {"schemaLocation": "#/definitions/MemberAttributes", "pointerToViolation": "#/hazelcast/member-attributes", "causingExceptions": [{"schemaLocation": "#/definitions/MemberAttributes/additionalProperties", "pointerToViolation": "#/hazelcast/member-attributes/attr-name", "causingExceptions": [], "keyword": "type", "message": "expected type: JSONObject, found: Integer"}, {"schemaLocation": "#/definitions/MemberAttributes/additionalProperties", "pointerToViolation": "#/hazelcast/member-attributes/attr-1-name", "causingExceptions": [{"schemaLocation": "#/definitions/MemberAttributes/additionalProperties", "pointerToViolation": "#/hazelcast/member-attributes/attr-1-name", "causingExceptions": [], "keyword": "required", "message": "required key [value] not found"}, {"schemaLocation": "#/definitions/MemberAttributes/additionalProperties/properties/type", "pointerToViolation": "#/hazelcast/member-attributes/attr-1-name/type", "causingExceptions": [], "keyword": "type", "message": "expected type: String, found: Null"}], "message": "2 schema violations found"}], "message": "3 schema violations found"}}