/*
 * Copyright (c) 2008-2022, Hazelcast, Inc. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.hazelcast.test;

/**
 * Constants in milliseconds for use in test annotations such as {@code @Test(timeout = 5 * SECOND)}.
 */
@SuppressWarnings("WeakerAccess")
public final class TimeConstants {

    private TimeConstants() {
    }

    public static final long SECOND = 1000;

    public static final long MINUTE = 60 * SECOND;
}
