/*
 * Copyright (c) 2008-2022, Hazelcast, Inc. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.hazelcast.internal.partition.service.fragment;

import com.hazelcast.spi.impl.operationservice.BackupAwareOperation;
import com.hazelcast.spi.impl.operationservice.Operation;
import com.hazelcast.internal.services.ServiceNamespaceAware;

public class TestFragmentIncrementOperation extends TestAbstractFragmentOperation implements BackupAwareOperation, ServiceNamespaceAware {

    private transient int value;

    public TestFragmentIncrementOperation() {
    }

    public TestFragmentIncrementOperation(String name) {
        super(name);
    }

    @Override
    public void run() throws Exception {
        TestFragmentedMigrationAwareService service = getService();
        value = service.inc(name, getPartitionId());
    }

    @Override
    public boolean shouldBackup() {
        return true;
    }

    @Override
    public int getSyncBackupCount() {
        TestFragmentedMigrationAwareService service = getService();
        return service.backupCount;
    }

    @Override
    public int getAsyncBackupCount() {
        return 0;
    }

    @Override
    public Operation getBackupOperation() {
        return new TestFragmentBackupPutOperation(name, value);
    }

}
