/*
 * Copyright (c) 2008-2022, Hazelcast, Inc. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.hazelcast.jet.retry.impl;

import com.hazelcast.jet.retry.IntervalFunction;
import com.hazelcast.jet.retry.RetryStrategy;

public class RetryStrategyImpl implements RetryStrategy {

    private static final long serialVersionUID = 1L;

    private final int maxAttempts;
    private final IntervalFunction intervalFunction;

    public RetryStrategyImpl(int maxAttempts, IntervalFunction intervalFunction) {
        this.maxAttempts = maxAttempts;
        this.intervalFunction = intervalFunction;
    }

    @Override
    public int getMaxAttempts() {
        return maxAttempts;
    }

    @Override
    public IntervalFunction getIntervalFunction() {
        return intervalFunction;
    }

    @Override
    public String toString() {
        return "max attempts = " + maxAttempts + ", interval function = " + intervalFunction;
    }
}
