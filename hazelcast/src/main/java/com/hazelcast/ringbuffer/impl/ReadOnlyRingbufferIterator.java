/*
 * Copyright (c) 2008-2022, Hazelcast, Inc. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.hazelcast.ringbuffer.impl;

import java.util.Iterator;

/**
 * Read-only iterator over items in a provided {@link com.hazelcast.ringbuffer.impl.Ringbuffer}.
 */
public class ReadOnlyRingbufferIterator<E> implements Iterator<E> {
    private final ArrayRingbuffer<E> ringbuffer;
    private long sequence;

    ReadOnlyRingbufferIterator(ArrayRingbuffer<E> ringbuffer) {
        this.ringbuffer = ringbuffer;
        this.sequence = ringbuffer.headSequence();
    }

    @Override
    public boolean hasNext() {
        return sequence <= ringbuffer.tailSequence();
    }

    @Override
    public E next() {
        return ringbuffer.read(sequence++);
    }

    @Override
    public void remove() {
        throw new UnsupportedOperationException();
    }
}
