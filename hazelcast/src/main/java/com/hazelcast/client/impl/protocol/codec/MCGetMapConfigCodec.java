/*
 * Copyright (c) 2008-2022, Hazelcast, Inc. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.hazelcast.client.impl.protocol.codec;

import com.hazelcast.client.impl.protocol.ClientMessage;
import com.hazelcast.client.impl.protocol.Generated;
import com.hazelcast.client.impl.protocol.codec.builtin.*;
import com.hazelcast.client.impl.protocol.codec.custom.*;

import javax.annotation.Nullable;

import static com.hazelcast.client.impl.protocol.ClientMessage.*;
import static com.hazelcast.client.impl.protocol.codec.builtin.FixedSizeTypesCodec.*;

/*
 * This file is auto-generated by the Hazelcast Client Protocol Code Generator.
 * To change this file, edit the templates or the protocol
 * definitions on the https://github.com/hazelcast/hazelcast-client-protocol
 * and regenerate it.
 */

/**
 * Gets the config of a map on the member it's called on.
 */
@Generated("df9be78e9246c7f4bc73582dd835de30")
public final class MCGetMapConfigCodec {
    //hex: 0x200300
    public static final int REQUEST_MESSAGE_TYPE = 2097920;
    //hex: 0x200301
    public static final int RESPONSE_MESSAGE_TYPE = 2097921;
    private static final int REQUEST_INITIAL_FRAME_SIZE = PARTITION_ID_FIELD_OFFSET + INT_SIZE_IN_BYTES;
    private static final int RESPONSE_IN_MEMORY_FORMAT_FIELD_OFFSET = RESPONSE_BACKUP_ACKS_FIELD_OFFSET + BYTE_SIZE_IN_BYTES;
    private static final int RESPONSE_BACKUP_COUNT_FIELD_OFFSET = RESPONSE_IN_MEMORY_FORMAT_FIELD_OFFSET + INT_SIZE_IN_BYTES;
    private static final int RESPONSE_ASYNC_BACKUP_COUNT_FIELD_OFFSET = RESPONSE_BACKUP_COUNT_FIELD_OFFSET + INT_SIZE_IN_BYTES;
    private static final int RESPONSE_TIME_TO_LIVE_SECONDS_FIELD_OFFSET = RESPONSE_ASYNC_BACKUP_COUNT_FIELD_OFFSET + INT_SIZE_IN_BYTES;
    private static final int RESPONSE_MAX_IDLE_SECONDS_FIELD_OFFSET = RESPONSE_TIME_TO_LIVE_SECONDS_FIELD_OFFSET + INT_SIZE_IN_BYTES;
    private static final int RESPONSE_MAX_SIZE_FIELD_OFFSET = RESPONSE_MAX_IDLE_SECONDS_FIELD_OFFSET + INT_SIZE_IN_BYTES;
    private static final int RESPONSE_MAX_SIZE_POLICY_FIELD_OFFSET = RESPONSE_MAX_SIZE_FIELD_OFFSET + INT_SIZE_IN_BYTES;
    private static final int RESPONSE_READ_BACKUP_DATA_FIELD_OFFSET = RESPONSE_MAX_SIZE_POLICY_FIELD_OFFSET + INT_SIZE_IN_BYTES;
    private static final int RESPONSE_EVICTION_POLICY_FIELD_OFFSET = RESPONSE_READ_BACKUP_DATA_FIELD_OFFSET + BOOLEAN_SIZE_IN_BYTES;
    private static final int RESPONSE_INITIAL_FRAME_SIZE = RESPONSE_EVICTION_POLICY_FIELD_OFFSET + INT_SIZE_IN_BYTES;

    private MCGetMapConfigCodec() {
    }

    public static ClientMessage encodeRequest(java.lang.String mapName) {
        ClientMessage clientMessage = ClientMessage.createForEncode();
        clientMessage.setRetryable(true);
        clientMessage.setOperationName("MC.GetMapConfig");
        ClientMessage.Frame initialFrame = new ClientMessage.Frame(new byte[REQUEST_INITIAL_FRAME_SIZE], UNFRAGMENTED_MESSAGE);
        encodeInt(initialFrame.content, TYPE_FIELD_OFFSET, REQUEST_MESSAGE_TYPE);
        encodeInt(initialFrame.content, PARTITION_ID_FIELD_OFFSET, -1);
        clientMessage.add(initialFrame);
        StringCodec.encode(clientMessage, mapName);
        return clientMessage;
    }

    /**
     * Name of the map.
     */
    public static java.lang.String decodeRequest(ClientMessage clientMessage) {
        ClientMessage.ForwardFrameIterator iterator = clientMessage.frameIterator();
        //empty initial frame
        iterator.next();
        return StringCodec.decode(iterator);
    }

    @edu.umd.cs.findbugs.annotations.SuppressFBWarnings({"URF_UNREAD_PUBLIC_OR_PROTECTED_FIELD"})
    public static class ResponseParameters {

        /**
         * The in memory storage format of the map:
         * 0 - Binary
         * 1 - Object
         * 2 - Native
         */
        public int inMemoryFormat;

        /**
         * Backup count of the map.
         */
        public int backupCount;

        /**
         * Async backup count of the map.
         */
        public int asyncBackupCount;

        /**
         * Time to live seconds for the map entries.
         */
        public int timeToLiveSeconds;

        /**
         * Maximum idle seconds for the map entries.
         */
        public int maxIdleSeconds;

        /**
         * Maximum size of the map.
         */
        public int maxSize;

        /**
         * The maximum size policy of the map:
         * 0 - PER_NODE
         * 1 - PER_PARTITION
         * 2 - USED_HEAP_PERCENTAGE
         * 3 - USED_HEAP_SIZE
         * 4 - FREE_HEAP_PERCENTAGE
         * 5 - FREE_HEAP_SIZE
         * 6 - USED_NATIVE_MEMORY_SIZE
         * 7 - USED_NATIVE_MEMORY_PERCENTAGE
         * 8 - FREE_NATIVE_MEMORY_SIZE
         * 9 - FREE_NATIVE_MEMORY_PERCENTAGE
         */
        public int maxSizePolicy;

        /**
         * Whether reading from backup data is allowed.
         */
        public boolean readBackupData;

        /**
         * The eviction policy of the map:
         * 0 - LRU
         * 1 - LFU
         * 2 - NONE
         * 3 - RANDOM
         */
        public int evictionPolicy;

        /**
         * Classname of the SplitBrainMergePolicy for the map.
         */
        public java.lang.String mergePolicy;

        /**
         * Global indexs of the map.
         */
        public java.util.List<com.hazelcast.config.IndexConfig> globalIndexes;

        /**
         * True if the globalIndexes is received from the member, false otherwise.
         * If this is false, globalIndexes has the default value for its type.
         */
        public boolean isGlobalIndexesExists;
    }

    public static ClientMessage encodeResponse(int inMemoryFormat, int backupCount, int asyncBackupCount, int timeToLiveSeconds, int maxIdleSeconds, int maxSize, int maxSizePolicy, boolean readBackupData, int evictionPolicy, java.lang.String mergePolicy, java.util.Collection<com.hazelcast.config.IndexConfig> globalIndexes) {
        ClientMessage clientMessage = ClientMessage.createForEncode();
        ClientMessage.Frame initialFrame = new ClientMessage.Frame(new byte[RESPONSE_INITIAL_FRAME_SIZE], UNFRAGMENTED_MESSAGE);
        encodeInt(initialFrame.content, TYPE_FIELD_OFFSET, RESPONSE_MESSAGE_TYPE);
        encodeInt(initialFrame.content, RESPONSE_IN_MEMORY_FORMAT_FIELD_OFFSET, inMemoryFormat);
        encodeInt(initialFrame.content, RESPONSE_BACKUP_COUNT_FIELD_OFFSET, backupCount);
        encodeInt(initialFrame.content, RESPONSE_ASYNC_BACKUP_COUNT_FIELD_OFFSET, asyncBackupCount);
        encodeInt(initialFrame.content, RESPONSE_TIME_TO_LIVE_SECONDS_FIELD_OFFSET, timeToLiveSeconds);
        encodeInt(initialFrame.content, RESPONSE_MAX_IDLE_SECONDS_FIELD_OFFSET, maxIdleSeconds);
        encodeInt(initialFrame.content, RESPONSE_MAX_SIZE_FIELD_OFFSET, maxSize);
        encodeInt(initialFrame.content, RESPONSE_MAX_SIZE_POLICY_FIELD_OFFSET, maxSizePolicy);
        encodeBoolean(initialFrame.content, RESPONSE_READ_BACKUP_DATA_FIELD_OFFSET, readBackupData);
        encodeInt(initialFrame.content, RESPONSE_EVICTION_POLICY_FIELD_OFFSET, evictionPolicy);
        clientMessage.add(initialFrame);

        StringCodec.encode(clientMessage, mergePolicy);
        ListMultiFrameCodec.encode(clientMessage, globalIndexes, IndexConfigCodec::encode);
        return clientMessage;
    }

    public static MCGetMapConfigCodec.ResponseParameters decodeResponse(ClientMessage clientMessage) {
        ClientMessage.ForwardFrameIterator iterator = clientMessage.frameIterator();
        ResponseParameters response = new ResponseParameters();
        ClientMessage.Frame initialFrame = iterator.next();
        response.inMemoryFormat = decodeInt(initialFrame.content, RESPONSE_IN_MEMORY_FORMAT_FIELD_OFFSET);
        response.backupCount = decodeInt(initialFrame.content, RESPONSE_BACKUP_COUNT_FIELD_OFFSET);
        response.asyncBackupCount = decodeInt(initialFrame.content, RESPONSE_ASYNC_BACKUP_COUNT_FIELD_OFFSET);
        response.timeToLiveSeconds = decodeInt(initialFrame.content, RESPONSE_TIME_TO_LIVE_SECONDS_FIELD_OFFSET);
        response.maxIdleSeconds = decodeInt(initialFrame.content, RESPONSE_MAX_IDLE_SECONDS_FIELD_OFFSET);
        response.maxSize = decodeInt(initialFrame.content, RESPONSE_MAX_SIZE_FIELD_OFFSET);
        response.maxSizePolicy = decodeInt(initialFrame.content, RESPONSE_MAX_SIZE_POLICY_FIELD_OFFSET);
        response.readBackupData = decodeBoolean(initialFrame.content, RESPONSE_READ_BACKUP_DATA_FIELD_OFFSET);
        response.evictionPolicy = decodeInt(initialFrame.content, RESPONSE_EVICTION_POLICY_FIELD_OFFSET);
        response.mergePolicy = StringCodec.decode(iterator);
        if (iterator.hasNext()) {
            response.globalIndexes = ListMultiFrameCodec.decode(iterator, IndexConfigCodec::decode);
            response.isGlobalIndexesExists = true;
        } else {
            response.isGlobalIndexesExists = false;
        }
        return response;
    }
}
