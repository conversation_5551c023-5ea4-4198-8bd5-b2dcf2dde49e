{"$schema": "http://json-schema.org/draft-06/schema", "type": "object", "properties": {"hazelcast": {"type": "object", "additionalProperties": false, "properties": {"import": {"$ref": "#/definitions/Import"}, "config-replacers": {"$ref": "#/definitions/ConfigReplacers"}, "cluster-name": {"type": "string"}, "license-key": {"type": "string"}, "instance-name": {"type": "string"}, "management-center": {"$ref": "#/definitions/ManagementCenter"}, "properties": {"description": "The \"properties\" mapping lets you add properties to some of the Hazelcast elements used to configure some of the Hazelcast modules. You can define the name and value of these properties. You can use \"properties\" for the following Hazelcast configuration elements: \\n * discovery-strategy \\n * map-store \\n * queue-store \\n * wan-replication \\n * ssl \\n * service \\n * login-module \\n * security-object \\n * socket-interceptor ", "type": "object"}, "wan-replication": {"$ref": "#/definitions/WanReplication"}, "network": {"$ref": "#/definitions/Network"}, "partition-group": {"$ref": "#/definitions/PartitionGroup"}, "executor-service": {"$ref": "#/definitions/ExecutorService"}, "durable-executor-service": {"$ref": "#/definitions/DurableExecutorService"}, "scheduled-executor-service": {"$ref": "#/definitions/ScheduledExecutorService"}, "cardinality-estimator": {"$ref": "#/definitions/CardinalityEstimator"}, "queue": {"$ref": "#/definitions/Queue"}, "map": {"$ref": "#/definitions/Map"}, "multimap": {"$ref": "#/definitions/Multimap"}, "replicatedmap": {"$ref": "#/definitions/ReplicatedMap"}, "cache": {"$ref": "#/definitions/Cache"}, "list": {"$ref": "#/definitions/List"}, "set": {"$ref": "#/definitions/Set"}, "topic": {"$ref": "#/definitions/Topic"}, "reliable-topic": {"$ref": "#/definitions/ReliableTopic"}, "ringbuffer": {"$ref": "#/definitions/Ringbuffer"}, "flake-id-generator": {"$ref": "#/definitions/FlakeIdGenerator"}, "listeners": {"$ref": "#/definitions/Listeners"}, "serialization": {"$ref": "#/definitions/Serialization"}, "native-memory": {"$ref": "#/definitions/NativeMemory"}, "security": {"$ref": "#/definitions/Security"}, "member-attributes": {"$ref": "#/definitions/MemberAttributes"}, "split-brain-protection": {"$ref": "#/definitions/SplitBrainProtection"}, "lite-member": {"$ref": "#/definitions/LiteMember"}, "hot-restart-persistence": {"$ref": "#/definitions/HotRestartPersistence"}, "persistence": {"$ref": "#/definitions/Persistence"}, "user-code-deployment": {"$ref": "#/definitions/UserCodeDeployment"}, "crdt-replication": {"$ref": "#/definitions/CrdtReplication"}, "pn-counter": {"$ref": "#/definitions/PNCounter"}, "advanced-network": {"$ref": "#/definitions/AdvancedNetwork"}, "cp-subsystem": {"$ref": "#/definitions/CPSubsystem"}, "metrics": {"$ref": "#/definitions/Metrics"}, "sql": {"$ref": "#/definitions/Sql"}, "auditlog": {"$ref": "#/definitions/Auditlog"}, "instance-tracking": {"$ref": "#/definitions/InstanceTracking"}, "jet": {"$ref": "#/definitions/Jet"}, "local-device": {"$ref": "#/definitions/LocalDevice"}, "dynamic-configuration": {"$ref": "#/definitions/DynamicConfiguration"}, "integrity-checker": {"$ref": "#/definitions/IntegrityChecker"}, "external-data-store": {"$ref": "#/definitions/ExternalDataStoreConfig"}}}, "hazelcast-client": {"type": "object", "additionalProperties": false, "properties": {"import": {"$ref": "#/definitions/Import"}, "config-replacers": {"$ref": "#/definitions/ConfigReplacers"}, "cluster-name": {"type": "string", "description": "Specifies the cluster name. It's sent as part of the client authentication message to Hazelcast member(s)."}, "instance-name": {"$ref": "#/properties/hazelcast/properties/instance-name"}, "properties": {"$ref": "#/properties/hazelcast/properties/properties"}, "client-labels": {"description": "The 'client-labels' sequence lets you define labels in your Java client, similar to the way it can be done for the members. Through the client labels, you can assign special roles for your clients and use these roles to perform some actions specific to those client connections. You can also group your clients using the client labels. These client groups can be blacklisted in the Hazelcast Management Center so that they can be prevented from connecting to a cluster. See the related section in the Hazelcast Management Center Reference Manual for more information on this topic.", "type": "array", "uniqueItems": true, "items": {"type": "string"}}, "backup-ack-to-client-enabled": {"type": "boolean", "default": true, "description": "The 'backup-ack-to-client-enabled' element lets you enable/disable backups ack to client. This feature reduces number of hops and increase performance for smart clients. It is enabled by default for smart clients. This config has no effect for unisocket clients."}, "listeners": {"$ref": "#/definitions/Listeners"}, "serialization": {"$ref": "#/definitions/Serialization"}, "native-memory": {"$ref": "#/definitions/NativeMemory"}, "proxy-factories": {"$ref": "#/definitions/ProxyFactories"}, "load-balancer": {"$ref": "#/definitions/LoadBalancer"}, "near-cache": {"$ref": "#/definitions/ClientNearCache"}, "flake-id-generator": {"$ref": "#/definitions/ClientFlakeIdGenerator"}, "query-caches": {"$ref": "#/definitions/Map/additionalProperties/properties/query-caches"}, "reliable-topic": {"$ref": "#/definitions/ClientReliableTopic"}, "user-code-deployment": {"$ref": "#/definitions/ClientUserCodeDeployment"}, "instance-tracking": {"$ref": "#/definitions/InstanceTracking"}, "metrics": {"$ref": "#/definitions/ClientMetrics"}, "connection-strategy": {"$ref": "#/definitions/ConnectionStrategy"}, "security": {"$ref": "#/definitions/ClientSecurity"}, "network": {"$ref": "#/definitions/ClientNetwork"}, "sql": {"$ref": "#/definitions/ClientSql"}}}, "hazelcast-client-failover": {"type": "object", "additionalProperties": false, "description": "The Hazelcast client can configurations that the client is using to connect to a Hazelcast cluster in the case of a disconnect is listed under the \"clients\" array. You can list client configurations as many as you want.", "properties": {"clients": {"type": "array", "minItems": 1, "items": {"type": "string"}}, "try-count": {"type": "integer", "minimum": 0, "default": 2147483647}}}}, "definitions": {"Import": {"type": "array", "items": {"type": "string"}}, "ConfigReplacers": {"type": "object", "properties": {"fail-if-value-missing": {"type": "boolean", "default": false}, "replacers": {"type": "array", "items": {"type": "object", "properties": {"class-name": {"type": "string"}, "properties": {"type": "object"}}, "required": ["class-name"]}}}}, "ManagementCenter": {"type": "object", "additionalProperties": false, "properties": {"scripting-enabled": {"description": "Set to true to allow scripting on the member, false to disallow.", "type": "boolean", "default": false}, "console-enabled": {"description": "Set to true to allow console commands execution on the member, false to disallow.", "type": "boolean", "default": false}, "data-access-enabled": {"description": "Set to true to allow Management Center access to contents of Hazelcast data structures (for instance map entries), false to disallow. Management Center can't access the data if at least one member has the data access disabled.", "type": "boolean", "default": true}, "trusted-interfaces": {"type": "array", "items": {"type": "string"}}}}, "NetworkJoin": {"type": "object", "description": "This configuration lets you choose a discovery mechanism that Hazelcast will use to form a cluster. Hazelcast can find members by multicast, TCP/IP lists and by various discovery mechanisms provided by different cloud APIs.", "additionalProperties": false, "properties": {"multicast": {"type": "object", "additionalProperties": false, "description": "Set its \"enabled\" attribute to true for discovery by multicast. It has another attribute (\"loopback-mode-enabled\") which enables or disables the loopback mode in the multicast discovery mechanism. ", "properties": {"enabled": {"type": "boolean", "default": true, "description": "Specifies whether multicast discovery is enabled."}, "loopback-mode-enabled": {"type": "boolean", "default": false, "description": "Specifies if the loopback mode is enabled for multicast discovery."}, "loopbackModeEnabled": {"type": "boolean", "default": false, "description": "Alias for loopback-mode-enabled"}, "multicast-group": {"type": "string", "default": "*********", "description": "Specifies the multicast group IP address when you want to create clusters within the same network."}, "multicast-port": {"type": "integer", "default": 54327, "description": "Specifies the multicast socket port that the Hazelcast member listens to and sends discovery messages through."}, "multicast-time-to-live": {"type": "integer", "minimum": 0, "maximum": 255, "default": 32, "description": "Time-to-live value for multicast packets sent out to control the scope of multicasts."}, "multicast-timeout-seconds": {"type": "integer", "default": 2, "description": "Only when the members are starting up, this timeout (in seconds) specifies the period during which a member waits for a multicast response from another node. For example, if you set it as 60 seconds, each node will wait for 60 seconds until a leader node is selected."}, "trusted-interfaces": {"type": "array", "description": "Includes IP addresses of trusted members. When a node wants to join to the cluster, its join request will be rejected if it is not a trusted member. You can give an IP addresses range using the wildcard (*) on the last digit of IP address (e.g. 192.168.1.* or *************-110).", "items": {"type": "string"}}}}, "auto-detection": {"$ref": "#/definitions/AutoDetection"}, "tcp-ip": {"type": "object", "additionalProperties": false, "properties": {"enabled": {"type": "boolean", "default": false, "description": "Specifies whether the TCP/IP discovery is enabled or not."}, "connection-timeout-seconds": {"type": "integer", "minimum": 0, "default": 5, "description": "The maximum amount of time Hazel<PERSON> is going to try to connect to a well known member before giving up. Setting it to a too low value could mean that a member is not able to connect to a cluster. Setting it to a too high value means that member startup could slow down because of longer timeouts (e.g. when a well known member is not up). Increasing this value is recommended if you have many IPs listed and the members cannot properly build up the cluster."}, "required-member": {"type": "string", "description": "IP address of the required member. Cluster will only be formed if the member with this IP address is found."}, "member": {"type": "string", "description": "IP address(es) of one or more well known members. Once members are connected to these well known ones, all member addresses will be communicated with each other. You can also give comma separated IP addresses using the \"members\" element or list the members with the \"member\" element under \"member-list\"."}, "members": {"type": "string", "description": "Comma separated IP addresses of one or more well known members."}, "member-list": {"type": "array", "description": "Sequence of IP address(es) of one or more well known members.", "items": {"type": "string"}}, "interface": {"type": "string", "description": "IP address(es) of one or more well known members."}}}, "aws": {"$ref": "#/definitions/AwsDiscovery"}, "gcp": {"$ref": "#/definitions/GcpDiscovery"}, "azure": {"$ref": "#/definitions/AzureDiscovery"}, "kubernetes": {"$ref": "#/definitions/KubernetesDiscovery"}, "eureka": {"$ref": "#/definitions/EurekaDiscovery"}, "discovery-strategies": {"$ref": "#/definitions/DiscoveryStrategies"}}}, "MemberAddressProvider": {"type": "object", "description": "IMPORTANT\\nThis configuration is not intended to provide addresses of other cluster members with which the Hazelcast instance will form a cluster. This is an SPI for advanced use in cases where the DefaultAddressPicker does not pick suitable addresses to bind to and publish to other cluster members. For instance, this could allow easier deployment in some cases when running on Docker, AWS or other cloud environments. That said, if you are just starting with Hazelcast, you will probably want to set the member addresses by using the tcp-ip or multicast configuration or adding a discovery strategy. Member address provider allows to plug in own strategy to customize:\\n1. What address Hazelcast will bind to\\n2. What address Hazelcast will advertise to other members on which they can bind to\\nIn most environments you don't need to customize this and the default strategy will work just fine. However in some cloud environments the default strategy does not make the right choice and the member address provider delegates the process of address picking to external code.", "additionalProperties": false, "properties": {"enabled": {"type": "boolean", "default": false, "description": "Specifies whether the member address provider SPI is enabled or not."}, "class-name": {"type": "string", "description": "The name of the class implementing the com.hazelcast.spi.MemberAddressProvider interface."}, "properties": {"type": "object", "description": "The properties that will be provided when constructing the provided MemberAddressProvider. Hazelcast will first try instantiating the provided class by invoking a constructor accepting a single java.util.Properties instance. In the case where there is no such constructor and there are also no properties defined by this configuration, Hazelcast will exceptionally try to use the no-arg constructor."}}}, "Interfaces": {"type": "object", "additionalProperties": false, "properties": {"enabled": {"type": "boolean", "default": false, "description": "You need to set it to true to be able to use your defined interfaces."}, "interfaces": {"type": "array", "items": {"type": "string"}}}, "description": "You can define multiple interfaces. By default, it is disabled."}, "FailureDetector": {"type": "object", "description": "A failure detector is responsible to determine if a member in the cluster is unreachable or crashed. Please refer to https://docs.hazelcast.org/docs/latest/manual/html-single/index.html#failure-detector-configuration for the failure detectors implemented by Hazelcast.", "additionalProperties": false, "properties": {"icmp": {"type": "object", "description": "ICMP can be used in addition to the other detectors. It operates at layer 3 and detects network and hardware issues more quickly.", "additionalProperties": false, "properties": {"enabled": {"type": "boolean", "default": false}, "timeout-milliseconds": {"type": "integer", "minimum": 1, "default": 1000, "description": "Timeout in milliseconds before declaring a failed ping."}, "fail-fast-on-startup": {"type": "boolean", "default": true, "description": "Cluster Member will fail to start if it is unable to action an ICMP ping command when ICMP is enabled. Failure is usually due to OS level restrictions."}, "interval-milliseconds": {"type": "integer", "minimum": 1000, "default": 1000, "description": "Time in milliseconds between each ICMP ping."}, "max-attempts": {"type": "integer", "minimum": 0, "default": 2, "description": "Maximum number of consecutive failed attempts before declaring a member suspect."}, "parallel-mode": {"type": "boolean", "default": true, "description": "Run ICMP detection in parallel with the Heartbeat failure detector."}, "ttl": {"type": "integer", "minimum": 0, "default": 255, "description": "Maximum number of times the IP Datagram (ping) can be forwarded, in most cases all Hazelcast cluster members would be within one network switch/router therefore default of 0 is usually sufficient."}}}}}, "AwsDiscovery": {"type": "object", "description": "Please refer to https://github.com/hazelcast/hazelcast-aws/#configuration for the configuration details.", "properties": {"enabled": {"type": "boolean", "default": false, "description": "Specifies whether AWS discovery is enabled or not."}}}, "GcpDiscovery": {"type": "object", "description": "Please refer to https://github.com/hazelcast/hazelcast-gcp/#configuration for the configuration details.", "properties": {"enabled": {"type": "boolean", "default": false, "description": "Specifies whether Google Cloud Platform discovery is enabled or not."}}}, "AzureDiscovery": {"type": "object", "description": "Please refer to https://github.com/hazelcast/hazelcast-azure/#configuring-at-hazelcast-side for the configuration details.", "properties": {"enabled": {"type": "boolean", "default": false, "description": "Specifies whether Microsoft Azure discovery is enabled or not."}}}, "KubernetesDiscovery": {"type": "object", "description": "Please refer to https://github.com/hazelcast/hazelcast-azure/#configuring-at-hazelcast-side for the configuration details.", "properties": {"enabled": {"type": "boolean", "default": false, "description": "Specifies whether Microsoft Azure discovery is enabled or not."}}}, "EurekaDiscovery": {"type": "object", "description": "Please refer to https://github.com/hazelcast/hazelcast-eureka#hazelcast-configuration for the configuration details.", "properties": {"enabled": {"type": "boolean", "default": false, "description": "Specifies whether Eureka Service Registry discovery is enabled or not."}}}, "DiscoveryStrategies": {"type": "object", "additionalProperties": false, "properties": {"node-filter": {"type": "object", "properties": {"class": {"type": "string"}}}, "discovery-strategies": {"type": "array", "items": {"type": "object", "additionalProperties": false, "description": "Set its \"enabled\" sub-element to true for discovery in various cloud infrastructures. You also need to set the value of \"hazelcast.discovery.enabled\" property to true. See the description of the \"properties\" element to learn how to do this. You can define multiple discovery strategies using the \"discovery-strategy\" sub-element and its properties. Please refer to http://docs.hazelcast.org/docs/latest/manual/html-single/index.html#discovering-cluster-members to see the properties you can use.", "properties": {"enabled": {"type": "boolean", "default": true}, "class": {"type": "string"}, "properties": {"type": "object"}}}}}}, "RestEndpointGroups": {"allOf": [{"type": "object", "propertyNames": {"enum": ["CLUSTER_READ", "CLUSTER_WRITE", "HEALTH_CHECK", "PERSISTENCE", "HOT_RESTART", "WAN", "DATA", "CP"]}, "additionalProperties": {"type": "object", "additionalProperties": false, "properties": {"enabled": {"type": "boolean"}}}}, {"patternProperties": {"^(CLUSTER_READ|HEALTH_CHECK)$": {"default": {"enabled": true}}}, "additionalProperties": {"default": {"enabled": false}}}]}, "Network": {"type": "object", "additionalProperties": false, "properties": {"public-address": {"type": "string", "description": "Overrides the public address of a node. By default, a node selects its socket address as its public address. But behind a network address translation (NAT), two endpoints (nodes) may not be able to see/access each other. If both nodes set their public addresses to their defined addresses on NAT, then they can communicate with each other. In this case, their public addresses are not an address of a local network interface but a virtual address defined by NAT. This is optional to set and useful when you have a private cloud."}, "reuse-address": {"type": "boolean", "default": false, "description": "When you shutdown a cluster member, the server socket port will be in the TIME_WAIT state for the next couple of minutes. If you start the member right after shutting it down, you may not be able to bind it to the same port because it is in the TIME_WAIT state. If you set reuse-address to true, the TIME_WAIT state is ignored and you can bind the member to the same port again."}, "port": {"type": "object", "additionalProperties": false, "description": "Specifies the ports that Hazelcast will use to communicate between cluster members.", "properties": {"port": {"type": "integer", "minimum": 0, "maximum": 65536, "default": 5701}, "port-count": {"type": "integer", "default": 100, "minimum": 1, "description": "By default, Hazelcast will try 100 ports to bind (i.e. the ports between 5701 and 5801). You can change the port count in such cases as having large instances on a single machine or you are willing to have only a few ports assigned."}, "auto-increment": {"type": "boolean", "default": true, "description": "By default, Hazelcast tries to find a port by automatically incrementing the port numbers. If you don't want this (for example, you want to use a specific port), set auto-increment to false. If it is set to false, the port-count attribute is ignored."}}}, "outbound-ports": {"$ref": "#/definitions/OutboundPorts"}, "join": {"$ref": "#/definitions/NetworkJoin"}, "member-address-provider": {"$ref": "#/definitions/MemberAddressProvider"}, "failure-detector": {"$ref": "#/definitions/FailureDetector"}, "interfaces": {"$ref": "#/definitions/Interfaces"}, "memcache-protocol": {"type": "object", "additionalProperties": false, "properties": {"enabled": {"type": "boolean", "default": false}}, "description": "Allows to configure Memcache text protocol support in Hazelcast."}, "ssl": {"$ref": "#/definitions/SSL"}, "rest-api": {"type": "object", "additionalProperties": false, "properties": {"enabled": {"type": "boolean", "default": false}, "endpoint-groups": {"$ref": "#/definitions/RestEndpointGroups"}}}, "symmetric-encryption": {"type": "object", "additionalProperties": false, "description": "Lets you encrypt the entire socket level communication among all Hazelcast members. This feature is available only in Hazelcast Enterprise.  Its configuration contains the encryption properties and the same configuration must be placed to all members. By default, it is disabled.", "properties": {"enabled": {"type": "boolean", "default": false}, "algorithm": {"type": "string", "default": "PBEWithMD5AndDES"}, "password": {"type": "string"}, "salt": {"type": "string"}, "iteration-count": {"type": "integer", "default": 19}}}, "socket-interceptor": {"$ref": "#/definitions/SocketInterceptor"}}}, "AdvancedNetwork": {"type": "object", "additionalProperties": false, "properties": {"enabled": {"type": "boolean", "default": false, "description": "Indicates whether the advanced network configuration is enabled or not."}, "join": {"$ref": "#/definitions/NetworkJoin"}, "member-address-provider": {"$ref": "#/definitions/MemberAddressProvider"}, "failure-detector": {"$ref": "#/definitions/FailureDetector"}, "interfaces": {"$ref": "#/definitions/Interfaces"}, "memcache-server-socket-endpoint-config": {"$ref": "#/definitions/SocketEndpointConfig"}, "wan-server-socket-endpoint-config": {"description": "Configures the server sockets used for the WAN replication's inbound WAN connections. Multiple WAN server sockets can be configured to allow configuring separate server sockets for each inbound WAN connection.", "$ref": "#/definitions/SocketEndpointConfig"}, "client-server-socket-endpoint-config": {"description": "Configures the server socket used for the member-client communication.", "$ref": "#/definitions/SocketEndpointConfig"}, "member-server-socket-endpoint-config": {"description": "Configures the server socket used for the member-member communication.", "$ref": "#/definitions/SocketEndpointConfig"}, "wan-endpoint-config": {"type": "object", "description": "This element configures socket settings for the outgoing WAN connections.", "additionalProperties": {"$ref": "#/definitions/WanEndpointConfig"}}, "rest-server-socket-endpoint-config": {"description": "Controls access to Hazelcast HTTP REST API. The methods available through REST API are grouped to several REST endpoint groups, which can be specified in this section.", "allOf": [{"$ref": "#/definitions/SocketEndpointConfig"}, {"properties": {"endpoint-groups": {"$ref": "#/definitions/RestEndpointGroups"}}, "propertyNames": {"enum": ["endpoint-groups", "port", "public-address", "reuse-address", "name", "outbound-ports", "interfaces", "ssl", "socket-interceptor", "symmetric-encryption", "socket-options"]}}]}}}, "PartitionGroup": {"type": "object", "properties": {"enabled": {"type": "boolean", "default": false}, "group-type": {"enum": ["HOST_AWARE", "NODE_AWARE", "CUSTOM", "PER_MEMBER", "ZONE_AWARE", "PLACEMENT_AWARE", "SPI"], "default": "PER_MEMBER"}, "member-group": {"type": "array", "items": {"type": "array", "items": {"type": "string"}}}}, "additionalProperties": false}, "ExecutorService": {"type": "object", "additionalProperties": {"type": "object", "properties": {"statistics-enabled": {"type": "boolean", "default": true}, "pool-size": {"$ref": "#/definitions/executorServicePoolSize"}, "queue-capacity": {"type": "integer", "minimum": 0, "default": 2147483647}, "split-brain-protection-ref": {"type": "string"}}}}, "DurableExecutorService": {"type": "object", "additionalProperties": {"pool-size": {"$ref": "#/definitions/executorServicePoolSize"}, "durability": {"type": "integer", "minimum": 0, "default": 1}, "capacity": {"type": "integer", "minimum": 1, "default": 100}, "split-brain-protection-ref": {"type": "string"}}}, "ScheduledExecutorService": {"type": "object", "additionalProperties": {"pool-size": {"$ref": "#/definitions/executorServicePoolSize"}, "durability": {"type": "integer", "minimum": 0, "default": 1}, "capacity": {"type": "integer", "minimum": 1, "default": 100}, "capacity-policy": {"enum": ["PER_NODE", "PER_PARTITION"]}, "split-brain-protection-ref": {"type": "string"}, "merge-policy": {"$ref": "#/definitions/MergePolicy"}}}, "executorServicePoolSize": {"type": "integer", "minimum": 1, "default": 16}, "MergePolicyClassName": {"type": "string", "examples": ["ExpirationTimeMergePolicy", "HyperLogLogMergePolicy", "HigherHitsMergePolicy", "PutIfAbsentMergePolicy", "DiscardMergePolicy", "LatestUpdateMergePolicy", "PassThroughMergePolicy", "LatestAccessMergePolicy"], "default": "PutIfAbsentMergePolicy"}, "MergePolicy": {"type": "object", "additionalProperties": false, "properties": {"batch-size": {"type": "integer", "minimum": 0, "default": 100}, "class-name": {"$ref": "#/definitions/MergePolicyClassName"}}}, "CardinalityEstimator": {"additionalProperties": {"type": "object", "additionalProperties": false, "properties": {"backup-count": {"$ref": "#/definitions/BackupCount"}, "async-backup-count": {"$ref": "#/definitions/AsyncBackupCount"}, "split-brain-protection-ref": {"type": "string", "description": " Adds the Split Brain Protection for this data-structure which you configure using the \"split-brain-protection\" element. You should set the \"split-brain-protection-ref\"'s value as the \"split-brain-protection\"'s name."}, "merge-policy": {"allOf": [{"$ref": "#/definitions/MergePolicy"}, {"default": {"batch-site": 100, "class-name": "HyperLogLogMergePolicy"}}]}}}}, "InMemoryFormat": {"enum": ["BINARY", "OBJECT", "NATIVE"], "default": "BINARY", "description": "Binary type that will be used for storing records. Possible values: BINARY (default): keys and values will be stored as binary data. OBJECT: values will be stored in their object forms. NATIVE: values will be stored in non-heap region of JVM (Hazelcast Enterprise only)"}, "Eviction": {"type": "object", "additionalProperties": false, "properties": {"eviction-policy": {"enum": ["LRU", "LFU", "NONE", "RANDOM"], "default": "NONE"}, "max-size-policy": {"enum": ["PER_NODE", "PER_PARTITION", "USED_HEAP_PERCENTAGE", "USED_HEAP_SIZE", "FREE_HEAP_PERCENTAGE", "FREE_HEAP_SIZE", "ENTRY_COUNT", "USED_NATIVE_MEMORY_SIZE", "USED_NATIVE_MEMORY_PERCENTAGE", "FREE_NATIVE_MEMORY_SIZE", "FREE_NATIVE_MEMORY_PERCENTAGE"], "default": "PER_NODE"}, "size": {"type": "integer", "minimum": 0, "default": 2147483647}, "comparator-class-name": {"type": "string"}}}, "IndexConfig": {"type": "object", "additionalProperties": false, "properties": {"type": {"enum": ["SORTED", "BITMAP", "HASH"], "default": "SORTED"}, "name": {"type": "string"}, "attributes": {"type": "array", "items": {"type": "string"}, "minItems": 1}, "bitmap-index-options": {"type": "object", "additionalProperties": false, "properties": {"unique-key": {"type": "string", "default": "__key"}, "unique-key-transformation": {"enum": ["OBJECT", "LONG", "RAW"], "default": "OBJECT", "description": "Defines an assortment of transformations which can be applied to unique-key values. OBJECT: Extracted unique key value is interpreted as an object value. Non-negative unique ID is assigned to every distinct object value. LONG: Extracted unique key value is interpreted as a whole integer value of byte, short, int or long type. The extracted value is upcasted to long (if necessary) and unique non-negative ID is assigned to every distinct value. RAW: Extracted unique key value is interpreted as a whole integer value of byte, short, int or long type. The extracted value is upcasted to long (if necessary) and the resulting value is used directly as an ID."}}}, "btree-index": {"type": "object", "additionalProperties": false, "properties": {"page-size": {"$ref": "#/definitions/Capacity", "description": "Page size of the index B+ Tree. Default value is 16 kB.", "default": {"value": 16, "unit": "KILOBYTES"}}, "memory-tier": {"type": "object", "description": "Memory tier.", "additionalProperties": false, "properties": {"capacity": {"$ref": "#/definitions/Capacity", "description": "Memory tier capacity, i.e., how much main memory should this tier consume at most. The default is 256 megabytes."}}, "default": {"capacity": {"default": {"value": 16, "unit": "KILOBYTES"}}}}}}}, "required": ["attributes"]}, "Map": {"type": "object", "additionalProperties": {"type": "object", "additionalProperties": false, "properties": {"in-memory-format": {"$ref": "#/definitions/InMemoryFormat"}, "metadata-policy": {"enum": ["CREATE_ON_UPDATE", "OFF"], "default": "OFF"}, "statistics-enabled": {"type": "boolean", "default": true}, "per-entry-stats-enabled": {"type": "boolean", "default": false}, "cache-deserialized-values": {"enum": ["INDEX_ONLY", "INDEX-ONLY", "NEVER", "ALWAYS"], "default": "INDEX_ONLY"}, "backup-count": {"$ref": "#/definitions/BackupCount"}, "async-backup-count": {"$ref": "#/definitions/AsyncBackupCount"}, "time-to-live-seconds": {"description": "The maximum number of seconds for each entry to stay in the map.", "type": "integer", "minimum": 0, "default": 0}, "max-idle-seconds": {"type": "integer", "minimum": 0, "default": 0}, "eviction": {"$ref": "#/definitions/Eviction"}, "merge-policy": {"$ref": "#/definitions/MergePolicy"}, "read-backup-data": {"description": "Sets if read-backup-data (reading local backup entries) is enabled for this map.", "type": "boolean", "default": false}, "hot-restart": {"type": "object", "additionalProperties": false, "properties": {"enabled": {"description": "Sets whether hot restart is enabled on related data structure.", "type": "boolean", "default": false}, "fsync": {"description": "Sets whether disk write should be followed by an fsync() system call.", "type": "boolean", "default": false}}}, "data-persistence": {"type": "object", "additionalProperties": false, "properties": {"enabled": {"description": "Sets whether persistence is enabled on related data structure.", "type": "boolean", "default": false}, "fsync": {"description": "Sets whether disk write should be followed by an fsync() system call.", "type": "boolean", "default": false}}}, "event-journal": {"type": "object", "additionalProperties": false, "properties": {"enabled": {"type": "boolean", "default": false}, "capacity": {"type": "integer", "minimum": 1, "default": 10000, "description": "The capacity is the total number of items that the event journal can hold at any moment. The actual number of items contained in the journal can be lower. NOTE: The capacity is shared equally between all partitions. This is done by assigning each partition {capacity / partitionCount} available slots in the event journal. Because of this, the effective total capacity may be somewhat lower and you must make sure that the configured capacity is at least greater than the partition count."}, "time-to-live-seconds": {"type": "integer", "minimum": 0, "default": 0, "description": "Time to live is the time the event journal retains items before removing them from the journal. The events are removed on journal read and write actions, not while the journal is idle. Time to live can be disabled by setting timeToLiveSeconds to 0. This means that the events never expire but they can be overwritten when the capacity of the journal is exceeded."}}}, "merkle-tree": {"type": "object", "additionalProperties": false, "properties": {"enabled": {"type": "boolean", "default": false}, "depth": {"type": "integer", "minimum": 2, "maximum": 27, "default": 10}}}, "map-store": {"type": "object", "additionalProperties": false, "properties": {"enabled": {"type": "boolean", "default": true}, "initial-mode": {"enum": ["LAZY", "EAGER"], "default": "LAZY", "description": "LAZY: Default load mode where load is async; EAGER: load mode where load is blocked till all partitions are loaded."}, "offload": {"type": "boolean", "default": true, "description": "Set true to offload map-store interacting code parts from partition threads, otherwise interaction is executed inside partition thread"}, "write-delay-seconds": {"type": "integer", "minimum": 0, "default": 0, "description": "The number of seconds to delay before writing (storing) the dirty records."}, "write-batch-size": {"type": "integer", "minimum": 1, "default": 1, "description": "The number of operations to be included in each batch processing round."}, "write-coalescing": {"type": "boolean", "default": true, "description": " Setting writeCoalescing is meaningful if you are using write-behind MapStore. When writeCoalescing is true, only the latest store operation on a key in the writeDelaySeconds time-window will be reflected to MapStore."}, "class-name": {"type": "string", "description": "The name for the MapStore implementation class"}, "factory-class-name": {"type": "string", "description": "The name for the MapStoreFactory implementation class"}, "properties": {"type": "object", "default": {}}}, "default": {"enabled": false}}, "near-cache": {"type": "object", "additionalProperties": false, "properties": {"name": {"type": "string", "default": "default"}, "time-to-live-seconds": {"type": "integer", "minimum": 0, "default": 0, "description": "The maximum number of seconds for each entry to stay in the Near Cache (time to live). Entries that are older than timeToLiveSeconds will automatically be evicted from the Near Cache."}, "max-idle-seconds": {"type": "integer", "minimum": 0, "default": 0, "description": "The maximum number of seconds each entry can stay in the Near Cache as untouched (not-read). Entries that are not read (touched) more than maxIdleSeconds value will get removed from the Near Cache. Accepts any integer between 0 and Integer#MAX_VALUE. The value 0 means Integer#MAX_VALUE. The default is 0."}, "invalidate-on-change": {"type": "boolean", "default": true, "description": "Sets if Near Cache entries are invalidated when the entries in the backing data structure are changed. When this setting is enabled, a Hazelcast instance with a Near Cache listens for cluster-wide changes on the entries of the backing data structure and invalidates its corresponding Near Cache entries. Changes done on the local Hazelcast instance always invalidate the Near Cache immediately."}, "in-memory-format": {"$ref": "#/definitions/InMemoryFormat"}, "cache-local-entries": {"type": "boolean", "default": false, "description": "Sets if local entries are also cached in the Near Cache. This is useful when the in-memory format of the Near Cache is different from the backing data structure. This setting has no meaning on Hazelcast clients, since they have no local entries."}, "local-update-policy": {"description": "Defines how to reflect local updates to the Near Cache. Possible values:\\n * INVALIDATE: Local put and local remove immediately invalidate Near Cache.\\n * CACHE_ON_UPDATE: While local remove immediately invalidates Near Cache, local put adds new value to it.", "enum": ["INVALIDATE", "CACHE_ON_UPDATE"], "default": "INVALIDATE"}, "eviction": {"$ref": "#/definitions/Eviction"}, "serialize-keys": {"type": "boolean", "default": false}}}, "wan-replication-ref": {"type": "object", "additionalProperties": {"type": "object", "additionalProperties": false, "properties": {"merge-policy-class-name": {"$ref": "#/definitions/MergePolicyClassName"}, "republishing-enabled": {"type": "boolean", "default": true, "description": "Sets if incoming WAN events to this member should be republished (forwarded) to this WAN replication reference."}, "filters": {"type": "array", "items": {"type": "string"}, "description": "Array of class names implementing the CacheWanEventFilter or MapWanEventFilter for filtering outbound WAN replication events. NOTE: EE only"}}}}, "indexes": {"type": "array", "items": {"$ref": "#/definitions/IndexConfig"}}, "attributes": {"type": "object", "additionalProperties": {"type": "object", "additionalProperties": false, "properties": {"extractor-class-name": {"type": "string", "description": "Fully qualified class name of the extractor used to extract the value of the attribute."}}, "required": ["extractor-class-name"], "description": "Contains the configuration of a custom attribute that will be extracted from a Map's entry using a given ValueExtractor."}}, "entry-listeners": {"type": "array", "items": {"$ref": "#/definitions/EntryListener"}, "description": "Adds listeners (listener classes) for the map entries using the \"entry-listener\" sub-elements"}, "partition-lost-listeners": {"type": "array", "items": {"type": "string"}}, "split-brain-protection-ref": {"type": "string"}, "query-caches": {"type": "object", "additionalProperties": {"type": "object", "additionalProperties": false, "properties": {"map-name": {"type": "string", "description": "In client configurations it is used to identify the map to which this query cache belongs to."}, "include-value": {"type": "boolean", "default": true, "description": "Enables value caching."}, "predicate": {"type": "object", "additionalProperties": false, "properties": {"class-name": {"type": "string"}, "sql": {"type": "string"}}, "oneOf": [{"required": ["class-name"]}, {"required": ["sql"]}], "description": "Predicate to filter events which will be applied to the query cache."}, "entry-listeners": {"type": "array", "items": {"$ref": "#/definitions/EntryListener"}}, "in-memory-format": {"$ref": "#/definitions/InMemoryFormat"}, "populate": {"type": "boolean", "default": true, "description": "Flag to enable/disable initial population of the QueryCache."}, "coalesce": {"type": "boolean", "default": false}, "serialize-keys": {"description": "Defines if the Query Cache keys should be serialized or not. Keys are better to be serialized if they are mutable and need to be cloned via serialization. Default value is false.", "type": "boolean", "default": false}, "delay-seconds": {"type": "integer", "minimum": 0, "default": 0, "description": "Minimum time in seconds that an event waits in the member's buffer."}, "batch-size": {"type": "integer", "minimum": 1, "default": 1, "description": "The batch size which will be used to determine number of events to be sent in a batch to QueryCache."}, "buffer-size": {"type": "integer", "minimum": 1, "default": 16, "description": "Maximum number of events which can be stored in a buffer of partition."}, "eviction": {"$ref": "#/definitions/Eviction"}, "indexes": {"type": "array", "items": {"$ref": "#/definitions/IndexConfig"}}}}}, "tiered-store": {"type": "object", "additionalProperties": false, "properties": {"enabled": {"type": "boolean", "description": "True if Tiered-Store is enabled, false otherwise."}, "memory-tier": {"type": "object", "description": "Memory tier.", "additionalProperties": false, "properties": {"capacity": {"$ref": "#/definitions/Capacity", "description": "Memory tier capacity, i.e., how much main memory should this tier consume at most. The default is 256 megabytes."}}}, "disk-tier": {"type": "object", "description": "Disk tier.", "additionalProperties": false, "properties": {"enabled": {"type": "boolean", "description": "True to enable using disk as the second memory tier, false otherwise."}, "device-name": {"type": "string", "default": "default-tiered-store-device", "description": "Name of the device for a given disk tier."}}, "required": ["enabled"]}}, "required": ["enabled"]}, "partition-strategy": {"type": "string"}}}}, "EntryListener": {"type": "object", "additionalProperties": false, "properties": {"class-name": {"type": "string"}, "include-value": {"type": "boolean", "default": false, "description": "True if you want the entry event to contain the item values."}, "local": {"type": "boolean", "default": false, "description": "True if you want to listen to the entries on the local member."}}, "required": ["class-name"]}, "Set": {"type": "object", "additionalProperties": {"type": "object", "additionalProperties": false, "properties": {"backup-count": {"$ref": "#/definitions/BackupCount"}, "async-backup-count": {"$ref": "#/definitions/AsyncBackupCount"}, "max-size": {"type": "integer", "minimum": 0, "description": "Maximum size of the set (item count). Its default value is 0, meaning Integer.MAX_VALUE."}, "item-listeners": {"type": "array", "items": {"$ref": "#/definitions/ItemListener"}}, "split-brain-protection-ref": {"type": "string"}, "merge-policy": {"$ref": "#/definitions/MergePolicy"}, "statistics-enabled": {"type": "boolean", "default": true, "description": "True if statistics gathering is enabled on the set, false otherwise."}}}}, "Topic": {"type": "object", "additionalProperties": {"type": "object", "additionalProperties": false, "properties": {"global-ordering-enabled": {"type": "boolean", "default": false, "description": "When it is set to true, all cluster members that listen to a topic will receive the messages in the same order they were published by all members. This way, Hazelcast guarantees that all members will see the in the same order. Its default value is false."}, "statistics-enabled": {"type": "boolean", "default": "true", "description": "When you enable it, you can retrieve topic statistics such as total number of published and received messages. Its default value is true."}, "multi-threading-enabled": {"type": "boolean", "default": false, "description": "Enable multi-threaded message handling. When enabled any thread from events thread pool can be used for incoming message processing. Otherwise only one dedicated thread will be used to handle topic messages. Note: it can be enabled only in case when global ordering is disabled. Moreover, the local message ordering is not supported in this mode also. This means the messages produced by local publisher can be processed by several threads with no ordering guarantee."}, "message-listeners": {"type": "array", "items": {"type": "string"}}}, "not": {"properties": {"global-ordering-enabled": {"const": true}, "multi-threading-enabled": {"const": true}}, "required": ["global-ordering-enabled", "multi-threading-enabled"]}}}, "ReliableTopic": {"type": "object", "additionalProperties": {"type": "object", "additionalProperties": false, "properties": {"statistics-enabled": {"type": "boolean", "default": true, "description": "Enables or disables statistics for this reliable topic. Collects the creation time, total number of published and received messages for each member locally."}, "topic-overload-policy": {"enum": ["DISCARD_OLDEST", "DISCARD_NEWEST", "BLOCK", "ERROR"], "default": "BLOCK", "description": "A policy to deal with an overloaded topic; so topic where there is no place to store new messages. This policy can only be used in combination with the com.hazelcast.core.HazelcastInstance#getReliableTopic(String). The reliable topic uses a com.hazelcast.ringbuffer.Ringbuffer to store the messages. A ringbuffer doesn't track where readers are, so it has no concept of a slow consumers. This provides many advantages like high performance reads, but it also gives the ability to the reader to re-read the same message multiple times in case of an error. A ringbuffer has a limited, fixed capacity. A fast producer may overwrite old messages that are still being read by a slow consumer. To prevent this, we may configure a time-to-live on the ringbuffer (see com.hazelcast.config.RingbufferConfig#setTimeToLiveSeconds(int). Once the time-to-live is configured, the TopicOverloadPolicy controls how the publisher is going to deal with the situation that a ringbuffer is full and the oldest item in the ringbuffer is not old enough to get overwritten. Keep in mind that this retention period (time-to-live) can keep messages from being overwritten, even though all readers might have already completed reading. Its default value is BLOCK. Available values are as follows: - DISCARD_OLDEST: Using this policy, a message that has not expired can be overwritten. No matter the retention period set, the overwrite will just overwrite the item. This can be a problem for slow consumers because they were promised a certain time window to process messages. But it will benefit producers and fast consumers since they are able to continue. This policy sacrifices the slow producer in favor of fast producers/consumers. - DISCARD_NEWEST: Message that was to be published is discarded. - BLOCK: The caller will wait until there is space in the Ringbuffer. - ERROR: The publish call fails immediately. "}, "read-batch-size": {"type": "integer", "default": 10, "minimum": 1, "description": "Sets the read batch size. The ReliableTopic tries to read a batch of messages from the ringbuffer. It will get at least one, but if there are more available, then it will try to get more to increase throughput. The maximum read batch size can be influenced using the read batch size. Apart from influencing the number of messages to retrieve, the readBatchSize also determines how many messages will be processed by the thread running the MessageListener before it returns back to the pool to look for other MessageListeners that need to be processed. The problem with returning to the pool and looking for new work is that interacting with an executor is quite expensive due to contention on the work-queue. The more work that can be done without retuning to the pool, the smaller the overhead. If the readBatchSize is 10 and there are 50 messages available, 10 items are retrieved and processed consecutively before the thread goes back to the pool and helps out with the processing of other messages. If the readBatchSize is 10 and there are 2 items available, 2 items are retrieved and processed consecutively. If the readBatchSize is an issue because a thread will be busy too long with processing a single MessageListener and it can't help out other MessageListeners, increase the size of the threadpool so the other MessageListeners don't need to wait for a thread, but can be processed in parallel. "}, "message-listeners": {"type": "array", "items": {"type": "string"}}}}}, "Ringbuffer": {"type": "object", "additionalProperties": {"type": "object", "additionalProperties": false, "properties": {"capacity": {"type": "integer", "minimum": 1, "default": 10000, "description": "Number of items in the Ringbuffer. If no \"time-to-live-seconds\" is set, this value will always be equal to the capacity after the head completes the first loop around the ring. This is because no items are being expired."}, "time-to-live-seconds": {"type": "integer", "default": 0, "description": "Sets the time to live in seconds which is the maximum number of seconds for each item to stay in the ringbuffer before being removed. Entries that are older than time-to-live-seconds are removed from the ringbuffer on the next ringbuffer operation (read or write). Time to live can be disabled by setting time-to-live-seconds to 0. It means that items won't get removed because they expire. They may only be overwritten. When time-to-live-seconds is disabled and after the tail does a full loop in the ring, the ringbuffer size will always be equal to the capacity. The time-to-live-seconds can be any integer between 0 and Integer#MAX_VALUE. 0 means infinite. The default is 0. "}, "backup-count": {"$ref": "#/definitions/BackupCount"}, "async-backup-count": {"$ref": "#/definitions/AsyncBackupCount"}, "in-memory-format": {"enum": ["OBJECT", "BINARY"], "default": "BINARY", "description": "Sets the in-memory format. Setting the in-memory format controls the format of the stored item in the ringbuffer: - OBJECT: the item is stored in deserialized format (a regular object) - BINARY (default): the item is stored in serialized format (a binary blob) The object in-memory format is useful when: - the object stored in object format has a smaller footprint than in binary format - if there are readers using a filter. Since for every filter invocation, the object needs to be available in object format."}, "ringbuffer-store": {"type": "object", "additionalProperties": false, "properties": {"class-name": {"type": "string", "description": "Fully qualified class name."}, "factory-class-name": {"type": "string", "description": "Fully qualified name of the RingbufferStoreFactory implementation class."}, "properties": {"type": "object"}, "enabled": {"type": "boolean", "default": true}}}, "split-brain-protection-ref": {"type": "string", "description": "Adds the Split Brain Protection for this data-structure which you configure using the \"split-brain-protection\" element. You should set the \"split-brain-protection-ref\"'s value as the \"split-brain-protection\"'s name."}, "merge-policy": {"$ref": "#/definitions/MergePolicy"}}}}, "FlakeIdGenerator": {"type": "object", "additionalProperties": {"type": "object", "additionalProperties": false, "properties": {"prefetch-count": {"type": "integer", "default": 100, "minimum": 1, "maximum": 100000, "description": "Sets how many IDs are pre-fetched on the background when one call to FlakeIdGenerator.newId() is made. Value must be in the range 1..100,000. This setting pertains only to newId() calls made on the member that configured it."}, "prefetch-validity-millis": {"type": "integer", "default": 600000, "minimum": 0, "description": "Sets for how long the pre-fetched IDs can be used. If this time elapses, a new batch of IDs will be fetched. Time unit is milliseconds, default is 10 minutes. The IDs contain timestamp component, which ensures rough global ordering of IDs. If an ID is assigned to an object that was created much later, it will be much out of order. If you don't care about ordering, set this value to 0. This setting pertains only to newId() calls made on the member that configured it."}, "epoch-start": {"description": "Sets the offset of timestamp component. Time unit is milliseconds, default is 1.1.2018 0:00 UTC. If you set the epoch start to a future instant, negative IDs will be generated until that time occurs.", "type": "integer", "default": 1514764800000}, "node-id-offset": {"type": "integer", "minimum": 0, "default": 0, "description": "Sets the offset that will be added to the node ID assigned to cluster member for this generator. Might be useful in A/B deployment scenarios where you have cluster A which you want to upgrade. You create cluster B and for some time both will generate IDs and you want to have them unique. In this case, configure node ID offset for generators on cluster B."}, "bits-sequence": {"type": "integer", "default": 6, "minimum": 0, "maximum": 63, "description": "Sets the bit-length of the sequence component"}, "bits-node-id": {"type": "integer", "minimum": 0, "maximum": 63, "default": 16, "description": "Sets the bit-length of node id component."}, "allowed-future-millis": {"type": "integer", "default": 15000, "minimum": 0, "description": "Sets how far to the future is the generator allowed to go to generate IDs without blocking."}, "statistics-enabled": {"type": "boolean", "default": true, "description": "When you enable it, you can retrieve the Flake ID generators statistics."}}}}, "Queue": {"type": "object", "additionalProperties": {"type": "object", "additionalProperties": false, "properties": {"statistics-enabled": {"type": "boolean", "default": true, "description": "When you enable it, you can retrieve queue statistics."}, "max-size": {"type": "integer", "minimum": 0, "description": "Maximum size of the queue. When a JVM's local queue size reaches the maximum, all put/offer operations will be blocked until the queue size of the JVM goes below this maximum. Its default value is 0, meaning Integer.MAX_VALUE"}, "backup-count": {"$ref": "#/definitions/BackupCount"}, "async-backup-count": {"$ref": "#/definitions/AsyncBackupCount"}, "empty-queue-ttl": {"type": "integer", "default": -1, "description": "Used to purge unused or empty queues. If you define a value (time in seconds) for this element, then your queue will be destroyed if it stays empty or unused for that time."}, "item-listeners": {"type": "array", "items": {"$ref": "#/definitions/ItemListener"}}, "queue-store": {"type": "object", "additionalProperties": false, "description": "Includes configuration elements and attributes for your queue store implementation. When you want to load/store the distributed queue items from/to a persistent datastore, first implement Hazelcast's QueueStore interface and then configure it using this element", "properties": {"enabled": {"type": "boolean", "default": true}, "class-name": {"description": "The fully qualified name of the QueueStore implementation you want to use", "type": "string"}, "factory-class-name": {"description": "The fully qualified name of the QueueStoreFactory which will be created by Hazelcast to instantiate QueueStores.", "type": "string"}, "properties": {"type": "object", "properties": {"binary": {"type": "boolean", "default": false, "description": "If you do not reach the queue store from an external application, you might prefer to insert the items in binary form. To do so, set this property to true and skip the deserialization step, which is a performance optimization."}, "memory-limit": {"type": "integer", "default": 1000, "description": "Number of items after which Hazelcast will store items only to datastore. For example, if the memory limit is 1000, then the 1001st item will be put only to datastore. This feature is useful when you want to avoid out-of-memory conditions. If you want to always use memory, you can set it to Integer.MAX_VALUE."}, "bulk-load": {"type": "integer", "default": 250, "description": "Size of the bulks loaded from QueueStore when the queue is initialized."}}}}}, "split-brain-protection-ref": {"type": "string"}, "merge-policy": {"$ref": "#/definitions/MergePolicy"}, "priority-comparator-class-name": {"type": "string", "description": "Fully-qualified comparator's class name to be used for the priority queue. If nothing is provided, then queue behaves as a FIFO queue."}}}}, "ItemListener": {"type": "object", "additionalProperties": false, "properties": {"include-value": {"type": "boolean", "default": true, "description": "Set to true if you want the item event to contain the item values."}, "class-name": {"type": "string"}}, "required": ["class-name"]}, "Multimap": {"type": "object", "additionalProperties": {"type": "object", "additionalProperties": false, "properties": {"backup-count": {"$ref": "#/definitions/BackupCount"}, "async-backup-count": {"$ref": "#/definitions/AsyncBackupCount"}, "value-collection-type": {"enum": ["SET", "LIST"]}, "binary": {"type": "boolean", "default": true, "description": "The in-memory format being used for representing the multimap. If it is false, the OBJECT in-memory format will be used."}, "entry-listeners": {"type": "array", "items": {"$ref": "#/definitions/EntryListener"}}, "split-brain-protection-ref": {"type": "string"}, "merge-policy": {"$ref": "#/definitions/MergePolicy"}, "statistics-enabled": {"type": "boolean", "default": true}}}}, "ReplicatedMap": {"type": "object", "additionalProperties": {"type": "object", "additionalProperties": false, "properties": {"in-memory-format": {"$ref": "#/definitions/InMemoryFormat", "default": "OBJECT"}, "async-fillup": {"type": "boolean", "default": true, "description": "Specifies whether the replicated map is available for reads before the initial replication is completed. If false, no Exception will be thrown when the replicated map is not yet ready, but call is blocked until the initial replication is completed."}, "statistics-enabled": {"type": "boolean", "default": true, "description": "When you enable it, you can retrieve replicated map entry statistics such as creation time, expiration time, number of hits, key, value, etc."}, "entry-listeners": {"type": "array", "items": {"$ref": "#/definitions/EntryListener"}}, "split-brain-protection-ref": {"type": "string"}, "merge-policy": {"$ref": "#/definitions/MergePolicy"}}}}, "Cache": {"type": "object", "additionalProperties": {"type": "object", "additionalProperties": false, "properties": {"key-type": {"$ref": "#/definitions/ClassNameObj"}, "value-type": {"$ref": "#/definitions/ClassNameObj"}, "statistics-enabled": {"type": "boolean", "default": false, "description": "When you enable it, you can retrieve cache entry statistics such as creation time, expiration time, number of hits, key, value, etc."}, "management-enabled": {"type": "boolean", "default": false, "description": "If set to true, JMX beans are enabled and collected statistics are provided. It doesn't automatically enable statistics collection."}, "read-through": {"type": "boolean", "default": false, "description": "If set to true, enables read-through behavior of the cache to an underlying configured javax.cache.integration.CacheLoader which is also known as lazy-loading."}, "write-through": {"type": "boolean", "default": false, "description": "If set to true, enables write-through behavior of the cache to an underlying configured javax.cache.integration.CacheWriter which passes any changed value to the external backend resource."}, "cache-loader-factory": {"description": "Fully qualified class name of the javax.cache.configuration.Factory implementation providing a javax.cache.integration.CacheLoader instance to the cache.", "$ref": "#/definitions/ClassNameObj"}, "cache-loader": {"type": "string", "description": "Name of the cache loader class."}, "cache-writer-factory": {"$ref": "#/definitions/ClassNameObj", "description": "Fully qualified class name of the javax.cache.configuration.Factory implementation providing a javax.cache.integration.CacheWriter instance to the cache."}, "cache-writer": {"type": "string", "description": "Name of the cache writer class."}, "expiry-policy-factory": {"anyOf": [{"$ref": "#/definitions/ClassNameObj", "description": "Fully qualified class name of the javax.cache.configuration.Factory implementation providing a javax.cache.expiry.ExpiryPolicy instance to the cache."}, {"type": "object", "additionalProperties": false, "properties": {"timed-expiry-policy-factory": {"type": "object", "additionalProperties": false, "properties": {"expiry-policy-type": {"enum": ["CREATED", "MODIFIED", "ACCESSED", "TOUCHED", "ETERNAL"]}, "duration-amount": {"type": "integer", "minimum": 0}, "time-unit": {"enum": ["NANOSECONDS", "MICROSECONDS", "MILLISECONDS", "SECONDS", "MINUTES", "HOURS", "DAYS"]}}}}, "required": ["timed-expiry-policy-factory"]}]}, "cache-entry-listeners": {"type": "array", "items": {"type": "object", "additionalProperties": false, "properties": {"old-value-required": {"type": "boolean", "default": false, "description": "If set to true, previously assigned values for the affected keys will be sent to the javax.cache.event.CacheEntryListener implementation. Setting this attribute to true creates additional traffic."}, "synchronous": {"type": "boolean", "default": false, "description": "If set to true, the javax.cache.event.CacheEntryListener implementation will be called in a synchronous manner."}, "cache-entry-listener-factory": {"$ref": "#/definitions/ClassNameObj", "description": "Fully qualified class name of the javax.cache.configuration.Factory implementation providing a javax.cache.event.CacheEntryListener instance."}, "cache-entry-event-filter-factory": {"$ref": "#/definitions/ClassNameObj", "description": "Fully qualified class name of the javax.cache.configuration.Factory implementation providing a javax.cache.event.CacheEntryEventFilter instance."}}}, "description": "List of cache entry listeners."}, "in-memory-format": {"$ref": "#/definitions/InMemoryFormat", "default": "BINARY"}, "backup-count": {"$ref": "#/definitions/BackupCount"}, "async-backup-count": {"$ref": "#/definitions/AsyncBackupCount"}, "eviction": {"$ref": "#/definitions/Eviction"}, "wan-replication-ref": {"type": "object", "additionalProperties": false, "properties": {"name": {"type": "string", "description": "Name of the WAN replication configuration"}, "republishing-enabled": {"type": "boolean", "default": true, "description": "When enabled, an incoming event to a member is forwarded to target cluster of that member. "}, "filters": {"type": "array", "items": {"type": "string"}, "description": "Filters to intercept WAN replication events before they are placed to WAN event replication queues by providing a filtering API. Just implement Hazelcast's CacheWanEventFilter interface to create your filters."}, "merge-policy-class-name": {"enum": ["HigherHitsMergePolicy", "PassThroughMergePolicy"], "description": "Resolve conflicts that occurred when target cluster already has the replicated entry key."}}}, "split-brain-protection-ref": {"type": "string", "description": "Adds the split brain protection for this cache. The value should be a \"split-brain-protection\"'s name."}, "partition-lost-listeners": {"type": "array", "items": {"type": "string"}, "description": "Adds the partition lost listeners that you created by implementing Hazelcast's PartitionLostListener interface."}, "merge-policy": {"$ref": "#/definitions/MergePolicy"}, "hot-restart": {"type": "object", "additionalProperties": false, "description": "Used to enable Hazelcast's Hot Restart Persistence feature for the cache. It is available only in Hazelcast Enterprise HD. Set its \"enabled\" to true to enable the feature. By default, it is disabled.", "properties": {"enabled": {"type": "boolean", "default": false}, "fsync": {"type": "boolean", "default": false, "description": "Set as true if the writing to disk should be followed by an fsync() system call."}}}, "data-persistence": {"type": "object", "additionalProperties": false, "description": "Used to enable Hazelcast's Persistence feature for the cache. It is available only in Hazelcast Enterprise HD. Set its \"enabled\" to true to enable the feature. By default, it is disabled.", "properties": {"enabled": {"type": "boolean", "default": false}, "fsync": {"type": "boolean", "default": false, "description": "Set as true if the writing to disk should be followed by an fsync() system call."}}}, "event-journal": {"type": "object", "additionalProperties": false, "properties": {"enabled": {"type": "boolean", "default": false}, "capacity": {"type": "integer", "minimum": 0, "default": 10000, "description": "The capacity of the event journal. The capacity is the total number of items that the event journal can hold at any moment. The actual number of items contained in the journal can be lower. The capacity is shared equally between all partitions. This is done by assigning each partition capacity / partitionCount available slots in the event journal. Because of this, the effective total capacity may be somewhat lower and you must take into account that the configured capacity is at least greater than the partition count."}, "time-to-live-seconds": {"type": "integer", "minimum": 0, "default": 0, "description": "Sets the time to live in seconds. Time to live is the time the event journal retains items before removing them from the journal. The events are removed on journal read and write actions, not while the journal is idle. Time to live can be disabled by setting timeToLiveSeconds to 0. This means that the events never expire but they can be overwritten when the capacity of the journal is exceeed. Any integer between 0 and Integer.MAX_VALUE. 0 means infinite."}}}, "merkle-tree": {"type": "object", "additionalProperties": false, "properties": {"enabled": {"type": "boolean", "default": false}, "depth": {"type": "integer", "minimum": 2, "maximum": 27, "default": 10}}}, "disable-per-entry-invalidation-events": {"type": "boolean", "default": false, "description": "Disables invalidation events for each entry; but full-flush invalidation events are still enabled. Full-flush invalidation means the invalidation of events for all entries when clear is called."}}}}, "ClassNameObj": {"type": "object", "additionalProperties": false, "properties": {"class-name": {"type": "string", "description": "Fully qualified class name."}}, "required": ["class-name"]}, "ClassNameWithProps": {"type": "object", "additionalProperties": false, "properties": {"class-name": {"type": "string", "description": "Fully qualified class name."}, "properties": {"type": "object"}}, "required": ["class-name"]}, "Metrics": {"type": "object", "allOf": [{"properties": {"management-center": {"type": "object", "description": "Defines the Hazelcast Management Center related metrics configuration.", "additionalProperties": false, "properties": {"enabled": {"type": "boolean", "default": true, "description": "Controls whether the metrics collected are exposed to Hazelcast Management Center. Please note that the metrics are polled by the Hazelcast Management Center, hence the members need to buffer the collected metrics between two polls. The aim for this switch is to reduce memory consumption of the metrics system if the Hazelcast Management Center is not used."}, "retention-seconds": {"type": "integer", "minimum": 1, "default": 5, "description": "Sets the number of seconds the metrics will be retained on the instance. More retention means more heap memory, but allows for longer client hiccups without losing a value (for example to restart the Management Center)."}}}}, "propertyNames": {"enum": ["enabled", "management-center", "jmx", "collection-frequency-seconds"]}}, {"$ref": "#/definitions/BaseMetrics"}]}, "ClientMetrics": {"allOf": [{"$ref": "#/definitions/BaseMetrics"}, {"propertyNames": {"enum": ["enabled", "jmx", "collection-frequency-seconds"]}}]}, "BaseMetrics": {"type": "object", "properties": {"enabled": {"type": "boolean", "default": true, "description": "The master-switch for the metrics collection. If this is set to false no metrics collection is done, regardless of the other settings."}, "jmx": {"type": "object", "description": "Defines the JMX related metrics configuration.", "additionalProperties": false, "properties": {"enabled": {"type": "boolean", "default": true, "description": "Controls whether the metrics collected are exposed to through JMX. It is enabled by default. In order to expose the metrics, the metrics system need to be enabled via the enabled master-switch attribute."}}}, "collection-frequency-seconds": {"type": "integer", "minimum": 1, "default": 5, "description": "Sets the metrics collection frequency in seconds."}}}, "LiteMember": {"type": "object", "additionalProperties": false, "properties": {"enabled": {"type": "boolean"}}, "required": ["enabled"], "description": "When you want to use a Hazelcast member as a lite member, set this element's \"enabled\" attribute to true in that member's YAML configuration. Lite members do not store data, do not have partitions and are used mainly to execute tasks and register listeners."}, "CPSubsystem": {"type": "object", "additionalProperties": false, "properties": {"cp-member-count": {"default": 0, "anyOf": [{"const": 0}, {"type": "integer", "minimum": 3}], "description": "Number of CP Members to initialize the CP Subsystem. The CP subsystem is enabled when a positive value is set. After the CP subsystem is initialized successfully, more CP members can be added at run-time and number of active CP members can go beyond the configured CP member count. Number of CP members can be smaller than total size of the Hazelcast cluster. For instance, you can run 5 CP members in a 20-member Hazelcast cluster. If set, must be greater than or equal to \"group-size\"."}, "group-size": {"anyOf": [{"const": 0}, {"type": "integer", "minimum": 3, "maximum": 7, "not": {"multipleOf": 2}}], "description": "Number of CP members to run CP groups. If set, it must be an odd number between 3 and 7. Otherwise, \"cp-member-count\" is respected. If set, must be smaller than or equal to \"cp-member-count\"."}, "session-time-to-live-seconds": {"type": "integer", "minimum": 1, "default": 300, "description": "Duration for a CP session to be kept alive after the last received heartbeat. The session will be closed if there is no new heartbeat this duration. Session TTL must be decided wisely. If a very low value is set, CP session of a Hazelcast instance can be closed prematurely if the instance temporarily loses connectivity to the CP subsystem because of a network partition or a GC pause. In such an occasion, all CP resources of this Hazelcast instance, such as FencedLock or ISemaphore, are released. On the other hand, if a very large value is set, CP resources can remain assigned to an actually crashed Hazelcast instance for too long and liveliness problems can occur. The CP subsystem offers an API, CPSessionManagementService, to deal with liveliness issues related to CP sessions. In order to prevent premature session expires, session TTL configuration can be set a relatively large value and CPSessionManagementService#forceCloseSession() can be manually called to close CP session of a crashed Hazelcast instance. Must be greater than \"session-heartbeat-interval-seconds\", and smaller than or equal to \"missing-cp-member-auto-removal-seconds\"."}, "session-heartbeat-interval-seconds": {"type": "integer", "minimum": 1, "default": 5, "description": "Interval for the periodically-committed CP session heartbeats. A CP session is started on a CP group with the first session-based request of a Hazelcast instance. After that moment, heartbeats are periodically committed to the CP group. Must be smaller than \"session-time-to-live-seconds\"."}, "missing-cp-member-auto-removal-seconds": {"type": "integer", "minimum": 0, "default": 14400, "description": "Duration to wait before automatically removing a missing CP member from the CP subsystem. When a CP member leaves the cluster, it is not automatically removed from the CP subsystem, since it could be still alive and left the cluster because of a network partition. On the other hand, if a missing CP member is actually crashed, it creates a danger for its CP groups, because it will be still part of majority calculations. This situation could lead to losing majority of CP groups if multiple CP members leave the cluster over time. With the default configuration, missing CP members will be automatically removed from the CP subsystem after 4 hours. This feature is very useful in terms of fault tolerance when CP member count is also configured to be larger than group size. In this case, a missing CP member will be safely replaced in its CP groups with other available CP members in the CP subsystem. This configuration also implies that no network partition is expected to be longer than the configured duration. Must be greater than or equal to \"session-time-to-live-seconds\". If a missing CP member comes back alive after it is automatically removed from the CP subsystem with this feature, that CP member must be terminated manually. The default is 4 hours."}, "fail-on-indeterminate-operation-state": {"type": "boolean", "default": false, "description": "Offers a choice between at-least-once and at-most-once execution of the operations on top of the Raft consensus algorithm. It is disabled by default and offers at-least-once execution guarantee. If enabled, it switches to at-most-once execution guarantee. When you invoke an API method on a CP data structure proxy, it replicates an internal operation to the corresponding CP group. After this operation is committed to majority of this CP group by the Raft leader node, it sends a response for the public API call. If a failure causes loss of the response, then the calling side cannot determine if the operation is committed on the CP group or not. In this case, if this configuration is disabled, the operation is replicated again to the CP group, and hence could be committed multiple times. If it is enabled, the public API call fails with com.hazelcast.core.IndeterminateOperationStateException"}, "persistence-enabled": {"type": "boolean", "default": false, "description": " Flag to denote whether or not CP Subsystem Persistence is enabled. If enabled, CP members persist their local CP data to stable storage and can recover from crashes."}, "base-dir": {"type": "string", "default": "cp-data", "description": "Base directory to store all CP data when persistence-enabled is true. This directory can be shared between multiple CP members. Each CP member creates a unique directory for itself under the base directory. This is especially useful for cloud environments where CP members generally use a shared filesystem."}, "data-load-timeout-seconds": {"type": "integer", "minimum": 1, "default": 120, "description": "Timeout duration for CP members to restore their data from disk. CP member fails its startup if it cannot complete its CP data restore rocess in the configured duration."}, "cp-member-priority": {"type": "integer", "default": 0, "description": "The CP member priority. The CP groups' leadership will be eventually transferred to members with higher priorities within the CP group."}, "raft-algorithm": {"type": "object", "additionalProperties": false, "properties": {"leader-election-timeout-in-millis": {"type": "integer", "default": 2000, "minimum": 1, "description": "Leader election timeout in milliseconds. If a candidate cannot win majority of the votes in time, a new election round is initiated."}, "leader-heartbeat-period-in-millis": {"type": "integer", "default": 5000, "minimum": 1, "description": "Period in milliseconds for a leader to send heartbeat messages to its followers."}, "max-missed-leader-heartbeat-count": {"type": "integer", "default": 5, "minimum": 1, "description": "Maximum number of missed leader heartbeats to trigger a new leader election."}, "append-request-max-entry-count": {"type": "integer", "default": 100, "minimum": 1, "description": "Maximum number of entries that can be sent in a single batch of append entries request."}, "commit-index-advance-count-to-snapshot": {"type": "integer", "default": 10000, "minimum": 1, "description": "Number of new commits to initiate a new snapshot after the last snapshot."}, "uncommitted-entry-count-to-reject-new-appends": {"type": "integer", "default": 100, "minimum": 1, "description": "Maximum number of uncommitted entries in the leader's Raft log before temporarily rejecting new requests of callers."}, "append-request-backoff-timeout-in-millis": {"type": "integer", "minimum": 1, "default": 100, "description": "Timeout in milliseconds for append request backoff. After the leader sends an append request to a follower, it will not send a subsequent append request until the follower responds to the former request or this timeout occurs."}}}, "semaphores": {"type": "object", "additionalProperties": {"type": "object", "additionalProperties": false, "properties": {"jdk-compatible": {"type": "boolean", "default": false, "description": " Enables / disables JDK compatibility of the CP ISemaphore. When it is JDK compatible, just as in the Semaphore#release() method, a permit can be released without acquiring it first, because acquired permits are not bound to threads. However, there is no auto-cleanup of acquired permits upon Hazelcast server / client failures. If a permit holder fails, its permits must be released manually. When JDK compatibility is disabled, a HazelcastInstance must acquire permits before releasing them and it cannot release a permit that it has mot acquired. It means, you can acquire a permit from one thread and release it from another thread using the same HazelcastInstance, but not different HazelcastInstances. In this mode, acquired permits are automatically released upon failure of the holder HazelcastInstance. So there is a minor behavioral difference to the Semaphore#release() method."}, "initial-permits": {"type": "integer", "minimum": 0, "default": 0, "description": "Number of permits to initialize the Semaphore. If a positive value is set, the Semaphore is initialized with the given number of permits."}}}, "description": "Configurations for CP Semaphore instances. The CP Semaphores can be configured with mappings under keys as their names."}, "locks": {"type": "object", "description": "Configurations for FencedLock instances. The FencedLocks can be configured with mappings under keys as their names.", "additionalProperties": {"type": "object", "additionalProperties": false, "properties": {"lock-acquire-limit": {"type": "integer", "default": 0, "minimum": 0, "description": " Maximum number of reentrant lock acquires. Once a caller acquires the lock this many times, it will not be able to acquire the lock again, until it makes at least one unlock() call. By default, no upper bound is set for the number of reentrant lock acquires, which means that once a caller acquires a FencedLock, all of its further lock() calls will succeed. However, for instance, if you set lock-acquire-limit to 2, once a caller acquires the lock, it will be able to acquire it once more, but its third lock() call will not succeed. If lock-acquire-limit is set to 1, then the lock becomes non-reentrant."}}}}}}, "BackupCount": {"description": "Number of synchronous backups. For example, if 1 is set as the backup count, then all entries of the map will be copied to another JVM for fail-safety. 0 means no sync backup. The sum of backup-count and async-backup-count can't be larger than than 6.", "type": "integer", "minimum": 0, "default": 1, "maximum": 6}, "AsyncBackupCount": {"description": "The number of asynchronous backups. 0 means no backups. The sum of backup-count and async-backup-count can't be larger than than 6.", "type": "integer", "minimum": 0, "default": 0, "maximum": 6}, "List": {"type": "object", "description": "Name-value pairs of list configurations", "additionalProperties": {"type": "object", "additionalProperties": false, "properties": {"statistics-enabled": {"type": "boolean", "default": true, "description": "When you enable it, you can retrieve list statistics."}, "max-size": {"type": "integer", "default": 0, "description": "Maximum size of the list (item count). 0 means Integer.MAX_VALUE."}, "backup-count": {"$ref": "#/definitions/BackupCount"}, "async-backup-count": {"$ref": "#/definitions/AsyncBackupCount"}, "item-listeners": {"type": "array", "items": {"$ref": "#/definitions/ItemListener"}}, "split-brain-protection-ref": {"type": "string", "description": "Adds the Split Brain Protection for this data-structure. You should set this value as a \"split-brain-protection\"'s name."}, "merge-policy": {"$ref": "#/definitions/MergePolicy"}}}}, "LdapSearchScope": {"enum": ["object", "one-level", "subtree"], "default": "subtree"}, "Security": {"type": "object", "additionalProperties": false, "properties": {"enabled": {"type": "boolean", "default": false, "description": "Set to true to enable Hazelcast's security features."}, "realms": {"type": "array", "description": "Defines set of named security realms. Security realms are named security configurations which can be referenced from Hazelcast security configuration. Currently, the realms support configuring \"authentication\", and/or \"identity\". Only one type of authentication configuration and one type of identity configuration is allowed per the realm.", "items": {"type": "object", "additionalProperties": false, "properties": {"name": {"type": "string"}, "authentication": {"type": "object", "propertyNames": {"enum": ["jaas", "tls", "ldap", "kerber<PERSON>", "simple"]}, "maxProperties": 1, "minProperties": 1, "properties": {"jaas": {"type": "array", "description": "Defines JAAS authentication - i.e. list of login-module descriptions and optional \"properties\"", "items": {"type": "object", "additionalProperties": false, "properties": {"class-name": {"type": "string"}, "usage": {"enum": ["REQUIRED", "REQUISITE", "SUFFICIENT", "OPTIONAL"], "default": "REQUIRED"}, "properties": {"type": "object"}}, "required": ["class-name"]}}, "tls": {"type": "object", "additionalProperties": false, "description": "TLS defines X.509 based authentication (for cases when SSL mutual authentication is configured in Hazelcast network configuration)", "properties": {"roleAttribute": {"type": "string"}}}, "ldap": {"type": "object", "additionalProperties": false, "properties": {"url": {"type": "string"}, "socket-factory-class-name": {"type": "string"}, "parse-dn": {"type": "boolean"}, "role-context": {"type": "string"}, "role-filter": {"type": "string"}, "role-mapping-attribute": {"type": "string"}, "role-mapping-mode": {"enum": ["attribute", "reverse", "direct"], "default": "reverse"}, "role-name-attribute": {"type": "string"}, "role-recursion-max-depth": {"type": "integer", "minimum": 0}, "role-search-scope": {"$ref": "#/definitions/LdapSearchScope"}, "user-name-attribute": {"type": "string"}, "system-user-dn": {"type": "string"}, "system-user-password": {"type": "string"}, "password-attribute": {"type": "string"}, "user-context": {"type": "string"}, "user-filter": {"type": "string"}, "user-search-scope": {"$ref": "#/definitions/LdapSearchScope"}, "system-authentication": {"type": "string"}, "security-realm": {"type": "string"}}}, "simple": {"type": "object", "additionalProperties": true, "properties": {"role-separator": {"type": "string"}, "users": {"type": "array", "items": {"additionalProperties": false, "properties": {"username": {"type": "string"}, "password": {"type": "string"}, "roles": {"type": "array", "items": {"type": "string"}}}, "required": ["username", "password"]}}}}}}, "identity": {"type": "object", "propertyNames": {"enum": ["username-password", "kerber<PERSON>", "token", "credentials-factory"]}, "minProperties": 1, "maxProperties": 1, "properties": {"username-password": {"$ref": "#/definitions/UsernamePasswordIdentity"}, "token": {"$ref": "#/definitions/TokenIdentity"}, "credentials-factory": {"$ref": "#/definitions/CredentialsFactoryIdentity"}}}}}}, "member-authentication": {"type": "object", "additionalProperties": false, "description": "Maps member authentication to a realm name.", "properties": {"realm": {"type": "string"}}, "required": ["realm"]}, "client-authentication": {"type": "object", "additionalProperties": false, "description": "Maps client authentication to a realm name.", "properties": {"realm": {"type": "string"}}, "required": ["realm"]}, "client-permission-policy": {"description": "Specifies the name and properties of your class that you developed by implementing Hazelcast's IPermissionPolicy interface, which is the default permission policy (com.hazelcast.security.IPermissionPolicy). This policy defines the client authorization specifications.", "$ref": "#/definitions/ClassNameWithProps"}, "security-interceptors": {"description": "Specifies the security interceptor class that you developed by implementing Hazelcast's SecurityInterceptor interface to intercept every remote operation executed by a client.", "type": "array", "items": {"type": "string"}}, "client-block-unmapped-actions": {"type": "boolean", "default": true, "description": "Specifies whether to block (true) or allow (false) actions, submitted as tasks in an Executor from the clients and have no permission mappings."}, "client-permissions": {"description": "Lists the client permissions that can be accessed by the client permission policy. For each permission, you need to provide the end points and actions to be permitted. Also, the name and principal for each permission should be given using the \"name\" and \"principal\" attributes. Please see http://docs.hazelcast.org/docs/latest/manual/html-single/index.html#permissions for all permissions and actions.", "type": "object", "propertyNames": {"enum": ["on-join-operation", "all", "config", "transaction", "map", "queue", "topic", "multimap", "list", "set", "flake-id-generator", "lock", "atomic-long", "countdown-latch", "semaphore", "executor-service", "durable-executor-service", "cardinality-estimator", "scheduled-executor", "cache", "user-code-deployment", "pn-counter", "atomic-reference", "ring-buffer", "reliable-topic", "replicatedmap", "management", "job", "connector", "sql"]}, "properties": {"on-join-operation": {"enum": ["RECEIVE", "SEND", "NONE"]}, "all": {"$ref": "#/definitions/SecurityPermission"}, "config": {"$ref": "#/definitions/SecurityPermission"}, "transaction": {"$ref": "#/definitions/SecurityPermission"}}, "additionalProperties": {"type": "array", "items": {"$ref": "#/definitions/SecurityPermission"}}}}}, "SecurityPermission": {"type": "object", "properties": {"name": {"type": "string"}, "principal": {"type": "string"}, "endpoints": {"type": "array", "items": {"type": "string"}}, "actions": {"anyOf": [{"type": "array", "items": {"$ref": "#/definitions/SecurityPermissionAction"}}, {"type": "object", "patternProperties": {".*": {"$ref": "#/definitions/SecurityPermissionAction"}}}]}}}, "SecurityPermissionAction": {"enum": ["all", "create", "destroy", "modify", "read", "remove", "lock", "listen", "release", "acquire", "put", "add", "index", "intercept", "publish", "aggregate", "projection", "deploy", "submit", "cancel", "restart", "export-snapshot", "add-resources", "write", "instance", "member", "migration"]}, "Capacity": {"type": "object", "properties": {"unit": {"enum": ["BYTES", "KILOBYTES", "MEGABYTES", "GIGABYTES"], "default": "MEGABYTES", "description": "Unit can be bytes, kilobytes, megabytes and gigabytes. Default unit is MEGABYTES."}, "value": {"type": "number", "default": 128, "minimum": 1, "description": "Default value is 128."}}}, "NativeMemory": {"description": "This feature is available only in Hazelcast Enterprise HD.", "properties": {"enabled": {"type": "boolean", "default": false, "description": "Set as true to enable the High-Density Memory Store usage."}, "allocator-type": {"enum": ["STANDARD", "POOLED"], "default": "POOLED", "description": "Type of the memory allocator. The default value is POOLED. Available values are as follows: \\n- STANDARD: Allocates/frees the memory using default OS memory manager, \\n- POOLED: Manages memory blocks in thread local pools"}, "size": {"$ref": "#/definitions/Capacity", "description": "Deprecated, use 'capacity' instead."}, "capacity": {"$ref": "#/definitions/Capacity"}, "min-block-size": {"type": "integer", "default": 16, "minimum": 1, "enum": [1, 2, 4, 6, 8, 16, 32, 64, 128, 256, 512, 1024, 2048, 4096, 8192, 16384, 32768, 65536], "description": "Minimum size of the blocks in bytes to split and fragment a page block to assign to an allocation request.\\n It is used only by the POOLED memory allocator. The value has to be power of two. Default value is 16."}, "page-size": {"type": "integer", "default": 4194304, "minimum": 1, "description": "Size of the page in bytes to allocate memory as a block. \\n It is used only by the POOLED memory allocator. Its default value is 1 << 22 (about 4 MB)"}, "metadata-space-percentage": {"type": "number", "default": 12.5, "minimum": 1, "description": "Percentage of the allocated native memory that is used for the metadata such as indexes, offsets, etc. \\n It is used only by the POOLED memory allocator. Its default value is 12.5."}, "persistent-memory-directory": {"type": "string", "description": "Specifies the directory where the non-volatile memory (e.g. Intel Optane) is mounted. \\n If this element is not defined, the RAM is used as a native memory. \\n This directory will be created automatically if it does not exist.\\n To avoid collisions, every member of the cluster will create its own subfolder to work with the non-volatile memory."}}}, "CrdtReplication": {"description": "Configures the replication mechanism for all CRDT implementations.\\n The CRDT states are replicated in rounds (the period is configurable) and \\n in each round the state is replicated up to the configured number of members", "type": "object", "additionalProperties": false, "properties": {"max-concurrent-replication-targets": {"description": "The maximum number of target members that we replicate the CRDT states to in one period. \\n A higher count will lead to states being disseminated more rapidly at the expense of burst-like behaviour - \\n one update to a CRDT will lead to a sudden burst in the number of replication messages in a short time interval.", "type": "integer", "minimum": 1, "default": 1}, "replication-period-millis": {"description": "The period between two replications of CRDT states in milliseconds. \\n A lower value will increase the speed at which changes are disseminated to other cluster members at the expense of burst-like behaviour - \\n less updates will be batched together in one replication message and \\n one update to a CRDT may cause a sudden burst of replication messages in a short time interval. \\n The value must be a positive non-null integer.", "type": "integer", "minimum": 1, "default": 1000}}}, "PNCounter": {"type": "object", "additionalProperties": {"type": "object", "additionalProperties": false, "properties": {"replica-count": {"description": "Number of replicas on which the CRDT state will be kept.", "type": "integer", "minimum": 1, "maximum": 2147483647, "default": 2147483647}, "split-brain-protection-ref": {"description": "Adds the Split Brain Protection for this data-structure which you configure using the \"split-brain-protection\" element.", "type": "string"}, "statistics-enabled": {"description": "When you enable it, you can retrieve the PN counter statistics.", "type": "boolean", "default": true}}}}, "Listeners": {"type": "array", "items": {"type": "string"}, "description": "Configuration used to register the listeners that you created by implementing Hazelcast's MembershipListener, DistributedObjectListener, MigrationListener and PartitionLostListener interfaces."}, "MemberAttributes": {"type": "object", "description": "You can define member attributes for your Hazelcast members to tag your members according to your business logic requirements. Configuration element's name is \"member-attributes\". You can list each member attribute by their name. For each attribute, you need to provide its type and value.", "additionalProperties": {"type": "object", "properties": {"type": {"type": "string"}, "value": true}, "required": ["value"]}}, "Serialization": {"type": "object", "additionalProperties": false, "properties": {"portable-version": {"type": "integer", "description": "Version of the portable serialization. Portable version is used to differentiate two of the same classes that have changes such as a new field or a new field type."}, "use-native-byte-order": {"type": "boolean", "description": "Set as true if you want to use the native byte order of the underlying platform.", "default": false}, "byte-order": {"enum": ["BIG_ENDIAN", "LITTLE_ENDIAN"], "description": "Specifies the byte order that the serialization will use.", "default": "BIG_ENDIAN"}, "enable-compression": {"type": "boolean", "default": false, "description": "Set as true to enable compression when default Java serialization is used. Its default value is false."}, "enable-shared-object": {"type": "boolean", "default": false, "description": "Set as true to enable shared object when default Java serialization is used."}, "allow-unsafe": {"type": "boolean", "default": false, "description": "Set as true to allow the usage of unsafe."}, "data-serializable-factories": {"description": "Lists your class implementations of Hazelcast's DataSerializableFactory. Each factory has the required", "type": "array", "items": {"$ref": "#/definitions/ClassWithFactoryId"}}, "portable-factories": {"description": "Lists your class implementations of Hazelcast's PortableFactory. Each factory has the required \"factory-id\" attribute that you should give as the ID of your factory.", "type": "array", "items": {"$ref": "#/definitions/ClassWithFactoryId"}}, "global-serializer": {"description": "Global serializer should be defined with \"global-serializer\" element. It has an optional boolean \"override-java-serialization\" attribute. If you set it as true, the Java serialization step will be handled by the global serializer, not by the Java Serializable or Externalizable", "type": "object", "additionalProperties": false, "properties": {"class-name": {"type": "string"}, "override-java-serialization": {"type": "boolean", "default": false, "description": "If you set it as true, the Java serialization step will be handled by the global serializer, not by the Java Serializable or Externalizable"}}, "required": ["class-name"]}, "serializers": {"description": "Lists the serializers (classes) that you implement using Hazelcast's StreamSerializer, ByteArraySerializer etc.", "type": "array", "items": {"type": "object", "additionalProperties": false, "properties": {"type-class": {"type": "string", "description": "The type of the class that will be serialized via this implementation."}, "class-name": {"type": "string", "description": "The class name of the serializer implementation."}}, "required": ["type-class", "class-name"]}}, "check-class-def-errors": {"type": "boolean", "default": true, "description": "If set to true, serialization system will check the class definitions error at the start of serialization process and throw a Serialization Exception with the error definition.s"}, "java-serialization-filter": {"type": "object", "additionalProperties": false, "description": "Allows to configure blacklisting and whitelisting for deserialized classes when Java serialization is used.", "properties": {"defaults-disabled": {"type": "boolean", "default": false}, "whitelist": {"$ref": "#/definitions/ClassFilter"}, "blacklist": {"$ref": "#/definitions/ClassFilter"}}}, "allow-override-default-serializers": {"type": "boolean", "default": false}, "compact-serialization": {"type": "object", "additionalProperties": false, "description": "Contains configuration elements and attributes for the compact serialization.", "properties": {"serializers": {"type": "array", "description": "Contains the Compact serializers to be registered.", "items": {"type": "object", "additionalProperties": false, "properties": {"serializer": {"type": "string", "description": "Fully qualified class name of the Compact serializer."}}}}, "classes": {"type": "array", "description": "Contains the classes to be serialized with Compact serialization, which will override any other serialization mechanisms.", "items": {"type": "object", "additionalProperties": false, "properties": {"class": {"type": "string", "description": "Fully qualified name of the class."}}}}}}}}, "ClassWithFactoryId": {"type": "object", "additionalProperties": false, "properties": {"factory-id": {"type": "integer"}, "class-name": {"type": "string"}}, "required": ["factory-id", "class-name"]}, "ClassFilter": {"type": "object", "additionalProperties": false, "properties": {"class": {"type": "array", "items": {"type": "string"}}, "package": {"type": "array", "items": {"type": "string"}}, "prefix": {"type": "array", "items": {"type": "string"}}}}, "SplitBrainProtection": {"type": "object", "additionalProperties": {"type": "object", "additionalProperties": false, "properties": {"enabled": {"type": "boolean", "default": false, "description": "Specifies whether this split brain protection is enabled."}, "minimum-cluster-size": {"type": "integer", "minimum": 2, "description": "The minimum number of members required in a cluster for the cluster to remain in an operational state. If the number of members is below the defined minimum at any time, the operations are rejected and the rejected operations return a SplitBrainProtectionException to their callers."}, "protect-on": {"enum": ["READ", "WRITE", "READ_WRITE"], "default": "READ_WRITE", "description": "Specifies for which operations the split brain protection will be applied. Available values are READ, WRITE and READ_WRITE."}, "function-class-name": {"type": "string", "description": "Name of the class that you develop by implementing Hazelcast's SplitBrainProtectionFunction interface to conclude the absence/presence of your split brain protection. This function is triggered when any change happens to the member list."}, "listeners": {"type": "array", "description": "Provides the list of split brain protection listeners that you can register to be notified about split brain protection results. Split brain protection listeners are local to the member that they are registered, so they receive only events occurred on that local member.", "items": {"type": "string"}}, "member-count-split-brain-protection": {"enabled": {"type": "boolean", "default": false}, "protect-on": {"enum": ["READ", "WRITE", "READ_WRITE"]}, "minimum-cluster-size": {"type": "integer"}, "member-count-split-brain-protection": {"type": "object"}}, "recently-active-split-brain-protection": {"type": "object", "additionalProperties": false, "properties": {"heartbeat-tolerance-millis": {"type": "integer", "description": "Overrides the hazelcast.max.no.heartbeat.seconds system property."}}}, "probabilistic-split-brain-protection": {"type": "object", "additionalProperties": false, "properties": {"acceptable-heartbeat-pause-millis": {"type": "integer", "description": "Default duration in milliseconds corresponding to number of potentially lost/delayed heartbeats that will be accepted before considering it to be an anomaly. Overrides the hazelcast.max.no.heartbeat.seconds system property."}, "suspicion-threshold": {"type": "number", "description": "Threshold for suspicion (��) level. A low threshold is prone to generate many wrong suspicions but ensures a quick detection in the event of a real crash. Conversely, a high threshold generates fewer mistakes but needs more time to detect actual crashes. Overrides the hazelcast.heartbeat.phiaccrual.failuredetector.threshold system property."}, "max-sample-size": {"type": "integer", "description": "Number of samples to use for calculations. Overrides the hazelcast.heartbeat.phiaccrual.failuredetector.sample.size system property."}, "min-std-deviation-millis": {"type": "integer", "description": "Minimum standard deviation (in milliseconds) to use for the normal distribution used when calculating phi. Overrides the hazelcast.heartbeat.phiaccrual.failuredetector.min.std.dev.millis system propery."}, "heartbeat-interval-millis": {"type": "integer", "description": "Value of heartbeat interval in milliseconds. Overrides the hazelcast.heartbeat.interval.seconds system property."}}}}}}, "HotRestartPersistence": {"type": "object", "additionalProperties": false, "properties": {"enabled": {"type": "boolean", "default": false}, "auto-remove-stale-data": {"type": "boolean", "default": true, "description": "Sets whether or not automatic removal of stale Hot Restart data is enabled. When a member terminates or crashes when cluster state is ACTIVE, remaining members redistributes data among themselves and data persisted on terminated member's storage becomes stale. That terminated member cannot rejoin the cluster without removing Hot Restart data. When auto-removal of stale Hot Restart data is enabled, while restarting that member, Hot Restart data is automatically removed and it joins the cluster as a completely new member. Otherwise, Hot Restart data should be removed manually."}, "base-dir": {"type": "string", "description": "Specifies the directory where the Hot Restart data will be stored. This directory will be created automatically if it does not exist.", "default": "hot-restart"}, "backup-dir": {"type": "string", "description": "Specifies the directory where the Hot backup data will be stored. If this element is not defined, hot backup will be disabled. If a directory is defined which does not exist, it will be created on first backup. To avoid clashing data on multiple backups, each backup has a unique sequence ID which determines the name of the directory which will contain all hot restart data. This unique directory is created as a subdirectory of the configured `backup-dir`."}, "parallelism": {"type": "integer", "minimum": 1, "default": 1, "description": "Level of parallelism in Hot Restart Persistence. There will be this many IO threads, each writing in parallel to its own files. During the Hot Restart procedure, this many IO threads will be reading the files and this many Rebuilder threads will be rebuilding the Hot Restart metadata."}, "validation-timeout-seconds": {"type": "integer", "minimum": 1, "default": 120, "description": " Validation timeout for the Hot Restart process when validating the cluster members expected to join and the partition table on the whole cluster."}, "data-load-timeout-seconds": {"type": "integer", "minimum": 1, "default": 900, "description": "Data load timeout for the Hot Restart process. All members in the cluster should finish restoring their local data before this timeout period."}, "cluster-data-recovery-policy": {"enum": ["FULL_RECOVERY_ONLY", "PARTIAL_RECOVERY_MOST_RECENT", "PARTIAL_RECOVERY_MOST_COMPLETE"], "default": "FULL_RECOVERY_ONLY", "description": " Specifies the data recovery policy that will be respected during Hot Restart cluster start. Valid values are:\\n FULL_RECOVERY_ONLY: Starts the cluster only when all expected members are present and correct. Otherwise, it fails. This is the default value.\\n PARTIAL_RECOVERY_MOST_RECENT: Starts the cluster with the members which have most up-to-date partition table and successfully restored their data. All other members will leave the cluster and force start themselves. If no member restores its data successfully, cluster start fails.\\n PARTIAL_RECOVERY_MOST_COMPLETE: Starts the cluster with the largest group of members which have the same partition table version and successfully restored their data. All other members will leave the cluster and force start themselves. If no member restores its data successfully, cluster start fails."}, "encryption-at-rest": {"type": "object", "description": "Specifies the (optional) encryption settings for Hot Restart persistence. It has the required attribute \"enabled\" to specify whether encryption is enabled. In addition to that, it specifies the symmetric cipher to use (such as AES/CBC/PKCS5Padding), the encryption salt, the key size for generating encryption keys and, in the \"secure-store\" sub-element, the configuration of the Secure Store that it will be used to retrieve the encryption keys.\\n \"keystore\": Provides integration with Java KeyStore. Specifies the path to the KeyStore file, the KeyStore type (such as PKCS12 or JCEKS), the KeyStore password, an (optional) alias for the current encryption key entry, and (optionally) a polling interval for checking for changes in the KeyStore.\\n \"vault\": Provides integration with HashiCorp Vault. Specifies the address of the Vault server, the (optional) secrets engine path, the secret path where the encryption key is stored, the access token, the (optional) namespace, the (optional) <ssl> configuration for HTTPS support, and an (optional) polling interval for checking for changes in Vault.", "properties": {"enabled": {"type": "boolean", "default": false}, "algorithm": {"type": "string", "description": "Symmetric encryption algorithm", "default": "AES/CBC/PKCS5Padding"}, "salt": {"type": "string", "default": "thesalt"}, "key-size": {"type": "integer"}, "secure-store": {"type": "object", "oneOf": [{"additionalProperties": false, "properties": {"keystore": {"description": "Provides integration with Java KeyStore. Specifies the path to the KeyStore file, the KeyStore type (such as PKCS12 or JCEKS), the KeyStore password, an (optional) alias for the current encryption key entry, and (optionally) a polling interval for checking for changes in the KeyStore.", "type": "object", "additionalProperties": false, "properties": {"path": {"type": "string"}, "type": {"type": "string", "default": "PKCS12", "description": "For information about standard keystore types see https://docs.oracle.com/javase/8/docs/technotes/guides/security/StandardNames.html#KeyStore"}, "password": {"type": "string", "description": "The keystore password"}, "current-key-alias": {"type": "string"}, "polling-interval": {"type": "integer", "minimum": 0, "default": 0, "description": "Interval (in seconds) for polling for changes in the KeyStore. 0 (default) means no polling"}}, "required": ["path", "password"]}}}, {"additionalProperties": false, "properties": {"vault": {"description": "Provides integration with HashiCorp Vault. Specifies the address of the Vault server, the (optional) secrets engine path, the secret path where the encryption key is stored, the access token, the (optional) namespace, the (optional) \"ssl\" configuration for HTTPS support, and an (optional) polling interval for checking for changes in Vault.", "type": "object", "additionalProperties": false, "properties": {"address": {"type": "string"}, "secret-path": {"type": "string"}, "token": {"type": "string"}, "polling-interval": {"type": "integer", "default": 0, "description": "Interval (in seconds) for polling for changes to the encryption key. 0 (default) means polling disabled."}, "ssl": {"type": "object", "additionalProperties": false, "properties": {"enabled": {"type": "boolean", "default": false}, "factory-class-name": {"type": "string", "examples": ["com.hazelcast.nio.ssl.BasicSSLContextFactory"]}, "properties": {"type": "object", "examples": [{"protocol": "TLS", "mutualAuthentication": "REQUIRED", "keyStore": "/opt/hazelcast.keystore", "keyStorePassword": "secret.97531", "keyStoreType": "JKS", "trustStore": "/opt/hazelcast.truststore", "trustStorePassword": "changeit", "trustStoreType": "JKS"}]}}}}, "required": ["address", "secret-path", "token"]}}}]}}}}}, "Persistence": {"type": "object", "additionalProperties": false, "properties": {"enabled": {"type": "boolean", "default": false}, "auto-remove-stale-data": {"type": "boolean", "default": true, "description": "Sets whether or not automatic removal of stale Hot Restart data is enabled. When a member terminates or crashes when cluster state is ACTIVE, remaining members redistributes data among themselves and data persisted on terminated member's storage becomes stale. That terminated member cannot rejoin the cluster without removing Hot Restart data. When auto-removal of stale Hot Restart data is enabled, while restarting that member, Hot Restart data is automatically removed and it joins the cluster as a completely new member. Otherwise, Hot Restart data should be removed manually."}, "base-dir": {"type": "string", "description": "Specifies the directory where the Persistence data will be stored. This directory will be created automatically if it does not exist.", "default": "persistence"}, "backup-dir": {"type": "string", "description": "Specifies the directory where the Persistence backup data will be stored. If this element is not defined, backup will be disabled. If a directory is defined which does not exist, it will be created on first backup. To avoid clashing data on multiple backups, each backup has a unique sequence ID which determines the name of the directory which will contain all persistence data. This unique directory is created as a subdirectory of the configured `backup-dir`."}, "parallelism": {"type": "integer", "minimum": 1, "default": 1, "description": "Level of parallelism in Persistence. There will be this many IO threads, each writing in parallel to its own files. During the restart procedure, this many IO threads will be reading the files and this many Rebuilder threads will be rebuilding the Persistence metadata."}, "validation-timeout-seconds": {"type": "integer", "minimum": 1, "default": 120, "description": " Validation timeout for the restart process when validating the cluster members expected to join and the partition table on the whole cluster."}, "data-load-timeout-seconds": {"type": "integer", "minimum": 1, "default": 900, "description": "Data load timeout for the restart process. All members in the cluster should finish restoring their local data before this timeout period."}, "cluster-data-recovery-policy": {"enum": ["FULL_RECOVERY_ONLY", "PARTIAL_RECOVERY_MOST_RECENT", "PARTIAL_RECOVERY_MOST_COMPLETE"], "default": "FULL_RECOVERY_ONLY", "description": " Specifies the data recovery policy that will be respected during restart cluster start. Valid values are:\\n FULL_RECOVERY_ONLY: Starts the cluster only when all expected members are present and correct. Otherwise, it fails. This is the default value.\\n PARTIAL_RECOVERY_MOST_RECENT: Starts the cluster with the members which have most up-to-date partition table and successfully restored their data. All other members will leave the cluster and force start themselves. If no member restores its data successfully, cluster start fails.\\n PARTIAL_RECOVERY_MOST_COMPLETE: Starts the cluster with the largest group of members which have the same partition table version and successfully restored their data. All other members will leave the cluster and force start themselves. If no member restores its data successfully, cluster start fails."}, "encryption-at-rest": {"type": "object", "description": "Specifies the (optional) encryption settings for persistence. It has the required attribute \"enabled\" to specify whether encryption is enabled. In addition to that, it specifies the symmetric cipher to use (such as AES/CBC/PKCS5Padding), the encryption salt, the key size for generating encryption keys and, in the \"secure-store\" sub-element, the configuration of the Secure Store that it will be used to retrieve the encryption keys.\\n \"keystore\": Provides integration with Java KeyStore. Specifies the path to the KeyStore file, the KeyStore type (such as PKCS12 or JCEKS), the KeyStore password, an (optional) alias for the current encryption key entry, and (optionally) a polling interval for checking for changes in the KeyStore.\\n \"vault\": Provides integration with HashiCorp Vault. Specifies the address of the Vault server, the (optional) secrets engine path, the secret path where the encryption key is stored, the access token, the (optional) namespace, the (optional) <ssl> configuration for HTTPS support, and an (optional) polling interval for checking for changes in Vault.", "properties": {"enabled": {"type": "boolean", "default": false}, "algorithm": {"type": "string", "description": "Symmetric encryption algorithm", "default": "AES/CBC/PKCS5Padding"}, "salt": {"type": "string", "default": "thesalt"}, "key-size": {"type": "integer"}, "secure-store": {"type": "object", "oneOf": [{"additionalProperties": false, "properties": {"keystore": {"description": "Provides integration with Java KeyStore. Specifies the path to the KeyStore file, the KeyStore type (such as PKCS12 or JCEKS), the KeyStore password, an (optional) alias for the current encryption key entry, and (optionally) a polling interval for checking for changes in the KeyStore.", "type": "object", "additionalProperties": false, "properties": {"path": {"type": "string"}, "type": {"type": "string", "default": "PKCS12", "description": "For information about standard keystore types see https://docs.oracle.com/javase/8/docs/technotes/guides/security/StandardNames.html#KeyStore"}, "password": {"type": "string", "description": "The keystore password"}, "current-key-alias": {"type": "string"}, "polling-interval": {"type": "integer", "minimum": 0, "default": 0, "description": "Interval (in seconds) for polling for changes in the KeyStore. 0 (default) means no polling"}}, "required": ["path", "password"]}}}, {"additionalProperties": false, "properties": {"vault": {"description": "Provides integration with HashiCorp Vault. Specifies the address of the Vault server, the (optional) secrets engine path, the secret path where the encryption key is stored, the access token, the (optional) namespace, the (optional) \"ssl\" configuration for HTTPS support, and an (optional) polling interval for checking for changes in Vault.", "type": "object", "additionalProperties": false, "properties": {"address": {"type": "string"}, "secret-path": {"type": "string"}, "token": {"type": "string"}, "polling-interval": {"type": "integer", "default": 0, "description": "Interval (in seconds) for polling for changes to the encryption key. 0 (default) means polling disabled."}, "ssl": {"type": "object", "additionalProperties": false, "properties": {"enabled": {"type": "boolean", "default": false}, "factory-class-name": {"type": "string", "examples": ["com.hazelcast.nio.ssl.BasicSSLContextFactory"]}, "properties": {"type": "object", "examples": [{"protocol": "TLS", "mutualAuthentication": "REQUIRED", "keyStore": "/opt/hazelcast.keystore", "keyStorePassword": "secret.97531", "keyStoreType": "JKS", "trustStore": "/opt/hazelcast.truststore", "trustStorePassword": "changeit", "trustStoreType": "JKS"}]}}}}, "required": ["address", "secret-path", "token"]}}}]}}}, "rebalance-delay-seconds": {"type": "integer", "minimum": 0, "default": 0, "description": "Time (in seconds) to wait before triggering automatic partition rebalancing after a member leaves the cluster unexpectedly. Unexpectedly in this context means that a member leaves the cluster by means other than graceful shutdown: programmatic termination (eg LifecycleService.terminate()), a process crash or network partition. Default is 0, which means rebalancing is triggered immediately. Setting this to a higher value will allow some time for members that are gone to rejoin the cluster. The benefit is that partition rebalancing in this case will be avoided, saving the burden of migrating partition data over the network. Do not use this option if your cluster also stores in-memory data. This option stops the cluster from migrating in-memory data. As a result any data that is not persisted will be lost if the member restarts within the configured delay, including backups. While members are gone, operations on partitions for which the owner is missing may fail immediately or will be retried until the member rejoins or operation timeout is exceeded. Notice that this delay only applies when cluster members leave the cluster; when the cluster is being scaled up and members are being added, partition rebalancing will be triggered immediately (subject to limitations imposed by the current cluster state)."}}}, "LocalDevice": {"type": "object", "additionalProperties": {"type": "object", "additionalProperties": false, "properties": {"base-dir": {"type": "string", "default": "tiered-store", "description": "Device base directory that will be used for tiered-store."}, "capacity": {"$ref": "#/definitions/Capacity", "description": "Device capacity, i.e., what is the soft upper bound capacity of the device. The default is 256 gigabytes."}, "block-size": {"type": "integer", "minimum": 512, "default": 4096, "description": "Device block/sector size in bytes. The default is 4096. The minimum is 512."}, "read-io-thread-count": {"type": "integer", "minimum": 1, "default": 4, "description": "Read IO thread count. The default is 4."}, "write-io-thread-count": {"type": "integer", "minimum": 1, "default": 4, "description": "Write IO thread count. The default is 4."}}}}, "DynamicConfiguration": {"type": "object", "additionalProperties": false, "properties": {"persistence-enabled": {"type": "boolean", "description": "True if dynamic configuration persistence is enabled."}, "backup-dir": {"type": "string", "description": "Directory for dynamic configuration persistence file backups. Each new backup will be created inside this directory. Can be an absolute or relative path to the node startup directory. If not set, new folder will be created at the node startup directory."}, "backup-count": {"type": "integer", "minimum": 0, "description": "How many backups will be kept. Default is 5. You can set this to zero to disable backups."}}}, "SocketEndpointConfig": {"allOf": [{"$ref": "#/definitions/WanEndpointConfig"}, {"type": "object", "properties": {"port": {"type": "object", "description": "The ports which Hazelcast will use to communicate between cluster members.", "additionalProperties": false, "properties": {"port": {"type": "integer", "default": 5701}, "port-count": {"type": "integer", "default": 100, "description": "The default value is 100, meaning that Hazelcast will try to bind 100 ports. If you set the value of port as 5701, as members join the cluster, Hazelcast tries to find ports between 5701 and 5801. You can change the port count in cases like having large instances on a single machine or you are willing to have only a few ports assigned."}, "auto-increment": {"type": "boolean", "default": true, "description": "If port is set to 5701, Hazelcast will try to find free ports between 5701 and 5801. Normally, you will not need to change this value, but it comes in handy when needed. You may also want to choose to use only one port. In that case, you can disable the auto-increment feature of port by setting its value as false."}}}, "public-address": {"type": "string", "description": "Overrides the public address of a node. By default, a node selects its socket address as its public address. But behind a network address translation (NAT), two endpoints (nodes) may not be able to see/access each other. If both nodes set their public addresses to their defined addresses on NAT, then they can communicate with each other. In this case, their public addresses are not an address of a local network interface but a virtual address defined by NAT. This is optional to set and useful when you have a private cloud."}, "reuse-address": {"type": "boolean", "default": false, "description": "When you shutdown a cluster member, the server socket port will be in the TIME_WAIT state for the next couple of minutes. If you start the member right after shutting it down, you may not be able to bind it to the same port because it is in the TIME_WAIT state. If you set reuse-address to true, the TIME_WAIT state is ignored and you can bind the member to the same port again."}}}]}, "WanEndpointConfig": {"properties": {"name": {"type": "string", "description": "Name of the endpoint configuration. Only relevant when defining WAN server sockets."}, "outbound-ports": {"$ref": "#/definitions/OutboundPorts"}, "interfaces": {"type": "object", "additionalProperties": false, "description": "Specifies which network interfaces Hazelcast should use. You need to set its \"enabled\" sub-element to true to be able to use your defined interfaces. By default, it is disabled. You can define multiple interfaces using a nested \"interfaces\" sequence.", "properties": {"enabled": {"type": "boolean", "default": false}, "interfaces": {"type": "array", "items": {"type": "string"}}}}, "ssl": {"type": "object", "additionalProperties": false, "description": "Lets you configure SSL using the SSL context factory. This feature is available only in Hazelcast Enterprise. To be able to use it, encryption should NOT be enabled and you should first implement your SSLContextFactory class. Its configuration contains the factory class and SSL properties.", "properties": {"enabled": {"type": "boolean", "default": false}, "factory-class-name": {"type": "string"}, "properties": {"type": "object"}}}, "socket-interceptor": {"type": "object", "additionalProperties": false, "description": "Lets you add custom hooks to join and perform connection procedures (like identity checking using Kerberos, etc.). This feature is available only in Hazelcast Enterprise. To be able to use it, you should first implement the MemberSocketInterceptor (for members joining to a cluster) or SocketInterceptor (for clients connecting to a member) class. Its configuration contains the class you implemented and socket interceptor properties.", "properties": {"enabled": {"type": "boolean", "default": false}, "class-name": {"type": "string"}, "properties": {"type": "object"}}}, "symmetric-encryption": {"type": "object", "additionalProperties": false, "description": "Lets you encrypt the entire socket level communication among all Hazelcast members. This feature is available only in Hazelcast Enterprise.  Its configuration contains the encryption properties and the same configuration must be placed to all members.", "properties": {"enabled": {"type": "boolean", "default": false}, "algorithm": {"type": "string", "default": "PBEWithMD5AndDES"}, "salt": {"type": "string", "default": "thesalt"}, "password": {"type": "string", "default": "thepassword"}, "iteration-count": {"type": "integer", "minimum": 0, "default": 19}}}, "socket-options": {"$ref": "#/definitions/SocketOptions"}}}, "SocketOptions": {"type": "object", "additionalProperties": false, "properties": {"buffer-direct": {"type": "boolean", "default": false, "description": "Specifies whether direct or non-direct buffers should be allocated for the socket."}, "tcp-no-delay": {"type": "boolean", "default": true, "description": "Specifies whether <PERSON><PERSON>'s algorithm should be disabled. The default value is true meaning that <PERSON><PERSON>'s algorithm is disabled."}, "keep-alive": {"type": "boolean", "default": true, "description": "Specifies whether TCP keep-alive should be enabled."}, "connect-timeout-seconds": {"type": "integer", "minimum": 0, "default": 0, "description": "Specifies the TCP timeout in seconds."}, "send-buffer-size-kb": {"type": "integer", "minimum": 0, "default": 128, "description": "Specifies the size of the send buffer."}, "receive-buffer-size-kb": {"type": "integer", "default": 128, "description": "Specifies the size of the receive buffer."}, "linger-seconds": {"type": "integer", "default": 0, "description": "Specifies the TCP linger seconds."}}}, "WanReplication": {"type": "object", "additionalProperties": {"type": "object", "additionalProperties": false, "properties": {"batch-publisher": {"type": "object", "additionalProperties": {"type": "object", "additionalProperties": false, "properties": {"cluster-name": {"type": "string", "description": "Sets the cluster name used as an endpoint cluster name for authentication on the target endpoint. If there is no separate publisher ID property defined, this cluster name will also be used as a WAN publisher ID. This ID is then used for identifying the publisher in a WanReplicationConfig."}, "batch-size": {"type": "integer", "default": 500, "description": "Changes the maximum size of events that are sent to the target cluster in a single batch. The batch of events is not sent until this size is reached."}, "batch-max-delay-millis": {"type": "integer", "default": 1000, "description": " If the number of events generated does not reach the \"batch-size\", they are sent to the target cluster after a certain amount of time is passed. You can set this time in milliseconds using this element."}, "response-timeout-millis": {"type": "integer", "default": 60000, "description": " After a replication event is sent to the target cluster, the source member waits for a confirmation that says the event has reached the target. If confirmation is not received for a period of `response-timeout-millis`, the event is resent to the target cluster."}, "acknowledge-type": {"enum": ["ACK_ON_RECEIPT", "ACK_ON_OPERATION_COMPLETE"], "default": "ACK_ON_OPERATION_COMPLETE", "description": "Acknowledgment type for each target cluster when the events are replicated. You can set it to the following values: \\n * ACK_ON_RECEIPT: Guarantees that events are received by the target cluster. It does not guarantee that the received event is actually applied, but it is faster. \\n * ACK_ON_OPERATION_COMPLETE (default): Guarantees that the event is both received and applied by the target cluster. It is more time consuming, but it is the best way if you have strong consistency requirements."}, "initial-publisher-state": {"enum": ["REPLICATING", "PAUSED", "STOPPED"], "default": "REPLICATING", "description": "Defines the initial state in which a WAN publisher is started. \\n * REPLICATING (default): State where both enqueuing new events is allowed, enqueued events are replicated to the target cluster and WAN sync is enabled. \\n * PAUSED: State where new events are enqueued but they not are dequeued. Some events which have been dequeued before the state was switched may still be replicated to the target cluster but further events will not be replicated. WAN sync is enabled. \\n * STOPPED:  State where neither new events are enqueued nor dequeued. As with the PAUSED state, some events might still be replicated after the publisher has switched to this state. WAN sync is enabled."}, "snapshot-enabled": {"type": "boolean", "default": false, "description": "Sets if key-based coalescing is configured for this WAN publisher. When enabled, only the latest WanReplicationEvent of a key is sent to target."}, "idle-max-park-ns": {"type": "integer", "default": 250000000, "description": "Sets the maximum duration in nanoseconds that the WAN replication thread will be parked if there are no events to replicate."}, "idle-min-park-ns": {"type": "integer", "default": 10000000, "description": " Sets the minimum duration in nanoseconds that the WAN replication thread will be parked if there are no events to replicate."}, "max-concurrent-invocations": {"type": "integer", "default": -1, "description": "Sets the maximum number of WAN event batches being sent to the target cluster concurrently. Setting this property to anything less than 2 will only allow a single batch of events to be sent to each target endpoint and will maintain causality of events for a single partition. Setting this property to 2 or higher will allow multiple batches of WAN events to be sent to each target endpoint. Since this allows reordering or batches due to network conditions, causality and ordering of events for a single partition is lost and batches for a single partition are now sent randomly to any available target endpoint. This, however, does present faster WAN replication for certain scenarios such as replicating immutable, independent map entries which are only added once and where ordering of when these entries are added is not necessary. Keep in mind that if you set this property to a value which is less than the target endpoint count, you will lose performance as not all target endpoints will be used at any point in time to process WAN event batches. So, for instance, if you have a target cluster with 3 members (target endpoints) and you want to use this property, it makes sense to set it to a value higher than 3. Otherwise, you can simply disable it by setting it to less than 2 in which case WAN will use the default replication strategy and adapt to the target endpoint count while maintaining causality."}, "discovery-period-seconds": {"type": "integer", "default": 10, "description": " Sets the period in seconds in which WAN tries to discover new target endpoints and reestablish connections to failed endpoints."}, "use-endpoint-private-address": {"type": "boolean", "default": false, "description": " Sets whether the WAN connection manager should connect to the endpoint on the private address returned by the discovery SPI. By default this property is false which means the WAN connection manager will always use the public address."}, "queue-full-behavior": {"enum": ["DISCARD_AFTER_MUTATION", "THROW_EXCEPTION", "THROW_EXCEPTION_ONLY_IF_REPLICATION_ACTIVE"], "default": "DISCARD_AFTER_MUTATION", "description": "Policy to be applied when WAN Replication event queues are full. You can set it to the following values: \\n - DISCARD_AFTER_MUTATION (default): The new WAN events generated are dropped and not replicated to the target cluster."}, "properties": {"type": "object"}, "max-target-endpoints": {"type": "integer", "default": 2147483647, "description": "The maximum number of endpoints that WAN will connect to when using a discovery mechanism to define endpoints. This property has no effect when static endpoint addresses are defined using target-endpoints."}, "queue-capacity": {"type": "integer", "default": 10000, "description": " Size of the queue of events. If you exceed this queue size, then the oldest, not yet replicated updates might get lost. Therefore, if you have a large rate of put/update/remove operations, you should increase queue capacity."}, "target-endpoints": {"type": "string", "description": "Comma separated list of IP addresses of the target cluster members for which the WAN replication is implemented."}, "endpoint": {"type": "string"}, "sync": {"type": "object", "additionalProperties": false, "properties": {"consistency-check-strategy": {"enum": ["NONE", "MERKLE_TREES"], "description": "Sets the strategy for checking consistency of data between source and target cluster. Any inconsistency will not be reconciled, it will be merely reported via the usual mechanisms (e.g. statistics, diagnostics). The user must initiate WAN sync to reconcile there differences. For the check procedure to work properly, the target cluster should support the chosen strategy."}}}, "aws": {"$ref": "#/definitions/AwsDiscovery"}, "gcp": {"$ref": "#/definitions/GcpDiscovery"}, "azure": {"$ref": "#/definitions/AzureDiscovery"}, "kubernetes": {"$ref": "#/definitions/KubernetesDiscovery"}, "eureka": {"$ref": "#/definitions/EurekaDiscovery"}, "discovery-strategies": {"$ref": "#/definitions/DiscoveryStrategies"}}}}, "custom-publisher": {"type": "object", "additionalProperties": {"$ref": "#/definitions/ClassNameWithProps"}}, "consumer": {"type": "object", "additionalProperties": false, "description": "Config for processing WAN events received from a target cluster. You can configure certain behaviour when processing incoming WAN events or even configure your own implementation for a WAN consumer. A custom WAN consumer allows you to define custom processing logic and is usually used in combination with a custom WAN publisher. A custom consumer is optional and you may simply omit defining it which will cause the default processing logic to be used.", "properties": {"class-name": {"type": "string", "description": "Sets the fully qualified class name of the class implementing a custom WAN consumer (WanConsumer). If you don't define a class name, the default processing logic for incoming WAN events will be used."}, "properties": {"type": "object", "description": "Properties for the custom WAN consumer. These properties are accessible when initalizing the WAN consumer."}, "persist-wan-replicated-data": {"type": "boolean", "default": false, "description": "When true, an incoming event over WAN replication can be persisted to a database for example, otherwise it will not be persisted. Default value is true."}}}}}}, "UserCodeDeployment": {"type": "object", "additionalProperties": false, "properties": {"enabled": {"type": "boolean", "default": false}, "class-cache-mode": {"enum": ["ETERNAL", "OFF"], "default": "ETERNAL", "description": " Controls the local caching behavior for the classes loaded from the remote class repository. Available values are as follows:\\n * ETERNAL: Cache the loaded classes locally. This is the default value and suitable when you load long-living objects, such as domain objects stored in a map. \\n * OFF: Do not cache the loaded classes locally."}, "provider-mode": {"enum": ["OFF", "LOCAL_CLASSES_ONLY", "LOCAL_AND_CACHED_CLASSES"], "default": "LOCAL_AND_CACHED_CLASSES", "description": "Controls how the classes are served to the other cluster members. Available values are as follows: \\n * LOCAL_AND_CACHED_CLASSES: Serve classes loaded from both local classpath and from other members. This is the default value. \\n * LOCAL_CLASSES_ONLY: Serve classes from the local classpath only. Classes loaded from other members will be used locally, but they are not served to other members. \\n * OFF: Never serve classes to other members."}, "blacklist-prefixes": {"type": "string", "description": "Comma separated name prefixes of classes/packages to be prevented from dynamic class loading. For example, if you set it as \"com.foo\", remote loading of all classes from the \"com.foo\" package will be blacklisted, including the classes from all its sub-packages. If you set it as \"com.foo.Class\", then the \"Class\" and all classes having the \"Class\" as prefix in the \"com.foo\" package will be blacklisted."}, "whitelist-prefixes": {"type": "string", "description": "Comma separated name prefixes of classes/packages only from which the classes will be loaded. It allows to quickly configure remote loading only for classes from selected packages. It can be used together with blacklisting. For example, you can whitelist the prefix \"com.foo\" and blacklist the prefix \"com.foo.secret\"."}, "provider-filter": {"type": "string", "description": "Filter to constraint members to be used for a class loading request when a class is not available locally. The value is in the format \"HAS_ATTRIBUTE:foo\". When it is set as \"HAS_ATTRIBUTE:foo\", the class loading request will only be sent to the members which have \"foo\" as a member attribute."}}}, "Sql": {"type": "object", "additionalProperties": false, "properties": {"statement-timeout-millis": {"type": "integer", "default": 0, "description": "Defines the timeout in milliseconds that is applied to queries without an explicit timeout."}}}, "Auditlog": {"type": "object", "additionalProperties": false, "properties": {"enabled": {"type": "boolean", "default": false}, "factory-class-name": {"type": "string"}, "properties": {"type": "object"}}}, "InstanceTracking": {"type": "object", "additionalProperties": false, "properties": {"enabled": {"type": "boolean", "default": false}, "file-name": {"type": "string"}, "format-pattern": {"type": "string"}}}, "ClientSql": {"type": "object", "additionalProperties": false, "properties": {"resubmission-mode": {"enum": ["NEVER", "RETRY_SELECTS", "RETRY_SELECTS_ALLOW_DUPLICATES", "RETRY_ALL"], "default": "NEVER", "description": "The configuration when to retry query that fails with reasons CONNECTION_PROBLEM, PARTITION_DISTRIBUTION, TOPOLOGY_CHANGE."}}}, "OutboundPortDefinition": {"anyOf": [{"type": "integer", "minimum": 0, "maximum": 65536}, {"type": "string"}]}, "OutboundPorts": {"description": "By default, Hazelcast lets the system pick up an ephemeral port during socket bind operation. But security policies/firewalls may require you to restrict outbound ports to be used by Hazelcast-enabled applications. You can specify these ports under the element \"outbound-ports\". You can give a single port number, comma separated multiple ports or port ranges.", "anyOf": [{"type": "array", "items": {"$ref": "#/definitions/OutboundPortDefinition"}}, {"type": "object", "additionalProperties": {"$ref": "#/definitions/OutboundPortDefinition"}}]}, "Jet": {"type": "object", "additionalProperties": false, "properties": {"enabled": {"type": "boolean", "default": false}, "resource-upload-enabled": {"type": "boolean", "default": false}, "instance": {"type": "object", "additionalProperties": false, "properties": {"cooperative-thread-count": {"type": "integer", "description": "the number of threads each cluster member will use to execute Jet jobs. This refers only to threads executing `cooperative` processors; each `blocking` processor is assigned its own thread. By default it is the same as the number of available processors."}, "flow-control-period": {"type": "integer", "default": 100, "description": "While executing a Jet job there is the issue of regulating the rate at which one member of the cluster sends data to another member. The receiver will regularly report to each sender how much more data it is allowed to send over a given DAG edge. This property sets the length (in milliseconds) of the interval between flow-control (\"ack\") packets."}, "backup-count": {"type": "integer", "default": 1, "maximum": 6, "description": "Sets the number of backups that <PERSON> will maintain for the job metadata and snapshots. Each backup is on another cluster member; all backup write operations must complete before the overall write operation can complete. The maximum allowed number of backups is 6 and the default is 1. For example, if you set the backup count to 2, <PERSON> will replicate all the job metadata and snapshot data to two other members. If one or two members of the cluster fail, <PERSON> can recover the data from the snapshot and continue executing the job on the remaining members without loss."}, "scale-up-delay-millis": {"type": "integer", "default": 10000, "description": "Sets the delay after which auto-scaled jobs will restart if a new member is added to the cluster. Has no effect on jobs with auto scaling disabled."}, "lossless-restart-enabled": {"type": "boolean", "default": false, "description": "Sets whether lossless job restart is enabled for the node. With lossless restart you can restart the whole cluster without losing the jobs and their state. The feature is implemented on top of the Hot Restart feature of Hazelcast which persists the data to disk. If enabled, you have to also configure Hot Restart. Note: the snapshots exported using `Job#exportSnapshot` will also have Hot Restart storage enabled. Feature is disabled by default. If you enable this option in open-source, the member will fail to start, you need Enterprise to run it and obtain a license from Hazelcast."}, "max-processor-accumulated-records": {"type": "integer", "default": 9223372036854775807, "description": "Specifies the maximum number of records that can be accumulated by any single processor instance. Operations like grouping, sorting or joining require certain amount of records to be accumulated before they can proceed. You can set this option to reduce the probability of `OutOfMemoryError`. It applies to each processor instance separately, hence the effective limit of records accumulated by each cluster member is influenced by the vertex's `localParallelism` and the number of jobs in the cluster. Currently, this option limits: number of items sorted by the sort operation, number of distinct keys accumulated by aggregation operations, number of entries in each hash-join lookup table, number of entries in stateful transforms and number of distinct items in distinct operation. The limit does not apply to streaming aggregations."}}, "description": "Deprecated, use `/hazelcast/jet` to configure the fields directly. General configuration options pertaining to a Jet instance."}, "cooperative-thread-count": {"type": "integer", "description": "the number of threads each cluster member will use to execute Jet jobs. This refers only to threads executing `cooperative` processors; each `blocking` processor is assigned its own thread. By default it is the same as the number of available processors."}, "flow-control-period": {"type": "integer", "default": 100, "description": "While executing a Jet job there is the issue of regulating the rate at which one member of the cluster sends data to another member. The receiver will regularly report to each sender how much more data it is allowed to send over a given DAG edge. This property sets the length (in milliseconds) of the interval between flow-control (\"ack\") packets."}, "backup-count": {"type": "integer", "default": 1, "maximum": 6, "description": "Sets the number of backups that <PERSON> will maintain for the job metadata and snapshots. Each backup is on another cluster member; all backup write operations must complete before the overall write operation can complete. The maximum allowed number of backups is 6 and the default is 1. For example, if you set the backup count to 2, <PERSON> will replicate all the job metadata and snapshot data to two other members. If one or two members of the cluster fail, <PERSON> can recover the data from the snapshot and continue executing the job on the remaining members without loss."}, "scale-up-delay-millis": {"type": "integer", "default": 10000, "description": "Sets the delay after which auto-scaled jobs will restart if a new member is added to the cluster. Has no effect on jobs with auto scaling disabled."}, "lossless-restart-enabled": {"type": "boolean", "default": false, "description": "Sets whether lossless job restart is enabled for the node. With lossless restart you can restart the whole cluster without losing the jobs and their state. The feature is implemented on top of the Hot Restart feature of Hazelcast which persists the data to disk. If enabled, you have to also configure Hot Restart. Note: the snapshots exported using `Job#exportSnapshot` will also have Hot Restart storage enabled. Feature is disabled by default. If you enable this option in open-source, the member will fail to start, you need Enterprise to run it and obtain a license from Hazelcast."}, "max-processor-accumulated-records": {"type": "integer", "default": 9223372036854775807, "description": "Specifies the maximum number of records that can be accumulated by any single processor instance. Operations like grouping, sorting or joining require certain amount of records to be accumulated before they can proceed. You can set this option to reduce the probability of `OutOfMemoryError`. It applies to each processor instance separately, hence the effective limit of records accumulated by each cluster member is influenced by the vertex's `localParallelism` and the number of jobs in the cluster. Currently, this option limits: number of items sorted by the sort operation, number of distinct keys accumulated by aggregation operations, number of entries in each hash-join lookup table, number of entries in stateful transforms and number of distinct items in distinct operation. The limit does not apply to streaming aggregations."}, "edge-defaults": {"type": "object", "additionalProperties": false, "properties": {"queue-size": {"type": "integer", "default": 1024, "description": "Sets the capacity of processor-to-processor concurrent queues. The value is rounded upwards to the next power of 2. When data needs to travel between two processors on the same cluster member, <PERSON> sends it over a concurrent single-producer, single-consumer (SPSC) queue of fixed capacity. Since there are several processors executing the logic of each vertex, and since the queues are SPSC, there will be `senderParallelism input.txt receiverParallelism` queues representing the edge on each member. The edge capacity should strike a balance between performance and memory usage."}, "packet-size-limit": {"type": "integer", "default": 16384, "description": "For a distributed edge, data is sent to a remote member via Hazelcast network packets. Each packet is dedicated to the data of a single edge, but may contain any number of data items. This setting limits the size of the packet in bytes. Packets should be large enough to drown out any fixed overheads, but small enough to allow good interleaving with other packets. Note that a single item cannot straddle packets, therefore the maximum packet size can exceed the value configured here by the size of a single data item. This setting has no effect on a non-distributed edge. "}, "receive-window-multiplier": {"type": "integer", "default": 3, "description": "Sets the scaling factor used by the adaptive receive window sizing function. For each distributed edge the receiving member regularly sends flow-control (\"ack\") packets to its sender which prevent it from sending too much data and overflowing the buffers. The sender is allowed to send the data one `receive window` further than the last acknowledged byte and the receive window is sized in proportion to the rate of processing at the receiver. Ack packets are sent in regular intervals (InstanceConfig#setFlowControlPeriodMs) and the `receive window multiplier` sets the factor of the linear relationship between the amount of data processed within one such interval and the size of the receive window. To put it another way, let us define an `ackworth` as the amount of data processed between two consecutive ack packets. The receive window multiplier determines the number of ackworths the sender can be ahead of the last acked byte. This setting has no effect on a non-distributed edge."}}}}}, "ConnectionStrategy": {"type": "object", "additionalProperties": false, "properties": {"async-start": {"type": "boolean", "default": false, "description": "If set to true, creating Hazelcast client does not wait for connecting to the cluster."}, "reconnect-mode": {"description": "Defines the reconnection mode. The valid values are:\\n * \"ON\": Enables reconnection in a blocking manner where all the waiting invocations are blocked until a cluster connection is established or failed. This is the default value.\\n * \"OFF\": Disables the reconnection.\\n * \"ASYNC\": Enables reconnection in a non-blocking manner where all the waiting invocations receive a HazelcastClientOfflineException.", "enum": ["ASYNC", "ON", "OFF"], "default": "ON"}, "connection-retry": {"type": "object", "additionalProperties": false, "description": "Defines the configuration used when retrying to connect to the cluster.", "properties": {"initial-backoff-millis": {"type": "integer", "default": 1000, "minimum": 0, "description": "Specifies how long to wait (backoff) after the first failure before retrying in milliseconds."}, "max-backoff-millis": {"type": "integer", "minimum": 0, "default": 30000, "description": "Specifies the upper limit for the backoff in milliseconds."}, "multiplier": {"type": "number", "default": 1.05, "minimum": 1.0, "description": "Factor to multiply the backoff after a failed retry."}, "cluster-connect-timeout-millis": {"type": "integer", "default": -1, "minimum": -1, "description": "Timeout value in milliseconds for the client to give up to connect to the current cluster Depending on FailoverConfig, a client can shutdown or start trying on alternative cluster after reaching the timeout. Its default value is -1. For the default value, the client will not stop trying to connect to the cluster. If the failover client is used, the client will start trying to connect alternative clusters after 120000 ms. For any other value, both the failover and non-failover client will use that as it is."}, "jitter": {"type": "number", "default": 0, "minimum": 0, "maximum": 1, "description": "Specifies by how much to randomize backoffs."}}}}}, "ClientUserCodeDeployment": {"type": "object", "additionalProperties": false, "properties": {"enabled": {"type": "boolean", "default": false, "description": "If set to true, user code deployment is enabled."}, "jarPaths": {"type": "array", "items": {"type": "string"}, "description": "Sequence lists the jar files containing the classes to load."}, "classNames": {"type": "array", "items": {"type": "string"}, "description": "Sequence lists the classes available in the client class path to load."}}}, "ProxyFactories": {"type": "array", "items": {"type": "object", "additionalProperties": false, "properties": {"class-name": {"type": "string"}, "service": {"type": "string"}}, "required": ["class-name", "service"]}}, "LoadBalancer": {"description": "The load balancer can be configured with the \"load-balancer\" mapping. It has a scalar sub-node called \"type\". The valid values for the type of the load balancer are:\\n * \"random\": The member the operations to be sent to is chosen randomly.\\n * \"round-robin\": The member the operations to be sent to is chosen in a round-robin fashion.\\n * \"custom\": The member the operations to be sent to is chosen by provided load balancer implementation. The implementation class name is specified in additional \"class-name\" key. ", "type": "object", "additionalProperties": false, "properties": {"type": {"enum": ["random", "round-robin", "custom"]}, "class-name": {"type": "string"}}, "anyOf": [{"not": {"properties": {"type": {"const": "custom"}}}}, {"required": ["class-name"]}]}, "ClientNearCache": {"type": "object", "additionalProperties": {"type": "object", "additionalProperties": false, "properties": {"time-to-live-seconds": {"$ref": "#/definitions/Map/additionalProperties/properties/near-cache/properties/time-to-live-seconds"}, "max-idle-seconds": {"$ref": "#/definitions/Map/additionalProperties/properties/near-cache/properties/max-idle-seconds"}, "invalidate-on-change": {"$ref": "#/definitions/Map/additionalProperties/properties/near-cache/properties/invalidate-on-change"}, "in-memory-format": {"$ref": "#/definitions/Map/additionalProperties/properties/near-cache/properties/in-memory-format"}, "serialize-keys": {"$ref": "#/definitions/Map/additionalProperties/properties/near-cache/properties/serialize-keys"}, "local-update-policy": {"$ref": "#/definitions/Map/additionalProperties/properties/near-cache/properties/local-update-policy"}, "eviction": {"$ref": "#/definitions/Eviction"}, "preloader": {"type": "object", "additionalProperties": false, "properties": {"enabled": {"type": "boolean", "default": false}, "directory": {"type": "string", "default": ""}, "store-initial-delay-seconds": {"type": "integer", "minimum": 0, "default": 600}, "store-interval-seconds": {"type": "integer", "minimum": 0, "default": 600}}}}}}, "ClientFlakeIdGenerator": {"type": "object", "additionalProperties": {"type": "object", "additionalProperties": false, "properties": {"prefetch-count": {"type": "integer", "minimum": 1, "maximum": 100000, "default": 100, "description": "Sets how many IDs are pre-fetched on the background when one call to FlakeIdGenerator#newId() is made."}, "prefetch-validity-millis": {"type": "integer", "minimum": 1, "default": 600000, "description": "Sets for how long the pre-fetched IDs can be used. If this time elapses, a new batch of IDs will be fetched. Time unit is milliseconds, default is 600,000 (10 minutes)."}}}}, "ClientReliableTopic": {"type": "object", "additionalProperties": {"type": "object", "additionalProperties": false, "properties": {"read-batch-size": {"type": "integer", "default": 10, "minimum": 1, "description": "Sets the read batch size. The ReliableTopic tries to read a batch of messages from the ringbuffer. It will get at least one, but if there are more available, then it will try to get more to increase throughput. The maximum read batch size can be influenced using the read batch size. Apart from influencing the number of messages to retrieve, the readBatchSize also determines how many messages will be processed by the thread running the MessageListener before it returns back to the pool to look for other MessageListeners that need to be processed. The problem with returning to the pool and looking for new work is that interacting with an executor is quite expensive due to contention on the work-queue. The more work that can be done without retuning to the pool, the smaller the overhead. If the readBatchSize is 10 and there are 50 messages available, 10 items are retrieved and processed consecutively before the thread goes back to the pool and helps out with the processing of other messages. If the readBatchSize is 10 and there are 2 items available, 2 items are retrieved and processed consecutively. If the readBatchSize is an issue because a thread will be busy too long with processing a single MessageListener and it can't help out other MessageListeners, increase the size of the threadpool so the other MessageListeners don't need to wait for a thread, but can be processed in parallel."}, "topic-overload-policy": {"default": "BLOCK", "enum": ["BLOCK", "DISCARD_OLDEST", "DISCARD_NEWEST"], "description": "A policy to deal with an overloaded topic; so topic where there is no place to store new messages. This policy can only be used in combination with the com.hazelcast.core.HazelcastInstance#getReliableTopic(String). The reliable topic uses a com.hazelcast.ringbuffer.Ringbuffer to store the messages. A ringbuffer doesn't track where readers are, so it has no concept of a slow consumers. This provides many advantages like high performance reads, but it also gives the ability to the reader to re-read the same message multiple times in case of an error. A ringbuffer has a limited, fixed capacity. A fast producer may overwrite old messages that are still being read by a slow consumer. To prevent this, we may configure a time-to-live on the ringbuffer (see com.hazelcast.config.RingbufferConfig#setTimeToLiveSeconds(int). Once the time-to-live is configured, the TopicOverloadPolicy controls how the publisher is going to deal with the situation that a ringbuffer is full and the oldest item in the ringbuffer is not old enough to get overwritten. Keep in mind that this retention period (time-to-live) can keep messages from being overwritten, even though all readers might have already completed reading. Its default value is BLOCK. Available values are as follows: - DISCARD_OLDEST: Using this policy, a message that has not expired can be overwritten. No matter the retention period set, the overwrite will just overwrite the item. This can be a problem for slow consumers because they were promised a certain time window to process messages. But it will benefit producers and fast consumers since they are able to continue. This policy sacrifices the slow producer in favor of fast producers/consumers.\\n * DISCARD_NEWEST: Message that was to be published is discarded.\\n * BLOCK: The caller will wait until there is space in the Ringbuffer.\\n * ERROR: The publish call fails immediately."}}}}, "ClientSecurity": {"type": "object", "additionalProperties": false, "propertyNames": {"enum": ["realms", "username-password", "kerber<PERSON>", "token", "credentials-factory"]}, "properties": {"username-password": {"$ref": "#/definitions/UsernamePasswordIdentity"}, "token": {"$ref": "#/definitions/TokenIdentity"}, "credentials-factory": {"$ref": "#/definitions/CredentialsFactoryIdentity"}, "kerberos": {"$ref": "#/definitions/KerberosIdentity"}, "realms": {"$ref": "#/definitions/Security/properties/realms"}}}, "UsernamePasswordIdentity": {"type": "object", "additionalProperties": false, "description": "Defines a static UsernamePasswordCredentials instance as the member's identity. It has mandatory \"username\" and \"password\" attributes.", "properties": {"username": {"type": "string"}, "password": {"type": "string"}}, "required": ["username", "password"]}, "KerberosIdentity": {"type": "object", "additionalProperties": false, "properties": {"realm": {"type": "string"}, "security-realm": {"type": "string"}, "principal": {"type": "string"}, "keytab-file": {"type": "string"}, "service-name-prefix": {"type": "string"}, "spn": {"type": "string"}, "use-canonical-hostname": {"type": "boolean", "default": false}}}, "TokenIdentity": {"type": "object", "additionalProperties": false, "description": "Defines a static TokenCredentials instance as the client's identity. It has a \"value\" and it can have also \"encoding\" defined.", "properties": {"encoding": {"enum": ["none", "base64"]}, "value": {"type": "string"}}, "required": ["encoding", "value"]}, "CredentialsFactoryIdentity": {"description": " Specifies the name and properties of your class that you developed by implementing Hazelcast's Credentials interface. This element has a mandatory \"class-name\" attribute where you should define the factory class implementing ICredentialsFactory used to create Credentials objects. With the \"properties\" child property, you can define properties for the factory class.", "$ref": "#/definitions/ClassNameWithProps"}, "ClientNetwork": {"type": "object", "additionalProperties": false, "properties": {"cluster-members": {"description": "This sequence lets you list the members in your cluster the client is connecting to.", "type": "array", "items": {"type": "string"}}, "outbound-ports": {"$ref": "#/definitions/OutboundPorts"}, "smart-routing": {"type": "boolean", "default": true, "description": "If true, client will route the key based operations to owner of the key at the best effort. Note that it uses a cached version of com.hazelcast.core.PartitionService#getPartitions() and doesn't guarantee that the operation will always be executed on the owner. The cached table is updated every second."}, "redo-operation": {"type": "boolean", "default": false, "description": "If true, client will redo the operations that were executing on the server and client lost the connection. This can be because of network, or simply because the member died. However it is not clear whether the application is performed or not. For idempotent operations this is harmless, but for non idempotent ones retrying can cause to undesirable effects. Note that the redo can perform on any member. If false, the operation will throw a RuntimeException that is wrapping a java.io.IOException."}, "socket-interceptor": {"$ref": "#/definitions/SocketInterceptor"}, "connection-timeout": {"type": "integer", "description": "Connection timeout is the timeout value in milliseconds for members to accept client connection requests. The following are the example configurations. Its default value is 5000.", "default": 5000, "minimum": 0}, "icmp-ping": {"type": "object", "additionalProperties": false, "properties": {"enabled": {"type": "boolean", "default": false, "description": "Flag to enable icmp ping failure detector. When set to true, this client will use icmp ping failure detector to detect unavailable members."}, "interval-milliseconds": {"type": "integer", "default": 1000, "minimum": 1000, "description": "Interval, in milliseconds, between each ping attempt. 1000ms (1 sec) is also the minimum interval allowed."}, "timeout-milliseconds": {"type": "integer", "default": 1000, "description": "Number of milliseconds until a ping attempt is considered failed if there was no reply."}, "max-attempts": {"type": "integer", "description": "Maximum number of ping attempts before the member gets suspected by the detector.", "default": 3}, "ttl": {"description": "Maximum number of hops the packets should go through. You can set to 0 to use your system���s default TTL.", "type": "integer", "default": 255, "minimum": 0}, "echo-fail-fast-on-startup": {"description": "The client will fail to start if it is unable to action an ICMP ping command when ICMP is enabled. Failure is usually due to OS level restrictions.", "type": "boolean", "default": true}}}, "auto-detection": {"$ref": "#/definitions/AutoDetection"}, "azure": {"$ref": "#/definitions/AzureDiscovery"}, "gcp": {"$ref": "#/definitions/GcpDiscovery"}, "aws": {"$ref": "#/definitions/AwsDiscovery"}, "kubernetes": {"$ref": "#/definitions/KubernetesDiscovery"}, "ssl": {"$ref": "#/definitions/SSL"}, "eureka": {"$ref": "#/definitions/EurekaDiscovery"}, "discovery-strategies": {"$ref": "#/definitions/DiscoveryStrategies"}, "hazelcast-cloud": {"type": "object", "additionalProperties": false, "description": "Set its \"enabled\" sub-element to true for cluster discovery in the Hazelcast Cloud infrastructure. You need to define the mandatory \"discovery-token\" sub-node used by the discovery mechanism.", "properties": {"enabled": {"type": "boolean", "default": false}, "discovery-token": {"type": "string"}}}, "socket-options": {"$ref": "#/definitions/ClientSocketOptions"}}}, "SocketInterceptor": {"type": "object", "additionalProperties": false, "properties": {"enabled": {"type": "boolean", "default": false}, "class-name": {"type": "string"}, "properties": {"type": "object"}}, "description": "Lets you add custom hooks to join and perform connection procedures (like a custom authentication protocol, etc.). This feature is available only in Hazelcast Enterprise. To be able to use it, you should first implement the MemberSocketInterceptor (for members joining to a cluster) or SocketInterceptor (for clients connecting to a member) class. Its configuration contains the class you implemented and socket interceptor properties. By default, it is disabled."}, "AutoDetection": {"type": "object", "additionalProperties": false, "properties": {"enabled": {"type": "boolean", "default": "true"}}, "description": "When enabled, it will walk through all available discovery strategies and detect the correct one for the current runtime environment. For example, it will automatically use the hazelcast-aws plugin if run on an AWS instance."}, "SSL": {"type": "object", "additionalProperties": false, "description": "Lets you configure SSL using the SSL context factory. This feature is available only in Hazelcast Enterprise. To be able to use it, encryption should NOT be enabled and you should first implement your SSLContextFactory class. Its configuration contains the factory class and SSL properties. By default, it is disabled.", "properties": {"enabled": {"type": "boolean", "default": false}, "factory-class-name": {"type": "string", "example": "com.hazelcast.nio.ssl.BasicSSLContextFactory"}, "properties": {"type": "object"}}}, "ClientSocketOptions": {"type": "object", "additionalProperties": false, "properties": {"tcp-no-delay": {"description": "Enables/disables the TCP_NODELAY socket option.", "default": true, "type": "boolean"}, "keep-alive": {"type": "boolean", "default": true, "description": "Enables/disables the SO_KEEPALIVE socket option."}, "reuse-address": {"type": "boolean", "default": true, "description": "Enables/disables the SO_REUSEADDR socket option."}, "linger-seconds": {"type": "integer", "default": 3, "minimum": 0, "description": "Enables/disables SO_LINGER with the specified linger time in seconds."}, "buffer-size": {"type": "integer", "default": 128, "minimum": 0, "description": "Sets the SO_SNDBUF and SO_RCVBUF options to the specified value in KB for this Socket."}}}, "IntegrityChecker": {"type": "object", "additionalProperties": false, "properties": {"enabled": {"type": "boolean"}}, "required": ["enabled"], "description": "Enables/disables Integrity Checker. Integrity Checker can be disabled to improve startup time."}, "ExternalDataStoreConfig": {"type": "object", "additionalProperties": {"type": "object", "additionalProperties": false, "properties": {"name": {"type": "string", "description": "Name of the external data store configuration."}, "class-name": {"type": "string", "description": "The name of the class implementing ExternalDataStoreFactory."}, "shared": {"type": "boolean", "description": "If true reuse an instance of JDBC pool or any other storage driver (e.g. kafka client) between jobs or instances of MapStore."}, "properties": {"description": "The \"properties\" used for configuring external data store", "type": "object"}}}}}}