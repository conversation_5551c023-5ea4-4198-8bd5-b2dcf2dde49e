<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (c) 2008-2022, Hazelcast, Inc. All Rights Reserved.
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~ http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns="http://www.hazelcast.com/schema/client-config"
           elementFormDefault="qualified"
           targetNamespace="http://www.hazelcast.com/schema/client-config">

    <xs:element name="hazelcast-client-failover">
        <xs:complexType>
            <xs:choice maxOccurs="unbounded">
                <xs:element name="try-count" type="xs:int"/>
                <xs:element name="clients" type="clients" minOccurs="0"/>
            </xs:choice>
        </xs:complexType>
    </xs:element>

    <xs:complexType name="clients">
        <xs:sequence>
            <xs:element name="client" type="client" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="client">
        <xs:annotation>
            <xs:documentation>Path of the client xml
            </xs:documentation>
        </xs:annotation>
        <xs:simpleContent>
            <xs:extension base="non-space-string"/>
        </xs:simpleContent>
    </xs:complexType>


    <xs:simpleType name="non-space-string">
        <xs:restriction base="xs:string">
            <xs:pattern value="\S.*"/>
        </xs:restriction>
    </xs:simpleType>


</xs:schema>
