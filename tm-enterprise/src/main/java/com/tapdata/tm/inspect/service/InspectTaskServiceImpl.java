package com.tapdata.tm.inspect.service;

import cn.hutool.core.collection.CollUtil;
import com.tapdata.tm.base.dto.Filter;
import com.tapdata.tm.base.dto.Where;
import com.tapdata.tm.base.exception.BizException;
import com.tapdata.tm.base.service.BaseService;
import com.tapdata.tm.commons.schema.DataSourceConnectionDto;
import com.tapdata.tm.commons.task.dto.TaskDto;
import com.tapdata.tm.commons.util.JsonUtil;
import com.tapdata.tm.config.security.UserDetail;
import com.tapdata.tm.ds.service.impl.DataSourceService;
import com.tapdata.tm.inspect.bean.Stats;
import com.tapdata.tm.inspect.bean.Task;
import com.tapdata.tm.inspect.constant.InspectResultEnum;
import com.tapdata.tm.inspect.constant.InspectStatusEnum;
import com.tapdata.tm.inspect.dto.InspectDto;
import com.tapdata.tm.inspect.dto.InspectResultDto;
import com.tapdata.tm.inspect.entity.InspectEntity;
import com.tapdata.tm.inspect.repository.InspectRepository;
import com.tapdata.tm.message.constant.Level;
import com.tapdata.tm.message.constant.MsgTypeEnum;
import com.tapdata.tm.message.service.MessageService;
import com.tapdata.tm.messagequeue.dto.MessageQueueDto;
import com.tapdata.tm.messagequeue.service.MessageQueueService;
import com.tapdata.tm.permissions.DataPermissionHelper;
import com.tapdata.tm.permissions.constants.DataPermissionActionEnums;
import com.tapdata.tm.permissions.constants.DataPermissionMenuEnums;
import com.tapdata.tm.task.service.TaskService;
import com.tapdata.tm.userLog.constant.Modular;
import com.tapdata.tm.userLog.constant.Operation;
import com.tapdata.tm.userLog.service.UserLogService;
import com.tapdata.tm.utils.Lists;
import com.tapdata.tm.utils.MongoUtils;
import com.tapdata.tm.worker.service.WorkerService;
import io.tapdata.entity.simplify.TapSimplify;
import io.tapdata.utils.ErrorCodeUtils;
import lombok.NonNull;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Slf4j
@Primary
@Setter(onMethod_ = {@Autowired})
public class InspectTaskServiceImpl extends BaseService<InspectDto, InspectEntity, ObjectId, InspectRepository> implements InspectTaskService {
    protected static final String STATUS = "status";
    private MessageService messageService;
    private UserLogService userLogService;
    private InspectDetailsService inspectDetailsService;
    private InspectResultService inspectResultService;
    private MessageQueueService messageQueueService;
    private WorkerService workerService;
    private TaskService taskService;
    private DataSourceService dataSourceService;

    public InspectTaskServiceImpl(@NonNull InspectRepository repository) {
        super(repository, InspectDto.class, InspectEntity.class);
    }

    @Override
    protected void beforeSave(InspectDto dto, UserDetail userDetail) {
        // do nothing
    }

    @Override
    public InspectDto inspectTaskRun(Where where, InspectDto updateDto, UserDetail user) {
        log.info("Inspect task execute running: {}", updateDto);
        InspectDto retDto = executeInspect(where, updateDto, user);
        userLogService.addUserLog(Modular.INSPECT, Operation.START, user.getUserId(), retDto.getId().toString(), retDto.getName());
        return retDto;
    }

    @Override
    public InspectDto inspectTaskStop(String id, InspectDto updateDto, UserDetail user) {
        log.info("Inspect task execute stopping: {}", updateDto);
        InspectDto dto = findById(MongoUtils.toObjectId(id));
        stopInspectTask(dto);
        userLogService.addUserLog(Modular.INSPECT, Operation.STOP, user.getUserId(), dto.getId().toHexString(), dto.getName());
        return updateDto;
    }

    @Override
    public InspectDto inspectTaskError(String id, InspectDto updateDto, UserDetail user) {
        String name = updateDto.getName();
        log.info("Inspect task execute error: {}", updateDto);
        messageService.addInspect(name, id, MsgTypeEnum.INSPECT_ERROR, Level.ERROR, user);
        return updateDto;
    }

    @Override
    public InspectDto inspectTaskDone(String id, InspectDto updateDto, UserDetail user) {
        String result = updateDto.getResult();
        String name = updateDto.getName();
        log.info("Inspect task execute complete: {}", updateDto);
        if (InspectResultEnum.FAILED.getValue().equals(result)) {
            messageService.addInspect(name, id, MsgTypeEnum.INSPECT_VALUE, Level.ERROR, user);
        }
        return updateDto;
    }

    @Override
    public InspectDto executeInspect(Where where, InspectDto updateDto, UserDetail user) {
        String id = (String) where.get("id");
        InspectDto inspectDto = null;
        ObjectId objectId = MongoUtils.toObjectId(id);
        inspectDto = findById(objectId, user);
        if (null == inspectDto) {
            throw new BizException("Inspect.Start.Failed", "Inspect task not exists: " + id);
        }

        InspectStatusEnum inspectStatus = InspectStatusEnum.of(inspectDto.getStatus());
        if (inspectStatus == InspectStatusEnum.RUNNING) {
            throw new BizException("Inspect.Start.Failed", "Running tasks cannot be restarted");
        }

        inspectDto.setStatus(InspectStatusEnum.SCHEDULING.getValue());

        if (StringUtils.isNotEmpty(updateDto.getByFirstCheckId())) {
            //It's a secondary verification
            inspectDto.setByFirstCheckId(updateDto.getByFirstCheckId());
        } else {
            //It not a secondary verification
            inspectDto.setByFirstCheckId("");
        }
        inspectDto.setAgentId(null);
        // Clear and reassign agents before execution
        workerService.scheduleTaskToEngine(inspectDto, user);
        Date now = new Date();
        inspectDto.setPing_time(now.getTime());
        inspectDto.setLastStartTime(now.getTime());

        if (StringUtils.isEmpty(inspectDto.getAgentId())) {
            throw new BizException("Inspect.ProcessId.NotFound");
        }
        String agentId = inspectDto.getAgentId();
        where.put("id", objectId);
        updateByWhere(where, inspectDto, user);

        String inspectResultId = updateDto.getInspectResultId();
        inspectDto.setInspectResultId(inspectResultId);
        inspectDto.setTaskIds(updateDto.getTaskIds());

        if (StringUtils.isNotEmpty(inspectResultId)) {
            // Re perform inspect task
            List<String> taskIds = inspectDto.getTaskIds();
            if (CollUtil.isEmpty(taskIds)) {
                InspectResultDto inspectResult = inspectResultService.findById(MongoUtils.toObjectId(inspectResultId), user);
                if (Objects.nonNull(inspectResult) && CollUtil.isNotEmpty(inspectResult.getStats())) {
                    taskIds = inspectResult.getStats().stream()
                            .filter(Objects::nonNull)
                            .filter(stats -> "failed".equals(stats.getResult()))
                            .map(Stats::getTaskId)
                            .collect(Collectors.toList());
                } else {
                    taskIds = new ArrayList<>();
                }
            }
            List<String> finalTaskIds = taskIds;
            List<Task> tasks = Optional.ofNullable(inspectDto.getTasks()).orElse(new ArrayList<>());
            List<Task> newTasks = tasks.stream()
                    .filter(Objects::nonNull)
                    .filter(task -> finalTaskIds.contains(task.getTaskId()))
                    .collect(Collectors.toList());
            inspectDto.setTasks(newTasks);

            inspectDetailsService.deleteAll(Query.query(
                    Criteria.where("inspect_id").is(id)
                            .and("taskId").in(finalTaskIds)
                            .and("inspectResultId").is(inspectResultId)), user);
            log.info("Remove inspect details before restart inspect task (inspectId={}, inspectResultId={}, taskId=[{}])",
                    id, inspectResultId, String.join(",", finalTaskIds));
        }

        startInspectTask(inspectDto, agentId);
        return inspectDto;
    }

    /**
     * Start data verification, click the verification button, update with an error message,
     * and it will be called when adding new data
     *
     * @param inspectDto
     * @param processId
     * @return
     */
    @Override
    public String startInspectTask(InspectDto inspectDto, String processId) {
        try {
            String json = JsonUtil.toJsonUseJackson(inspectDto);
            Map<String, Object> data = Optional.ofNullable(JsonUtil.parseJson(json, Map.class)).orElse(new HashMap<>());
            data.put("type", "data_inspect");
            data.remove("timing");

            //Data needs to be stored in the data that needs to be verified
            MessageQueueDto messageQueueDto = new MessageQueueDto();
            messageQueueDto.setReceiver(processId);
            messageQueueDto.setSender("");
            messageQueueDto.setData(data);
            messageQueueDto.setType("pipe");

            log.info("Send websocket and do execute data inspect task, processId = {}, name {}, inspectId {}",
                    processId,
                    inspectDto.getName(),
                    inspectDto.getId()
            );
            messageQueueService.sendMessage(messageQueueDto);
        } catch (Exception e) {
            inspectDto.setStatus(InspectStatusEnum.ERROR.getValue());
            inspectDto.setErrorMsg(ErrorCodeUtils.truncateData(TapSimplify.getStackTrace(e)));
            upsert(new Query(Criteria.where("_id").is(inspectDto.getId())), inspectDto);
            log.error("Start websocket failed", e);
        }
        return processId;
    }

    @Override
    public void stopInspectTask(InspectDto inspectDto) {
        try {
            String processId = inspectDto.getAgentId();
            if (StringUtils.isBlank(processId)) {
                log.error("Stop inspect '{}' failed, agentId can not be empty", inspectDto.getId().toHexString());
                return;
            }

            Map<String, Object> data = new HashMap<>();
            data.put("id", inspectDto.getId().toHexString());
            data.put("type", "data_inspect");
            data.put(STATUS, InspectStatusEnum.STOPPING.getValue());
            //data里面要放需要校验的数据
            MessageQueueDto messageQueueDto = new MessageQueueDto();
            messageQueueDto.setReceiver(processId);
            messageQueueDto.setSender("");
            messageQueueDto.setData(data);
            messageQueueDto.setType("pipe");

            log.info("Send stop inspect websocket message success, processId = {}, name {}, inspectId {}",
                    processId,
                    inspectDto.getName(),
                    inspectDto.getId()
            );
            messageQueueService.sendMessage(messageQueueDto);
        } catch (Exception e) {
            log.error("Stop inspect failed: {}", e.getMessage(), e);
        }
    }

    @Override
    public List<TaskDto> findTaskList(UserDetail user) {
        List<TaskDto> taskDtoList = new ArrayList<>();
        findTask(taskDtoList, TaskDto.SYNC_TYPE_MIGRATE, user);
        findTask(taskDtoList, TaskDto.SYNC_TYPE_SYNC, user);
        return taskDtoList;
    }

    protected void findTask(List<TaskDto> taskDtoList, String taskType, UserDetail user) {
        Criteria criteria = new Criteria();
        Query query = Query.query(criteria);
        criteria.and("syncType").is(taskType)
                .and(STATUS).in(Lists.newArrayList("running", "stop", "complete"));
        query.with(Sort.by(Sort.Order.desc("createTime")));
        query.fields().include("_id", "name", STATUS);
        DataPermissionMenuEnums dataPermissionMenuEnums = DataPermissionMenuEnums.ofTaskSyncType(taskType);
        if (null == dataPermissionMenuEnums) {
            return;
        }
        List<TaskDto> taskOfMigrate = DataPermissionHelper.check(
                user,
                dataPermissionMenuEnums,
                DataPermissionActionEnums.View,
                dataPermissionMenuEnums.getDataType(),
                null,
                () -> {
                    if (!user.isRoot() && !DataPermissionHelper.setFilterConditions(true, query, user)) {
                        criteria.and("user_id").is(user.getUserId());
                    }
                    return taskService.findAll(query);
                },
                ArrayList::new);
        Optional.ofNullable(taskOfMigrate).ifPresent(taskDtoList::addAll);
    }

    @Override
    public List<DataSourceConnectionDto> findConnectionList(UserDetail userDetail) {
        List<DataSourceConnectionDto> connectionDto = DataPermissionMenuEnums.Connections.checkAndSetFilter(
                userDetail,
                DataPermissionActionEnums.View,
                () -> dataSourceService.findAll(new Filter())
        );
        return Optional.ofNullable(connectionDto).orElse(new ArrayList<>());
    }
}
